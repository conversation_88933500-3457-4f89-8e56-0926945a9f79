package core

import (
	"Lightx/internal/common"
	"Lightx/pkg/clients"
	"Lightx/pkg/netutil"
	"Lightx/pkg/portscan"
	"Lightx/plugins/core"
	"fmt"
	"strconv"
	"strings"

	"github.com/projectdiscovery/gologger"
)

// 用于记录是否已输出爬虫模式信息
var crawlerModeInfoPrinted bool = false

// ProcessIPWithPort 处理IP:端口格式的目标（支持IPv4和IPv6）
func ProcessIPWithPort(options *common.Options) {
	var ip string
	var port int

	// 处理IPv6格式：[2001:db8::1]:8080 或 [fe80::1%eth0]:8080
	if strings.HasPrefix(options.Target, "[") && strings.Contains(options.Target, "]:") {
		parts := strings.Split(options.Target, "]:")
		ip = strings.TrimPrefix(parts[0], "[")
		// 清理IPv6地址，移除接口标识符
		ip = netutil.CleanIPv6(ip)
		port, _ = strconv.Atoi(parts[1])
	} else {
		// 处理IPv4格式：***********:8080
		parts := strings.Split(options.Target, ":")
		ip = parts[0]
		port, _ = strconv.Atoi(parts[1])
	}

	if netutil.IsIPv4(ip) {
		gologger.Info().Msgf("检测到IPv4:端口: %s:%d", ip, port)
	} else if netutil.IsIPv6(ip) {
		gologger.Info().Msgf("检测到IPv6:端口: [%s]:%d", ip, port)
	}

	// 执行连接检测
	result := portscan.Connect(ip, port, options.Timeout, clients.Proxy{})
	if !result.Status {
		gologger.Warning().Msgf("端口未开放: %s:%d", ip, port)
		return
	}

	// 输出端口服务信息
	gologger.Info().Msgf("[Port] %s:%s:%d", result.Server, ip, port)

	// 如果是Web服务，执行Web扫描
	if (result.Server == "http" || result.Server == "https" || port == 80 || port == 443 || port == 8080 || port == 8000) && options.WebScan {
		var targets []string

		// 根据IP版本构建URL格式
		var ipForURL string
		if netutil.IsIPv6(ip) {
			ipForURL = fmt.Sprintf("[%s]", ip) // IPv6需要用方括号包围
		} else {
			ipForURL = ip
		}

		// 根据服务类型构建URL
		if result.Server == "http" || result.Server == "https" {
			target := fmt.Sprintf("%s://%s:%d", result.Server, ipForURL, port)
			targets = append(targets, target)
		} else if port == 80 || port == 8080 || port == 8000 {
			target := fmt.Sprintf("http://%s:%d", ipForURL, port)
			targets = append(targets, target)
		} else if port == 443 {
			target := fmt.Sprintf("https://%s:%d", ipForURL, port)
			targets = append(targets, target)
		}

		// 执行Web扫描
		RunWebScan(targets, options)
	} else {
		gologger.Info().Msg("Web扫描已禁用，跳过Web扫描阶段")
	}

	// 执行插件系统处理端口扫描结果，如果未禁用插件系统
	if !options.NoPlugin {
		core.DispatchPluginTasks([]portscan.PortResult{result})
	} else {
		gologger.Info().Msgf("插件系统已禁用，跳过插件任务执行")
	}
}

// ProcessIP 处理单个IP地址（支持IPv4和IPv6）
func ProcessIP(options *common.Options) {
	// 清理IPv6地址，移除接口标识符
	cleanIP := netutil.CleanIPv6(options.Target)

	ipVersion := netutil.GetIPVersion(cleanIP)
	if ipVersion == 4 {
		gologger.Info().Msgf("检测到IPv4地址: %s", cleanIP)
	} else if ipVersion == 6 {
		gologger.Info().Msgf("检测到IPv6地址: %s", cleanIP)
		if cleanIP != options.Target {
			gologger.Info().Msgf("已清理接口标识符，原地址: %s", options.Target)
		}
	} else {
		gologger.Error().Msgf("无效的IP地址: %s", options.Target)
		return
	}

	// 单个IP不需要存活探测，使用清理后的IP
	ips := []string{cleanIP}

	// 执行端口扫描
	gologger.Info().Msg("开始端口扫描...")
	results := portscan.ScanAndOutputResult(ips, options.PortRange, options.ExcludePort, options.TCPThreads, options.TCPTimeout, options.Proxy, options.Log, options.RandomPort, options.PortsThreshold, options.Html, options.ServiceCheck)

	if len(results) == 0 {
		gologger.Warning().Msg("未发现开放端口")
		return
	}

	// 如果不进行Web扫描，直接处理插件任务
	if !options.WebScan {
		gologger.Info().Msg("Web扫描已禁用，跳过Web扫描阶段")

		// 执行插件系统处理端口扫描结果，如果未禁用插件系统
		if !options.NoPlugin {
			core.DispatchPluginTasks(results)
		} else {
			gologger.Info().Msgf("插件系统已禁用，跳过插件任务执行")
		}
		return
	}

	// 收集Web服务目标
	var webTargets []string

	// 遍历扫描结果
	for _, result := range results {
		// 根据IP版本构建URL格式
		var ipForURL string
		if netutil.IsIPv6(result.IP) {
			ipForURL = fmt.Sprintf("[%s]", result.IP) // IPv6需要用方括号包围
		} else {
			ipForURL = result.IP
		}

		// 收集HTTP/HTTPS服务
		if result.Server == "http" || result.Server == "https" {
			target := fmt.Sprintf("%s://%s:%d", result.Server, ipForURL, result.Port)
			webTargets = append(webTargets, target)
			continue
		}

		// 对于常见的Web端口，即使未识别为HTTP，也添加为HTTP目标
		if (result.Port == 80 || result.Port == 8080 || result.Port == 8088 || result.Port == 8000) && result.Server != "http" && result.Server != "https" {
			target := fmt.Sprintf("http://%s:%d", ipForURL, result.Port)
			webTargets = append(webTargets, target)
		}

		if result.Port == 443 && result.Server != "http" && result.Server != "https" {
			target := fmt.Sprintf("https://%s:%d", ipForURL, result.Port)
			webTargets = append(webTargets, target)
		}
	}

	if len(webTargets) > 0 {
		// 分别解析每个URL
		var targets []string
		for _, webTarget := range webTargets {
			parsedURLs := clients.ParseURL(webTarget)
			targets = append(targets, parsedURLs...)
		}

		gologger.Info().Msgf("已解析 %d 个Web目标，准备进行Web扫描", len(targets))

		// 清除可能残留的端口扫描进度显示
		portscan.ClearProgress()

		// 执行Web扫描
		RunWebScan(targets, options)
	} else {
		gologger.Warning().Msg("未发现Web服务目标")
	}

	// Web扫描完成后，执行插件系统处理端口扫描结果，如果未禁用插件系统
	if !options.NoPlugin {
		core.DispatchPluginTasks(results)
	} else {
		gologger.Info().Msgf("插件系统已禁用，跳过插件任务执行")
	}
}

// ProcessIPRange 处理IP段（支持IPv4和IPv6）
func ProcessIPRange(options *common.Options) {
	// 检测IP段类型
	if netutil.IsIPv4Range(options.Target) {
		gologger.Info().Msgf("检测到IPv4段: %s", options.Target)
	} else if netutil.IsIPv6Range(options.Target) {
		gologger.Info().Msgf("检测到IPv6段: %s", options.Target)
	} else {
		gologger.Error().Msgf("无效的IP段格式: %s", options.Target)
		return
	}

	// 解析IP段
	ipList, err := netutil.ParseIPRange(options.Target)
	if err != nil {
		gologger.Error().Msgf("解析IP段失败: %s", err)
		return
	}

	gologger.Info().Msgf("IP段包含 %d 个IP地址", len(ipList))

	var aliveIPs []string

	// 根据UsePing参数决定是否进行存活探测
	if options.UsePing {
		// 如果UsePing为true，跳过存活探测，直接使用所有IP
		gologger.Info().Msgf("已禁用存活探测，将对所有 %d 个IP进行端口扫描", len(ipList))
		aliveIPs = ipList
	} else {
		// 执行IP存活探测
		gologger.Info().Msgf("开始对 %d 个IP进行存活探测", len(ipList))
		aliveIPs = portscan.CheckLive(ipList, false, options.Log)
		gologger.Info().Msgf("IP存活探测完成，发现 %d 个存活IP", len(aliveIPs))
	}

	if len(aliveIPs) == 0 {
		gologger.Warning().Msg("未发现存活IP")
		return
	}

	// 执行端口扫描
	results := portscan.ScanAndOutputResult(aliveIPs, options.PortRange, options.ExcludePort, options.TCPThreads, options.TCPTimeout, options.Proxy, options.Log, options.RandomPort, options.PortsThreshold, options.Html, options.ServiceCheck)

	if len(results) == 0 {
		gologger.Warning().Msg("未发现开放端口")
		return
	}

	// 如果不进行Web扫描，直接处理插件任务
	if !options.WebScan {
		gologger.Info().Msg("Web扫描已禁用，跳过Web扫描阶段")

		// 执行插件系统处理端口扫描结果，如果未禁用插件系统
		if !options.NoPlugin {
			core.DispatchPluginTasks(results)
		} else {
			gologger.Info().Msgf("插件系统已禁用，跳过插件任务执行")
		}
		return
	}

	// 收集Web服务目标
	var webTargets []string

	// 遍历扫描结果
	for _, result := range results {
		// 根据IP版本构建URL格式
		var ipForURL string
		if netutil.IsIPv6(result.IP) {
			ipForURL = fmt.Sprintf("[%s]", result.IP) // IPv6需要用方括号包围
		} else {
			ipForURL = result.IP
		}

		// 收集HTTP/HTTPS服务
		if result.Server == "http" || result.Server == "https" {
			target := fmt.Sprintf("%s://%s:%d", result.Server, ipForURL, result.Port)
			webTargets = append(webTargets, target)
			continue
		}

		// 对于常见的Web端口，即使未识别为HTTP，也添加为HTTP目标
		if (result.Port == 80 || result.Port == 8080 || result.Port == 8088 || result.Port == 8000) && result.Server != "http" && result.Server != "https" {
			target := fmt.Sprintf("http://%s:%d", ipForURL, result.Port)
			webTargets = append(webTargets, target)
		}

		if result.Port == 443 && result.Server != "http" && result.Server != "https" {
			target := fmt.Sprintf("https://%s:%d", ipForURL, result.Port)
			webTargets = append(webTargets, target)
		}
	}

	if len(webTargets) > 0 {
		// 分别解析每个URL
		var targets []string
		for _, webTarget := range webTargets {
			parsedURLs := clients.ParseURL(webTarget)
			targets = append(targets, parsedURLs...)
		}

		gologger.Info().Msgf("已解析 %d 个Web目标，准备进行Web扫描", len(targets))

		// 清除可能残留的端口扫描进度显示
		portscan.ClearProgress()

		// 执行Web扫描
		RunWebScan(targets, options)
	} else {
		gologger.Warning().Msg("未发现Web服务目标")
	}

	// Web扫描完成后，执行插件系统处理端口扫描结果，如果未禁用插件系统
	if !options.NoPlugin {
		core.DispatchPluginTasks(results)
	} else {
		gologger.Info().Msgf("插件系统已禁用，跳过插件任务执行")
	}
}

// ProcessDomain 处理域名（增强版，支持完整端口扫描）
func ProcessDomain(options *common.Options) {
	gologger.Info().Msgf("检测到域名: %s", options.Target)

	// // 提供两种扫描模式选择
	// gologger.Info().Msg("域名扫描模式:")
	// gologger.Info().Msg("  1. 直接域名扫描 - 直接对域名进行端口扫描")
	// gologger.Info().Msg("  2. 解析IP扫描 - 先解析域名为IP再扫描")

	// 默认使用直接域名扫描，除非明确指定解析IP
	if options.ResolveAll {
		// 使用解析IP模式
		ProcessDomainWithIPResolution(options)
	} else {
		// 使用直接域名扫描模式
		ProcessDomainDirect(options)
	}
}

// ProcessDomainDirect 直接对域名进行端口扫描
func ProcessDomainDirect(options *common.Options) {
	gologger.Info().Msgf("使用直接域名扫描模式: %s", options.Target)

	// 执行CDN检测和域名分析
	domainInfo, err := netutil.ResolveDomain(options.Target)
	if err != nil {
		gologger.Warning().Msgf("域名解析失败，继续直接扫描: %s", err)
	} else {
		// 显示解析到的IP地址
		if len(domainInfo.IPv4Addrs) > 0 {
			gologger.Info().Msgf("IPv4地址 (%d个): %s", len(domainInfo.IPv4Addrs), strings.Join(domainInfo.IPv4Addrs, ", "))
		}
		if len(domainInfo.IPv6Addrs) > 0 {
			gologger.Info().Msgf("IPv6地址 (%d个): %s", len(domainInfo.IPv6Addrs), strings.Join(domainInfo.IPv6Addrs, ", "))
		}

		// 简化的状态报告
		var statusParts []string

		// IP总数信息
		totalIPs := len(domainInfo.IPv4Addrs) + len(domainInfo.IPv6Addrs)
		if totalIPs > 0 {
			statusParts = append(statusParts, fmt.Sprintf("IP: %d个", totalIPs))
		}

		// CDN检测结果和处理
		if domainInfo.CDNInfo != nil && domainInfo.CDNInfo.IsCDN {
			if options.SkipCDN {
				gologger.Warning().Msgf("检测到CDN保护，跳过扫描: %s (提供商: %s)",
					options.Target, domainInfo.CDNInfo.CDNProvider)
				return
			}
			statusParts = append(statusParts, fmt.Sprintf("CDN: %s", domainInfo.CDNInfo.CDNProvider))

			// 如果检测到CDN且用户没有指定特定端口范围，使用CDN专用Web端口
			if options.PortRange == "default" || options.PortRange == "" {
				options.PortRange = "cdn-web"
				gologger.Info().Msgf("检测到CDN，使用CDN专用Web端口进行扫描")
			}
		} else {
			statusParts = append(statusParts, "CDN: 否")
		}

		// 泛解析检测
		if domainInfo.IsWildcard {
			statusParts = append(statusParts, "泛解析: 是")
			gologger.Warning().Msgf("检测到泛解析，扫描结果可能不准确")
		}

		// 真实IP信息
		if len(domainInfo.RealIPs) > 0 {
			statusParts = append(statusParts, fmt.Sprintf("真实IP: %d个", len(domainInfo.RealIPs)))
			// 显示真实IP地址
			gologger.Info().Msgf("发现真实IP: %s", strings.Join(domainInfo.RealIPs, ", "))
		}

		// 输出简化的状态信息
		if len(statusParts) > 0 {
			gologger.Info().Msgf("域名分析: %s", strings.Join(statusParts, " | "))
		}
	}

	// 如果只进行Web扫描，直接处理Web目标
	if options.WebScan && len(options.PortRange) == 0 {
		targets := []string{"http://" + options.Target, "https://" + options.Target}
		gologger.Info().Msg("执行域名Web扫描")
		RunWebScan(targets, options)
		return
	}

	// 执行域名端口扫描
	gologger.Info().Msgf("开始对域名 %s 进行端口扫描", options.Target)

	// 直接使用域名作为目标进行扫描
	targets := []string{options.Target}
	results := portscan.ScanAndOutputResult(targets, options.PortRange, options.ExcludePort, options.TCPThreads, options.TCPTimeout, options.Proxy, options.Log, options.RandomPort, options.PortsThreshold, options.Html, options.ServiceCheck)

	if len(results) == 0 {
		gologger.Warning().Msgf("域名 %s 未发现开放端口", options.Target)
		return
	}

	gologger.Info().Msgf("域名 %s 端口扫描完成，发现 %d 个开放端口", options.Target, len(results))

	// 如果不进行Web扫描，直接处理插件任务
	if !options.WebScan {
		gologger.Info().Msg("Web扫描已禁用，跳过Web扫描阶段")

		// 执行插件系统处理端口扫描结果
		if !options.NoPlugin {
			core.DispatchPluginTasks(results)
		} else {
			gologger.Info().Msgf("插件系统已禁用，跳过插件任务执行")
		}
		return
	}

	// 收集Web服务目标
	var webTargets []string

	// 遍历扫描结果，构建Web目标
	for _, result := range results {
		// 直接使用域名构建Web目标
		target := options.Target

		// 收集HTTP/HTTPS服务
		if result.Server == "http" || result.Server == "https" {
			webTarget := fmt.Sprintf("%s://%s:%d", result.Server, target, result.Port)
			webTargets = append(webTargets, webTarget)
			continue
		}

		// 对于常见的Web端口，即使未识别为HTTP，也添加为HTTP目标
		if (result.Port == 80 || result.Port == 8080 || result.Port == 8088 || result.Port == 8000) && result.Server != "http" && result.Server != "https" {
			webTarget := fmt.Sprintf("http://%s:%d", target, result.Port)
			webTargets = append(webTargets, webTarget)
		}

		if result.Port == 443 && result.Server != "http" && result.Server != "https" {
			webTarget := fmt.Sprintf("https://%s:%d", target, result.Port)
			webTargets = append(webTargets, webTarget)
		}
	}

	// 同时添加基于域名的Web目标
	domainWebTargets := []string{"http://" + options.Target, "https://" + options.Target}
	webTargets = append(webTargets, domainWebTargets...)

	if len(webTargets) > 0 {
		// 分别解析每个URL
		var targets []string
		for _, webTarget := range webTargets {
			parsedURLs := clients.ParseURL(webTarget)
			targets = append(targets, parsedURLs...)
		}

		gologger.Info().Msgf("已解析 %d 个Web目标，准备进行Web扫描", len(targets))

		// 清除可能残留的端口扫描进度显示
		portscan.ClearProgress()

		// 执行Web扫描
		RunWebScan(targets, options)
	} else {
		gologger.Warning().Msg("未发现Web服务目标")
	}

	// Web扫描完成后，执行插件系统处理端口扫描结果
	if !options.NoPlugin {
		core.DispatchPluginTasks(results)
	} else {
		gologger.Info().Msgf("插件系统已禁用，跳过插件任务执行")
	}
}

// ProcessDomainWithIPResolution 先解析域名为IP再进行扫描
func ProcessDomainWithIPResolution(options *common.Options) {
	gologger.Info().Msgf("使用解析IP扫描模式: %s", options.Target)

	// 执行完整的域名解析
	domainInfo, err := netutil.ResolveDomain(options.Target)
	if err != nil {
		gologger.Error().Msgf("域名解析失败: %s", err)

		// 尝试直接作为Web目标处理
		if options.WebScan {
			targets := []string{"http://" + options.Target, "https://" + options.Target}
			gologger.Info().Msg("域名解析失败，尝试直接作为Web目标扫描")
			RunWebScan(targets, options)
		}
		return
	}

	// 打印域名解析信息
	domainInfo.PrintDomainInfo()

	// CDN检测和处理
	if domainInfo.CDNInfo != nil && domainInfo.CDNInfo.IsCDN {
		if options.SkipCDN {
			gologger.Warning().Msgf("检测到CDN保护，跳过扫描: %s (提供商: %s)",
				options.Target, domainInfo.CDNInfo.CDNProvider)
			return
		}

		// 如果检测到CDN且用户没有指定特定端口范围，使用CDN专用Web端口
		if options.PortRange == "default" || options.PortRange == "" {
			options.PortRange = "cdn-web"
			gologger.Info().Msgf("检测到CDN，使用CDN专用Web端口进行扫描")
		}

		if options.ForceRealIP && len(domainInfo.RealIPs) > 0 {
			gologger.Info().Msgf("检测到CDN，使用真实IP进行扫描: %v", domainInfo.RealIPs)
		} else if options.ForceRealIP {
			gologger.Warning().Msgf("检测到CDN但未发现真实IP，继续使用CDN IP扫描")
		}
	}

	// 泛解析检测和处理
	if domainInfo.IsWildcard {
		gologger.Warning().Msgf("检测到泛解析，扫描结果可能不准确: %s", options.Target)
	}

	// 获取所有解析到的IP地址，根据用户偏好排序
	var allIPs []string

	// 优先级：真实IP > IPv6/IPv4偏好 > 所有IP
	if options.ForceRealIP && len(domainInfo.RealIPs) > 0 {
		// 使用真实IP
		allIPs = domainInfo.RealIPs
		gologger.Info().Msgf("真实IP模式：使用发现的真实IP进行扫描")
	} else if options.PreferIPv6 && len(domainInfo.IPv6Addrs) > 0 {
		// 优先使用IPv6地址
		allIPs = append(allIPs, domainInfo.IPv6Addrs...)
		allIPs = append(allIPs, domainInfo.IPv4Addrs...)
		gologger.Info().Msgf("IPv6优先模式：优先使用IPv6地址进行扫描")
	} else {
		// 默认优先使用IPv4地址
		allIPs = append(allIPs, domainInfo.IPv4Addrs...)
		allIPs = append(allIPs, domainInfo.IPv6Addrs...)
	}

	if len(allIPs) == 0 {
		gologger.Warning().Msgf("域名 %s 没有解析到任何IP地址", options.Target)

		// 尝试直接作为Web目标处理
		if options.WebScan {
			targets := []string{"http://" + options.Target, "https://" + options.Target}
			gologger.Info().Msg("尝试直接作为Web目标扫描")
			RunWebScan(targets, options)
		}
		return
	}

	gologger.Info().Msgf("域名 %s 共解析到 %d 个IP地址（IPv4: %d个, IPv6: %d个），准备进行端口扫描",
		options.Target, len(allIPs), len(domainInfo.IPv4Addrs), len(domainInfo.IPv6Addrs))

	// 如果只进行Web扫描，直接处理Web目标
	if options.WebScan && len(options.PortRange) == 0 {
		// 优先使用域名进行Web扫描
		targets := []string{"http://" + options.Target, "https://" + options.Target}
		gologger.Info().Msg("执行域名Web扫描")
		RunWebScan(targets, options)
		return
	}

	// 执行端口扫描
	gologger.Info().Msgf("开始对域名 %s 的 %d 个IP地址进行端口扫描", options.Target, len(allIPs))
	results := portscan.ScanAndOutputResult(allIPs, options.PortRange, options.ExcludePort, options.TCPThreads, options.TCPTimeout, options.Proxy, options.Log, options.RandomPort, options.PortsThreshold, options.Html, options.ServiceCheck)

	if len(results) == 0 {
		gologger.Warning().Msgf("域名 %s 未发现开放端口", options.Target)
		return
	}

	gologger.Info().Msgf("域名 %s 端口扫描完成，发现 %d 个开放端口", options.Target, len(results))

	// 如果不进行Web扫描，直接处理插件任务
	if !options.WebScan {
		gologger.Info().Msg("Web扫描已禁用，跳过Web扫描阶段")

		// 执行插件系统处理端口扫描结果
		if !options.NoPlugin {
			core.DispatchPluginTasks(results)
		} else {
			gologger.Info().Msgf("插件系统已禁用，跳过插件任务执行")
		}
		return
	}

	// 收集Web服务目标
	var webTargets []string

	// 遍历扫描结果，构建Web目标
	for _, result := range results {
		// 根据IP版本构建URL格式
		var ipForURL string
		if netutil.IsIPv6(result.IP) {
			ipForURL = fmt.Sprintf("[%s]", result.IP) // IPv6需要用方括号包围
		} else {
			ipForURL = result.IP
		}

		// 收集HTTP/HTTPS服务
		if result.Server == "http" || result.Server == "https" {
			target := fmt.Sprintf("%s://%s:%d", result.Server, ipForURL, result.Port)
			webTargets = append(webTargets, target)
			continue
		}

		// 对于常见的Web端口，即使未识别为HTTP，也添加为HTTP目标
		if (result.Port == 80 || result.Port == 8080 || result.Port == 8088 || result.Port == 8000) && result.Server != "http" && result.Server != "https" {
			target := fmt.Sprintf("http://%s:%d", ipForURL, result.Port)
			webTargets = append(webTargets, target)
		}

		if result.Port == 443 && result.Server != "http" && result.Server != "https" {
			target := fmt.Sprintf("https://%s:%d", ipForURL, result.Port)
			webTargets = append(webTargets, target)
		}
	}

	// 同时添加基于域名的Web目标
	domainWebTargets := []string{"http://" + options.Target, "https://" + options.Target}
	webTargets = append(webTargets, domainWebTargets...)

	if len(webTargets) > 0 {
		// 分别解析每个URL
		var targets []string
		for _, webTarget := range webTargets {
			parsedURLs := clients.ParseURL(webTarget)
			targets = append(targets, parsedURLs...)
		}

		gologger.Info().Msgf("已解析 %d 个Web目标，准备进行Web扫描", len(targets))

		// 清除可能残留的端口扫描进度显示
		portscan.ClearProgress()

		// 执行Web扫描
		RunWebScan(targets, options)
	} else {
		gologger.Warning().Msg("未发现Web服务目标")
	}

	// Web扫描完成后，执行插件系统处理端口扫描结果
	if !options.NoPlugin {
		core.DispatchPluginTasks(results)
	} else {
		gologger.Info().Msgf("插件系统已禁用，跳过插件任务执行")
	}
}

// ProcessDomainResolveOnly 只进行域名解析，不进行端口扫描
func ProcessDomainResolveOnly(options *common.Options) {
	gologger.Info().Msgf("域名解析模式: %s", options.Target)

	// 执行域名解析
	domainInfo, err := netutil.ResolveDomain(options.Target)
	if err != nil {
		gologger.Error().Msgf("域名解析失败: %s", err)
		return
	}

	// 打印详细的域名解析信息
	domainInfo.PrintDomainInfo()

	// 如果启用了完整解析，显示更多信息
	if options.ResolveAll {
		gologger.Info().Msgf("=== 完整DNS记录信息 ===")

		if len(domainInfo.CNAMEs) > 0 {
			gologger.Info().Msgf("CNAME记录 (%d个):", len(domainInfo.CNAMEs))
			for _, cname := range domainInfo.CNAMEs {
				gologger.Info().Msgf("  └─ %s", cname)
			}
		}

		if len(domainInfo.MXRecords) > 0 {
			gologger.Info().Msgf("MX记录 (%d个):", len(domainInfo.MXRecords))
			for _, mx := range domainInfo.MXRecords {
				gologger.Info().Msgf("  └─ %s", mx)
			}
		}

		if len(domainInfo.TXTRecords) > 0 {
			gologger.Info().Msgf("TXT记录 (%d个):", len(domainInfo.TXTRecords))
			for _, txt := range domainInfo.TXTRecords {
				gologger.Info().Msgf("  └─ %s", txt)
			}
		}

		if len(domainInfo.NSRecords) > 0 {
			gologger.Info().Msgf("NS记录 (%d个):", len(domainInfo.NSRecords))
			for _, ns := range domainInfo.NSRecords {
				gologger.Info().Msgf("  └─ %s", ns)
			}
		}
	}

	// 统计信息
	totalIPs := len(domainInfo.GetAllIPs())
	gologger.Info().Msgf("=== 解析统计 ===")
	gologger.Info().Msgf("域名: %s", domainInfo.Domain)
	gologger.Info().Msgf("IPv4地址: %d个", len(domainInfo.IPv4Addrs))
	gologger.Info().Msgf("IPv6地址: %d个", len(domainInfo.IPv6Addrs))
	gologger.Info().Msgf("总IP地址: %d个", totalIPs)

	if len(domainInfo.CNAMEs) > 0 {
		gologger.Info().Msgf("CNAME记录: %d个", len(domainInfo.CNAMEs))
	}
	if len(domainInfo.MXRecords) > 0 {
		gologger.Info().Msgf("MX记录: %d个", len(domainInfo.MXRecords))
	}
	if len(domainInfo.TXTRecords) > 0 {
		gologger.Info().Msgf("TXT记录: %d个", len(domainInfo.TXTRecords))
	}
	if len(domainInfo.NSRecords) > 0 {
		gologger.Info().Msgf("NS记录: %d个", len(domainInfo.NSRecords))
	}
}

// ProcessURL 处理URL
func ProcessURL(options *common.Options) {
	gologger.Info().Msgf("检测到URL: %s", options.Target)

	targets := clients.ParseURL(options.Target)
	if len(targets) > 0 {
		if options.WebScan {
			RunWebScan(targets, options)
		} else {
			gologger.Info().Msg("Web扫描已禁用，跳过Web扫描阶段")
		}
	} else {
		gologger.Error().Msg("URL解析失败")
	}
}

// IsIPWithPort 判断是否为IP:端口格式（支持IPv4和IPv6）
func IsIPWithPort(target string) bool {
	// IPv6地址格式：[2001:db8::1]:8080 或 [fe80::1%eth0]:8080
	if strings.HasPrefix(target, "[") && strings.Contains(target, "]:") {
		parts := strings.Split(target, "]:")
		if len(parts) != 2 {
			return false
		}

		// 检查IPv6地址部分（去掉前面的[）
		ipv6 := strings.TrimPrefix(parts[0], "[")
		if !netutil.IsIPv6(ipv6) {
			return false
		}

		// 检查端口部分
		port, err := strconv.Atoi(parts[1])
		if err != nil {
			return false
		}

		return port > 0 && port < 65536
	}

	// IPv4地址格式：***********:8080
	parts := strings.Split(target, ":")
	if len(parts) != 2 {
		return false
	}

	// 检查IPv4地址部分
	if !netutil.IsIPv4(parts[0]) {
		return false
	}

	// 检查端口部分
	port, err := strconv.Atoi(parts[1])
	if err != nil {
		return false
	}

	return port > 0 && port < 65536
}
