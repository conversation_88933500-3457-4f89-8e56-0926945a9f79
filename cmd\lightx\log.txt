[2025-07-11 10:11:19] [Port] rpc:127.0.0.1:135
[2025-07-11 10:11:19] [Plugin:NetInfo] 开始对 127.0.0.1:135 进行网络信息探测
[2025-07-11 10:11:19] [Plugin:NetInfo:SUCCESS] RPC:127.0.0.1:135 主机名: zsec
[2025-07-11 10:11:19] [Plugin:NetInfo] 127.0.0.1:135 统计：成功=1 失败=0 总尝试=1
[2025-07-11 10:50:40] [Port] http:************:8161
[2025-07-11 10:50:40] [Plugin:ActiveMQ] 开始对 ************:8161 进行ActiveMQ暴力破解
[2025-07-11 10:50:40] [Plugin:ActiveMQ] ************:8161 统计：成功=0 失败=370 总尝试=370
[2025-07-11 10:55:42] [Finger] http://asd45fw345.admin.test.local:8161 [200] [6047] [Apache ActiveMQ] [Jetty | APACHE-ActiveMQ | Eclipse-Jetty]
[2025-07-11 10:55:42] [Finger] http://tes3453t2.api.test.local:8161 [200] [6047] [Apache ActiveMQ] [Jetty | APACHE-ActiveMQ | Eclipse-Jetty]
[2025-07-11 11:07:49] [Finger] http://aawe23e1sd.admin.test.local:8161 [200] [6047] [Apache ActiveMQ] [Jetty | Eclipse-Jetty | APACHE-ActiveMQ]
[2025-07-11 11:07:49] [Finger] http://tes435t1.api.test.local:8161 [200] [6047] [Apache ActiveMQ] [Jetty | Eclipse-Jetty | APACHE-ActiveMQ]
[2025-07-11 11:07:51] [CVE-2016-3088] [CRITICAL] http://tes435t1.api.test.local:8161/fileserver/2zi9xP2Rn7NKKVfuLBvkh4EzAqc.txt 
[2025-07-11 11:07:51] [CVE-2016-3088] [CRITICAL] http://aawe23e1sd.admin.test.local:8161/fileserver/2zi9xP2Rn7NKKVfuLBvkh4EzAqc.txt 
[2025-07-11 11:07:52] [activemq-default-login] [HIGH] http://aawe23e1sd.admin.test.local:8161/admin/ 
[2025-07-11 11:07:52] [activemq-default-login] [HIGH] http://tes435t1.api.test.local:8161/admin/ 
[2025-07-11 11:07:52] [activemq-default-login] [HIGH] http://aawe23e1sd.admin.test.local:8161/admin/ 
[2025-07-11 11:07:52] [activemq-default-login] [HIGH] http://tes435t1.api.test.local:8161/admin/ 
[2025-07-11 11:08:11] [Finger] http://as123d123.admin.test.local:8161 [200] [6047] [Apache ActiveMQ] [Eclipse-Jetty | Jetty | APACHE-ActiveMQ]
[2025-07-11 11:08:11] [Finger] http://different.api.test.local:8161 [200] [6047] [Apache ActiveMQ] [Eclipse-Jetty | Jetty | APACHE-ActiveMQ]
[2025-07-11 11:08:12] [CVE-2016-3088] [CRITICAL] http://different.api.test.local:8161/fileserver/2ziA02UHEGEuatLE8XeVWvqgjvG.txt 
[2025-07-11 11:08:12] [CVE-2016-3088] [CRITICAL] http://as123d123.admin.test.local:8161/fileserver/2zi9zv6h74j9ULYm0K27rUYrhqo.txt 
[2025-07-11 11:08:12] [activemq-default-login] [HIGH] http://different.api.test.local:8161/admin/ 
[2025-07-11 11:08:12] [activemq-default-login] [HIGH] http://as123d123.admin.test.local:8161/admin/ 
[2025-07-11 11:08:12] [activemq-default-login] [HIGH] http://different.api.test.local:8161/admin/ 
[2025-07-11 11:08:12] [activemq-default-login] [HIGH] http://as123d123.admin.test.local:8161/admin/ 
[2025-07-11 12:54:06] [Port] http:127.0.0.1:7890
[2025-07-11 12:54:06] [Finger] http://127.0.0.1:7890 [400] [0] [] []
[2025-07-11 12:54:25] [Port] http:127.0.0.1:7890
[2025-07-11 12:54:25] [Finger] http://127.0.0.1:7890 [400] [0] [] []
[2025-07-11 13:55:54] [Port] http:127.0.0.1:7890
[2025-07-11 13:55:54] [Finger] http://127.0.0.1:7890 [400] [0] [] []
[2025-07-11 17:17:52] [Port] http:127.0.0.1:7890
[2025-07-11 17:17:53] [Finger] http://127.0.0.1:7890 [400] [0] [] []
[2025-07-11 17:23:58] [Port] ssh:127.0.0.1:22
[2025-07-11 17:23:58] [Port] rpc:127.0.0.1:135
[2025-07-11 17:24:01] [Port] smb:127.0.0.1:445
[2025-07-11 17:24:04] [Port] http:127.0.0.1:7890
[2025-07-11 17:24:04] [Port] http:127.0.0.1:8680
[2025-07-11 17:24:05] [Port] https:127.0.0.1:10000
[2025-07-11 17:24:07] [Port] tcp:127.0.0.1:1001
[2025-07-11 17:24:07] [Finger] http://127.0.0.1:7890 [400] [0] [] []
[2025-07-11 17:24:07] [Finger] http://127.0.0.1:8680 [404] [0] [] []
[2025-07-11 17:24:10] [Plugin:SSH] 开始对 127.0.0.1:22 进行SSH暴力破解
[2025-07-11 17:24:10] [Plugin:NetInfo] 开始对 127.0.0.1:135 进行网络信息探测
[2025-07-11 17:24:10] [Plugin:MS17-010] 开始对 127.0.0.1:445 进行永恒之蓝漏洞检测
[2025-07-11 17:24:10] [Plugin:SMB] 开始对 127.0.0.1:445 进行SMB暴力破解
[2025-07-11 17:24:10] [Plugin:MS17-010] 127.0.0.1:445 统计：成功=0 失败=1 总尝试=1
[2025-07-11 17:24:10] [Plugin:NetInfo:SUCCESS] RPC:127.0.0.1:135 主机名: zsec
[2025-07-11 17:24:10] [Plugin:NetInfo] 127.0.0.1:135 统计：成功=1 失败=0 总尝试=1
[2025-07-11 17:24:12] [Plugin:SMB] 127.0.0.1:445 统计：成功=0 失败=134 总尝试=134
[2025-07-11 17:24:18] [Plugin:SSH] 127.0.0.1:22 统计：成功=0 失败=148 总尝试=148
[2025-07-11 17:34:00] [Port] ssh:127.0.0.1:22
[2025-07-11 17:34:00] [Port] rpc:127.0.0.1:135
[2025-07-11 17:34:03] [Port] smb:127.0.0.1:445
[2025-07-11 17:34:06] [Port] http:127.0.0.1:7890
[2025-07-11 17:34:06] [Port] http:127.0.0.1:8680
[2025-07-11 17:34:07] [Port] https:127.0.0.1:10000
[2025-07-11 17:34:09] [Port] tcp:127.0.0.1:1001
[2025-07-11 17:34:09] [Finger] http://127.0.0.1:7890 [400] [0] [] []
[2025-07-11 17:34:09] [Finger] http://127.0.0.1:8680 [404] [0] [] []
[2025-07-11 17:34:11] [Plugin:NetInfo] 开始对 127.0.0.1:135 进行网络信息探测
[2025-07-11 17:34:11] [Plugin:MS17-010] 开始对 127.0.0.1:445 进行永恒之蓝漏洞检测
[2025-07-11 17:34:11] [Plugin:SMB] 开始对 127.0.0.1:445 进行SMB暴力破解
[2025-07-11 17:34:11] [Plugin:SSH] 开始对 127.0.0.1:22 进行SSH暴力破解
[2025-07-11 17:34:11] [Plugin:NetInfo:SUCCESS] RPC:127.0.0.1:135 主机名: zsec
[2025-07-11 17:34:11] [Plugin:MS17-010] 127.0.0.1:445 统计：成功=0 失败=1 总尝试=1
[2025-07-11 17:34:11] [Plugin:NetInfo] 127.0.0.1:135 统计：成功=1 失败=0 总尝试=1
[2025-07-11 17:34:13] [Plugin:SMB] 127.0.0.1:445 统计：成功=0 失败=76 总尝试=76
[2025-07-11 17:34:18] [Plugin:SSH] 127.0.0.1:22 统计：成功=0 失败=148 总尝试=148
[2025-07-11 17:42:20] [Port] ssh:127.0.0.1:22
[2025-07-11 17:42:20] [Port] rpc:127.0.0.1:135
[2025-07-11 17:42:23] [Port] smb:127.0.0.1:445
[2025-07-11 17:42:26] [Port] http:127.0.0.1:7890
[2025-07-11 17:42:26] [Port] http:127.0.0.1:8680
[2025-07-11 17:42:27] [Port] https:127.0.0.1:10000
[2025-07-11 17:42:29] [Port] tcp:127.0.0.1:1001
[2025-07-11 17:42:30] [Finger] http://127.0.0.1:7890 [400] [0] [] []
[2025-07-11 17:42:30] [Finger] http://127.0.0.1:8680 [404] [0] [] []
[2025-07-11 17:42:31] [Finger] https://127.0.0.1:10000 [400] [51] [] [DigiCert-Cert]
[2025-07-11 17:42:33] [Plugin:SSH] 开始对 127.0.0.1:22 进行SSH暴力破解
[2025-07-11 17:42:33] [Plugin:MS17-010] 开始对 127.0.0.1:445 进行永恒之蓝漏洞检测
[2025-07-11 17:42:33] [Plugin:NetInfo] 开始对 127.0.0.1:135 进行网络信息探测
[2025-07-11 17:42:33] [Plugin:SMB] 开始对 127.0.0.1:445 进行SMB暴力破解
[2025-07-11 17:42:33] [Plugin:MS17-010] 127.0.0.1:445 统计：成功=0 失败=1 总尝试=1
[2025-07-11 17:42:33] [Plugin:NetInfo:SUCCESS] RPC:127.0.0.1:135 主机名: zsec
[2025-07-11 17:42:33] [Plugin:NetInfo] 127.0.0.1:135 统计：成功=1 失败=0 总尝试=1
[2025-07-11 17:42:36] [Plugin:SMB] 127.0.0.1:445 统计：成功=0 失败=134 总尝试=134
[2025-07-11 17:42:41] [Plugin:SSH] 127.0.0.1:22 统计：成功=0 失败=148 总尝试=148
[2025-07-11 17:47:39] [Port] ssh:127.0.0.1:22
[2025-07-11 17:47:39] [Port] rpc:127.0.0.1:135
[2025-07-11 17:47:42] [Port] smb:127.0.0.1:445
[2025-07-11 17:47:45] [Port] http:127.0.0.1:7890
[2025-07-11 17:47:45] [Port] http:127.0.0.1:8680
[2025-07-11 17:47:46] [Port] https:127.0.0.1:10000
[2025-07-11 17:47:48] [Port] tcp:127.0.0.1:1001
[2025-07-11 17:47:48] [Finger] http://127.0.0.1:7890 [400] [0] [] []
[2025-07-11 17:47:48] [Finger] http://127.0.0.1:8680 [404] [0] [] []
[2025-07-11 17:47:50] [Finger] https://127.0.0.1:10000 [400] [51] [] [DigiCert-Cert]
[2025-07-11 17:47:52] [Plugin:SSH] 开始对 127.0.0.1:22 进行SSH暴力破解
[2025-07-11 17:47:52] [Plugin:NetInfo] 开始对 127.0.0.1:135 进行网络信息探测
[2025-07-11 17:47:52] [Plugin:MS17-010] 开始对 127.0.0.1:445 进行永恒之蓝漏洞检测
[2025-07-11 17:47:52] [Plugin:SMB] 开始对 127.0.0.1:445 进行SMB暴力破解
[2025-07-11 17:47:52] [Plugin:MS17-010] 127.0.0.1:445 统计：成功=0 失败=1 总尝试=1
[2025-07-11 17:47:52] [Plugin:NetInfo:SUCCESS] RPC:127.0.0.1:135 主机名: zsec
[2025-07-11 17:47:52] [Plugin:NetInfo] 127.0.0.1:135 统计：成功=1 失败=0 总尝试=1
[2025-07-11 17:47:54] [Plugin:SMB] 127.0.0.1:445 统计：成功=0 失败=76 总尝试=76
[2025-07-11 17:47:59] [Plugin:SSH] 127.0.0.1:22 统计：成功=0 失败=148 总尝试=148
[2025-07-11 17:49:37] [Port] ssh:asd.admin.test.local:22
[2025-07-11 17:49:57] [Port] ssh:asd.admin.test.local:22
[2025-07-11 17:50:10] [Plugin:SSH] 开始对 asd.admin.test.local:22 进行SSH暴力破解
[2025-07-11 17:50:16] [Plugin:SSH] asd.admin.test.local:22 统计：成功=0 失败=74 总尝试=74
[2025-07-11 17:52:35] [Port] ssh:asd.admin.test.local:22
[2025-07-11 17:52:48] [Plugin:SSH] 开始对 asd.admin.test.local:22 进行SSH暴力破解
[2025-07-11 17:52:50] [Plugin:SSH:SUCCESS] SSH:asd.admin.test.local:22 kali/kali
[2025-07-11 17:52:50] [Plugin:SSH] asd.admin.test.local:22 统计：成功=1 失败=1 总尝试=2
[2025-07-11 17:55:13] [Port] ssh:asd.admin.test.local:22
[2025-07-11 17:55:25] [Plugin:SSH] 开始对 asd.admin.test.local:22 进行SSH暴力破解
[2025-07-11 17:55:28] [Plugin:SSH:SUCCESS] SSH:asd.admin.test.local:22 kali/kali
[2025-07-11 17:55:28] [Plugin:SSH] asd.admin.test.local:22 统计：成功=1 失败=1 总尝试=2
[2025-07-12 16:36:03] [Port] https:blog.uuming.com:443
[2025-07-21 13:54:40] [crawler] 启用无头浏览器访问: https://**********/
[2025-07-21 13:54:40] [crawler] 等待时间设置为 15 秒
[2025-07-21 13:54:40] [crawler] 开始执行爬虫任务...
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/ [方法: GET]
[2025-07-21 13:54:41] [爬虫请求] https://**********/ [状态码: 200]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/supermap/leaflet.css [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/supermap/iclient-leaflet.css [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/supermap/MarkerCluster.Default.css [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/supermap/MarkerCluster.css [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/supermap/leaflet.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/supermap/jquery.mini.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/supermap/Markerext.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/supermap/leaflet-heat.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/output/@datav/utils/dist/app.bundle.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/supermap/iclient-leaflet-es6.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/supermap/leaflet.markercluster.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://res.wx.qq.com/open/js/jweixin-1.3.2.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/app.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/chunk-vendors.js [方法: GET]
[2025-07-21 13:54:41] [爬虫请求] https://**********/supermap/leaflet.css [状态码: 200]
[2025-07-21 13:54:41] [爬虫请求] https://**********/supermap/iclient-leaflet.css [状态码: 200]
[2025-07-21 13:54:41] [爬虫请求] https://**********/supermap/MarkerCluster.Default.css [状态码: 200]
[2025-07-21 13:54:41] [爬虫请求] https://**********/supermap/MarkerCluster.css [状态码: 200]
[2025-07-21 13:54:41] [爬虫请求] https://**********/supermap/Markerext.js [状态码: 200]
[2025-07-21 13:54:41] [爬虫请求] https://**********/supermap/leaflet.js [状态码: 200]
[2025-07-21 13:54:41] [爬虫请求] https://res.wx.qq.com/open/js/jweixin-1.3.2.js [状态码: 200]
[2025-07-21 13:54:41] [爬虫请求] https://**********/supermap/leaflet-heat.js [状态码: 200]
[2025-07-21 13:54:41] [爬虫请求] https://**********/supermap/jquery.mini.js [状态码: 200]
[2025-07-21 13:54:41] [爬虫请求] https://**********/output/@datav/utils/dist/app.bundle.js [状态码: 200]
[2025-07-21 13:54:41] [爬虫请求] https://**********/supermap/leaflet.markercluster.js [状态码: 200]
[2025-07-21 13:54:41] [爬虫请求] https://**********/supermap/iclient-leaflet-es6.js [状态码: 200]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/bind-phone-num.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/change-phone-num.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/change-phone-num~forget-password.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/channel-operation.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/channel-operation~jiaotwlbtbc~qiaosjgjc~tongjfx~xianq-assistlist~zhongdjsgc.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/channel-operation~xianq-assistlist.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/channel-operation~xianq-detail.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/detail.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/forget-password.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/gangkqytb.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/gangkqytbDetail.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/gangkqytbEdit.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/gangkqytbInfo.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/haisyjml.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/hangdxq.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/hangdycjcyj.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/hangdycjcyj~map~sfzmap-list~video~video-list~xianq-assistlist.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/hangdycyjxx.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/hangdycyjxx~hangdyxjcsj.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/hangdyx.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/hangdyxjcsj.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/hangdyx~login-app.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/home.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/index.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/jiaotwlbtbc.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/jiaotwlbtbc~yingjsj.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/kuaqyxz.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/login-app.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/map.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/map~qiaosjgjc.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/map~search-deatils.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/map~search-deatils~xianq-assistlist.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/map~sfzmap-list.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/map~sfzmap-list~video~video-list.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/map~video.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/map~xianq-assistlist.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/message.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/open-page.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/qiaosjgjc.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/qiaosjgjc~xianq-assistlist~zhongdjsgc.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/qxzhyj.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/search-deatils.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/search-plai.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/sfzmap-list.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/shebgx.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/sihncgl-yilyd-da-detail.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/sihncgl-yilyd-da.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/sihncgl-yilyd-xm.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/sihncgl-yilyd.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/tongjbg.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/tongjbg~yingjdw~yingjsj~yingjzy~yjct~yjfj~yjry~yjss.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/tongjfx.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/video-list.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/video.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/xianq-assistlist.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/xianq-detail.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/xianq-history',.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/xianq-prediction.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/xianq-search.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/xianq-send.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/xianq-track.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/xianq-track~xinxlssgl.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/xinxlssgl.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/yingjdw.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/yingjsj.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/yingjzy.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/yjct.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/yjfj.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/yjry.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/yjss.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/zhongdjsgc.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/zhongyzj.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/zhujhy.js [方法: GET]
[2025-07-21 13:54:41] [爬虫发送请求] https://**********/js/zhxzzf.js [方法: GET]
[2025-07-21 13:54:41] [爬虫请求] https://**********/js/app.js [状态码: 200]
[2025-07-21 13:54:41] [爬虫请求] https://**********/js/change-phone-num.js [状态码: 200]
[2025-07-21 13:54:41] [爬虫请求] https://**********/js/bind-phone-num.js [状态码: 200]
[2025-07-21 13:54:41] [爬虫请求] https://**********/js/channel-operation~jiaotwlbtbc~qiaosjgjc~tongjfx~xianq-assistlist~zhongdjsgc.js [状态码: 200]
[2025-07-21 13:54:41] [爬虫请求] https://**********/js/change-phone-num~forget-password.js [状态码: 200]
[2025-07-21 13:54:41] [爬虫请求] https://**********/js/chunk-vendors.js [状态码: 200]
[2025-07-21 13:54:41] [爬虫请求] https://**********/js/channel-operation.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫发送请求] https://gjxxpt.mot.gov.cn/appProdservice/anon/emergency/jtcommon/commonQuerryByName [方法: OPTIONS]
[2025-07-21 13:54:42] [爬虫发送请求] https://gjxxpt.mot.gov.cn/appProdservice/nvwa/getLoginContext [方法: OPTIONS]
[2025-07-21 13:54:42] [爬虫发送请求] https://gjxxpt.mot.gov.cn/appProdservice/yxjc/app/userPhone/getIsTip [方法: OPTIONS]
[2025-07-21 13:54:42] [爬虫发送请求] https://**********/output/i18n/zh-cn.json?timestamp=1753077282192 [方法: GET]
[2025-07-21 13:54:42] [爬虫发送请求] https://**********/js/hangdyx~login-app.js [方法: GET]
[2025-07-21 13:54:42] [爬虫发送请求] https://**********/js/login-app.js [方法: GET]
[2025-07-21 13:54:42] [爬虫发送请求] https://gjxxpt.mot.gov.cn/appProdservice/anon/emergency/jtcommon/commonQuerryByName [方法: POST]
[2025-07-21 13:54:42] [爬虫发送请求] https://gjxxpt.mot.gov.cn/appProdservice/nvwa/getLoginContext [方法: GET]
[2025-07-21 13:54:42] [爬虫发送请求] https://gjxxpt.mot.gov.cn/appProdservice/yxjc/app/userPhone/getIsTip [方法: POST]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/channel-operation~xianq-assistlist.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/channel-operation~xianq-detail.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/gangkqytb.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/detail.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/forget-password.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/gangkqytbEdit.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/gangkqytbDetail.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/gangkqytbInfo.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/hangdxq.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/haisyjml.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/hangdycjcyj.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/hangdycjcyj~map~sfzmap-list~video~video-list~xianq-assistlist.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/hangdycyjxx.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/hangdycyjxx~hangdyxjcsj.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/hangdyxjcsj.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/hangdyx.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/home.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/index.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/hangdyx~login-app.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/jiaotwlbtbc.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/jiaotwlbtbc~yingjsj.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/kuaqyxz.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/map~qiaosjgjc.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/login-app.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/map.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/map~search-deatils.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/map~search-deatils~xianq-assistlist.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/map~sfzmap-list.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/map~video.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/map~sfzmap-list~video~video-list.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/hangdyx~login-app.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/output/i18n/zh-cn.json?timestamp=1753077282192 [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://gjxxpt.mot.gov.cn/appProdservice/nvwa/getLoginContext [状态码: 204]
[2025-07-21 13:54:42] [爬虫请求] https://gjxxpt.mot.gov.cn/appProdservice/anon/emergency/jtcommon/commonQuerryByName [状态码: 204]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/open-page.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/message.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://gjxxpt.mot.gov.cn/appProdservice/yxjc/app/userPhone/getIsTip [状态码: 204]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/login-app.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫发送请求] https://gjxxpt.mot.gov.cn/appProdservice/anon/sliderVerify [方法: OPTIONS]
[2025-07-21 13:54:42] [爬虫发送请求] https://**********/img/login-bg.f8fd98e6.png [方法: GET]
[2025-07-21 13:54:42] [爬虫发送请求] https://gjxxpt.mot.gov.cn/appProdservice/anon/sliderVerify [方法: GET]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/map~xianq-assistlist.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/qiaosjgjc.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/qiaosjgjc~xianq-assistlist~zhongdjsgc.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫发送请求] https://gjxxpt.mot.gov.cn/appProdservice/yxjc/app/userPhone/getAccountIsTip [方法: POST]
[2025-07-21 13:54:42] [爬虫发送请求] https://gjxxpt.mot.gov.cn/appProdservice/yxjc/app/userPhone/getAccountIsTip [方法: OPTIONS]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/qxzhyj.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://gjxxpt.mot.gov.cn/appProdservice/anon/emergency/jtcommon/commonQuerryByName [状态码: 502]
[2025-07-21 13:54:42] [爬虫请求] https://gjxxpt.mot.gov.cn/appProdservice/nvwa/getLoginContext [状态码: 502]
[2025-07-21 13:54:42] [爬虫请求] https://gjxxpt.mot.gov.cn/appProdservice/yxjc/app/userPhone/getIsTip [状态码: 502]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/search-plai.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/search-deatils.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/img/login-bg.f8fd98e6.png [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/shebgx.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/sfzmap-list.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://gjxxpt.mot.gov.cn/appProdservice/yxjc/app/userPhone/getAccountIsTip [状态码: 204]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/sihncgl-yilyd-da-detail.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/sihncgl-yilyd-da.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫发送请求] https://**********/favicon.ico [方法: GET]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/sihncgl-yilyd-xm.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/sihncgl-yilyd.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/tongjbg.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://gjxxpt.mot.gov.cn/appProdservice/anon/sliderVerify [状态码: 204]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/tongjbg~yingjdw~yingjsj~yingjzy~yjct~yjfj~yjry~yjss.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/tongjfx.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/video-list.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/video.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/favicon.ico [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://gjxxpt.mot.gov.cn/appProdservice/yxjc/app/userPhone/getAccountIsTip [状态码: 502]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/xianq-assistlist.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/xianq-history',.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/xianq-detail.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/xianq-prediction.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/xianq-search.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/xianq-send.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/xianq-track.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://gjxxpt.mot.gov.cn/appProdservice/anon/sliderVerify [状态码: 502]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/xianq-track~xinxlssgl.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/yingjdw.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/yingjzy.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/yingjsj.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/xinxlssgl.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/yjct.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/yjfj.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/yjry.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/yjss.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/zhongdjsgc.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/zhujhy.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/zhongyzj.js [状态码: 200]
[2025-07-21 13:54:42] [爬虫请求] https://**********/js/zhxzzf.js [状态码: 200]
[2025-07-21 13:54:46] [crawler] 完成初步爬取，页面标题: 登录, 内容长度: 835347
[2025-07-21 13:54:46] [crawler] 爬虫捕获到 104 个资源链接，其中 91 个属于当前站点
[2025-07-21 13:54:46] ---站内资源URL列表开始---
[2025-07-21 13:54:46] https://**********/js/hangdxq.js
[2025-07-21 13:54:46] https://**********/js/home.js
[2025-07-21 13:54:46] https://**********/js/sihncgl-yilyd-da.js
[2025-07-21 13:54:46] https://**********/js/tongjbg.js
[2025-07-21 13:54:46] https://**********/
[2025-07-21 13:54:46] https://**********/js/map~qiaosjgjc.js
[2025-07-21 13:54:46] https://**********/js/xianq-track.js
[2025-07-21 13:54:46] https://**********/favicon.ico
[2025-07-21 13:54:46] https://**********/supermap/leaflet.css
[2025-07-21 13:54:46] https://**********/js/change-phone-num.js
[2025-07-21 13:54:46] https://**********/js/channel-operation~xianq-detail.js
[2025-07-21 13:54:46] https://**********/js/jiaotwlbtbc.js
[2025-07-21 13:54:46] https://**********/js/login-app.js
[2025-07-21 13:54:46] https://**********/js/map~sfzmap-list.js
[2025-07-21 13:54:46] https://**********/js/search-plai.js
[2025-07-21 13:54:46] https://**********/supermap/MarkerCluster.css
[2025-07-21 13:54:46] https://**********/js/kuaqyxz.js
[2025-07-21 13:54:46] https://**********/js/map.js
[2025-07-21 13:54:46] https://**********/js/map~xianq-assistlist.js
[2025-07-21 13:54:46] https://**********/js/qiaosjgjc.js
[2025-07-21 13:54:46] https://**********/js/zhongyzj.js
[2025-07-21 13:54:46] https://**********/js/bind-phone-num.js
[2025-07-21 13:54:46] https://**********/js/message.js
[2025-07-21 13:54:46] https://**********/js/shebgx.js
[2025-07-21 13:54:46] https://**********/js/tongjfx.js
[2025-07-21 13:54:46] https://**********/js/xianq-detail.js
[2025-07-21 13:54:46] https://**********/js/zhxzzf.js
[2025-07-21 13:54:46] https://**********/js/jiaotwlbtbc~yingjsj.js
[2025-07-21 13:54:46] https://**********/supermap/leaflet.markercluster.js
[2025-07-21 13:54:46] https://**********/js/channel-operation~jiaotwlbtbc~qiaosjgjc~tongjfx~xianq-assistlist~zhongdjsgc.js
[2025-07-21 13:54:46] https://**********/js/hangdycjcyj~map~sfzmap-list~video~video-list~xianq-assistlist.js
[2025-07-21 13:54:46] https://**********/js/map~video.js
[2025-07-21 13:54:46] https://**********/js/yingjdw.js
[2025-07-21 13:54:46] https://**********/supermap/MarkerCluster.Default.css
[2025-07-21 13:54:46] https://**********/js/gangkqytb.js
[2025-07-21 13:54:46] https://**********/js/gangkqytbEdit.js
[2025-07-21 13:54:46] https://**********/js/open-page.js
[2025-07-21 13:54:46] https://**********/js/qiaosjgjc~xianq-assistlist~zhongdjsgc.js
[2025-07-21 13:54:46] https://**********/js/xianq-history',.js
[2025-07-21 13:54:46] https://**********/js/yjss.js
[2025-07-21 13:54:46] https://**********/js/zhujhy.js
[2025-07-21 13:54:46] https://**********/js/forget-password.js
[2025-07-21 13:54:46] https://**********/js/channel-operation.js
[2025-07-21 13:54:46] https://**********/js/detail.js
[2025-07-21 13:54:46] https://**********/js/hangdycjcyj.js
[2025-07-21 13:54:46] https://**********/js/map~sfzmap-list~video~video-list.js
[2025-07-21 13:54:46] https://**********/js/video-list.js
[2025-07-21 13:54:46] https://**********/js/xinxlssgl.js
[2025-07-21 13:54:46] https://**********/js/app.js
[2025-07-21 13:54:46] https://**********/supermap/leaflet.js
[2025-07-21 13:54:46] https://**********/supermap/jquery.mini.js
[2025-07-21 13:54:46] https://**********/js/hangdyxjcsj.js
[2025-07-21 13:54:46] https://**********/js/qxzhyj.js
[2025-07-21 13:54:46] https://**********/js/sihncgl-yilyd-xm.js
[2025-07-21 13:54:46] https://**********/supermap/iclient-leaflet.css
[2025-07-21 13:54:46] https://**********/js/xianq-track~xinxlssgl.js
[2025-07-21 13:54:46] https://**********/js/sihncgl-yilyd.js
[2025-07-21 13:54:46] https://**********/js/sfzmap-list.js
[2025-07-21 13:54:46] https://**********/js/video.js
[2025-07-21 13:54:46] https://**********/img/login-bg.f8fd98e6.png
[2025-07-21 13:54:46] https://**********/js/hangdycyjxx~hangdyxjcsj.js
[2025-07-21 13:54:46] https://**********/js/xianq-send.js
[2025-07-21 13:54:46] https://**********/js/yjct.js
[2025-07-21 13:54:46] https://**********/js/xianq-search.js
[2025-07-21 13:54:46] https://**********/js/haisyjml.js
[2025-07-21 13:54:46] https://**********/js/zhongdjsgc.js
[2025-07-21 13:54:46] https://**********/js/gangkqytbDetail.js
[2025-07-21 13:54:46] https://**********/js/tongjbg~yingjdw~yingjsj~yingjzy~yjct~yjfj~yjry~yjss.js
[2025-07-21 13:54:46] https://**********/js/xianq-assistlist.js
[2025-07-21 13:54:46] https://**********/js/xianq-prediction.js
[2025-07-21 13:54:46] https://**********/js/yingjsj.js
[2025-07-21 13:54:46] https://**********/js/yingjzy.js
[2025-07-21 13:54:46] https://**********/js/yjry.js
[2025-07-21 13:54:46] https://**********/js/map~search-deatils~xianq-assistlist.js
[2025-07-21 13:54:46] https://**********/supermap/iclient-leaflet-es6.js
[2025-07-21 13:54:46] https://**********/js/change-phone-num~forget-password.js
[2025-07-21 13:54:46] https://**********/js/channel-operation~xianq-assistlist.js
[2025-07-21 13:54:46] https://**********/js/map~search-deatils.js
[2025-07-21 13:54:46] https://**********/js/sihncgl-yilyd-da-detail.js
[2025-07-21 13:54:46] https://**********/js/yjfj.js
[2025-07-21 13:54:46] https://**********/supermap/Markerext.js
[2025-07-21 13:54:46] https://**********/output/@datav/utils/dist/app.bundle.js
[2025-07-21 13:54:46] https://**********/js/chunk-vendors.js
[2025-07-21 13:54:46] https://**********/js/gangkqytbInfo.js
[2025-07-21 13:54:46] https://**********/js/hangdycyjxx.js
[2025-07-21 13:54:46] https://**********/js/hangdyx.js
[2025-07-21 13:54:46] https://**********/js/hangdyx~login-app.js
[2025-07-21 13:54:46] https://**********/js/index.js
[2025-07-21 13:54:46] https://**********/supermap/leaflet-heat.js
[2025-07-21 13:54:46] https://**********/output/i18n/zh-cn.json?timestamp=1753077282192
[2025-07-21 13:54:46] https://**********/js/search-deatils.js
[2025-07-21 13:54:46] ---站内资源URL列表结束---
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/map~search-deatils~xianq-assistlist.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/tongjbg~yingjdw~yingjsj~yingjzy~yjct~yjfj~yjry~yjss.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/xianq-assistlist.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/xianq-prediction.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/yingjsj.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/yingjzy.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/yjry.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/supermap/Markerext.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/supermap/iclient-leaflet-es6.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/change-phone-num~forget-password.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/channel-operation~xianq-assistlist.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/map~search-deatils.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/sihncgl-yilyd-da-detail.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/yjfj.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/index.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/supermap/leaflet-heat.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/output/@datav/utils/dist/app.bundle.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/chunk-vendors.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/gangkqytbInfo.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/hangdycyjxx.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/hangdyx.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/hangdyx~login-app.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/search-deatils.js
[2025-07-21 13:54:46] [crawler] 请求附加站内资源: https://**********/output/i18n/zh-cn.json?timestamp=1753077282192
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/hangdxq.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/home.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/sihncgl-yilyd-da.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/tongjbg.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/supermap/leaflet.css
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/map~qiaosjgjc.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/xianq-track.js
[2025-07-21 13:54:46] [crawler] 请求附加站内资源: https://**********/favicon.ico
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/supermap/MarkerCluster.css
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/change-phone-num.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/channel-operation~xianq-detail.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/jiaotwlbtbc.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/login-app.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/map~sfzmap-list.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/search-plai.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/bind-phone-num.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/kuaqyxz.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/map.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/map~xianq-assistlist.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/qiaosjgjc.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/zhongyzj.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/jiaotwlbtbc~yingjsj.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/message.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/shebgx.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/tongjfx.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/xianq-detail.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/zhxzzf.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/supermap/MarkerCluster.Default.css
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/supermap/leaflet.markercluster.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/channel-operation~jiaotwlbtbc~qiaosjgjc~tongjfx~xianq-assistlist~zhongdjsgc.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/hangdycjcyj~map~sfzmap-list~video~video-list~xianq-assistlist.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/map~video.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/yingjdw.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/zhujhy.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/forget-password.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/gangkqytb.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/gangkqytbEdit.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/open-page.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/qiaosjgjc~xianq-assistlist~zhongdjsgc.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/xianq-history',.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/yjss.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/app.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/channel-operation.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/detail.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/hangdycjcyj.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/map~sfzmap-list~video~video-list.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/video-list.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/xinxlssgl.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/supermap/iclient-leaflet.css
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/supermap/leaflet.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/supermap/jquery.mini.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/hangdyxjcsj.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/qxzhyj.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/sihncgl-yilyd-xm.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/sihncgl-yilyd.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/xianq-track~xinxlssgl.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/hangdycyjxx~hangdyxjcsj.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/sfzmap-list.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/video.js
[2025-07-21 13:54:46] [crawler] 跳过图片资源: https://**********/img/login-bg.f8fd98e6.png
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/xianq-search.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/xianq-send.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/yjct.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/gangkqytbDetail.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/haisyjml.js
[2025-07-21 13:54:46] [crawler] 跳过已自动获取的资源类型: https://**********/js/zhongdjsgc.js
[2025-07-21 13:54:46] [crawler] 摘要: 标题: [36m登录[0m, 获取了 2 个资源，合并后总大小: 835347 字节
[2025-07-21 13:54:49] [Finger] https://**********/ [200] [835347] [登录] [jQuery | 登录界面 | Exposure-NuGet-API-Key | 视频监控联网平台 | Nginx | JavaScript-App]
[2025-07-21 14:01:55] [crawler] 启用无头浏览器访问: https://**********/
[2025-07-21 14:01:55] [crawler] 等待时间设置为 15 秒
[2025-07-21 14:01:55] [crawler] 开始执行爬虫任务...
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/ [方法: GET]
[2025-07-21 14:01:56] [爬虫请求] https://**********/ [状态码: 200]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/supermap/leaflet.css [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/supermap/iclient-leaflet.css [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/supermap/MarkerCluster.Default.css [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/supermap/MarkerCluster.css [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/supermap/leaflet.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/supermap/jquery.mini.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/supermap/Markerext.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/supermap/leaflet-heat.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/output/@datav/utils/dist/app.bundle.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/supermap/iclient-leaflet-es6.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/supermap/leaflet.markercluster.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://res.wx.qq.com/open/js/jweixin-1.3.2.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/app.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/chunk-vendors.js [方法: GET]
[2025-07-21 14:01:56] [爬虫请求] https://**********/supermap/leaflet.css [状态码: 200]
[2025-07-21 14:01:56] [爬虫请求] https://**********/supermap/iclient-leaflet.css [状态码: 200]
[2025-07-21 14:01:56] [爬虫请求] https://**********/supermap/MarkerCluster.css [状态码: 200]
[2025-07-21 14:01:56] [爬虫请求] https://**********/supermap/MarkerCluster.Default.css [状态码: 200]
[2025-07-21 14:01:56] [爬虫请求] https://**********/supermap/Markerext.js [状态码: 200]
[2025-07-21 14:01:56] [爬虫请求] https://**********/supermap/jquery.mini.js [状态码: 200]
[2025-07-21 14:01:56] [爬虫请求] https://**********/supermap/leaflet-heat.js [状态码: 200]
[2025-07-21 14:01:56] [爬虫请求] https://**********/supermap/leaflet.js [状态码: 200]
[2025-07-21 14:01:56] [爬虫请求] https://**********/output/@datav/utils/dist/app.bundle.js [状态码: 200]
[2025-07-21 14:01:56] [爬虫请求] https://res.wx.qq.com/open/js/jweixin-1.3.2.js [状态码: 200]
[2025-07-21 14:01:56] [爬虫请求] https://**********/supermap/leaflet.markercluster.js [状态码: 200]
[2025-07-21 14:01:56] [爬虫请求] https://**********/js/app.js [状态码: 200]
[2025-07-21 14:01:56] [爬虫请求] https://**********/supermap/iclient-leaflet-es6.js [状态码: 200]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/bind-phone-num.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/change-phone-num.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/change-phone-num~forget-password.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/channel-operation.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/channel-operation~jiaotwlbtbc~qiaosjgjc~tongjfx~xianq-assistlist~zhongdjsgc.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/channel-operation~xianq-assistlist.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/channel-operation~xianq-detail.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/detail.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/forget-password.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/gangkqytb.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/gangkqytbDetail.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/gangkqytbEdit.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/gangkqytbInfo.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/haisyjml.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/hangdxq.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/hangdycjcyj.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/hangdycjcyj~map~sfzmap-list~video~video-list~xianq-assistlist.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/hangdycyjxx.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/hangdycyjxx~hangdyxjcsj.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/hangdyx.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/hangdyxjcsj.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/hangdyx~login-app.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/home.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/index.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/jiaotwlbtbc.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/jiaotwlbtbc~yingjsj.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/kuaqyxz.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/login-app.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/map.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/map~qiaosjgjc.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/map~search-deatils.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/map~search-deatils~xianq-assistlist.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/map~sfzmap-list.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/map~sfzmap-list~video~video-list.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/map~video.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/map~xianq-assistlist.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/message.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/open-page.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/qiaosjgjc.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/qiaosjgjc~xianq-assistlist~zhongdjsgc.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/qxzhyj.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/search-deatils.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/search-plai.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/sfzmap-list.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/shebgx.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/sihncgl-yilyd-da-detail.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/sihncgl-yilyd-da.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/sihncgl-yilyd-xm.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/sihncgl-yilyd.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/tongjbg.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/tongjbg~yingjdw~yingjsj~yingjzy~yjct~yjfj~yjry~yjss.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/tongjfx.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/video-list.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/video.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/xianq-assistlist.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/xianq-detail.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/xianq-history',.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/xianq-prediction.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/xianq-search.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/xianq-send.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/xianq-track.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/xianq-track~xinxlssgl.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/xinxlssgl.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/yingjdw.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/yingjsj.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/yingjzy.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/yjct.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/yjfj.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/yjry.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/yjss.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/zhongdjsgc.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/zhongyzj.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/zhujhy.js [方法: GET]
[2025-07-21 14:01:56] [爬虫发送请求] https://**********/js/zhxzzf.js [方法: GET]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/change-phone-num.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/change-phone-num~forget-password.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/channel-operation~jiaotwlbtbc~qiaosjgjc~tongjfx~xianq-assistlist~zhongdjsgc.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/bind-phone-num.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/channel-operation.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/chunk-vendors.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫发送请求] https://**********/output/i18n/zh-cn.json?timestamp=1753077717412 [方法: GET]
[2025-07-21 14:01:57] [爬虫发送请求] https://**********/js/hangdyx~login-app.js [方法: GET]
[2025-07-21 14:01:57] [爬虫发送请求] https://**********/js/login-app.js [方法: GET]
[2025-07-21 14:01:57] [爬虫发送请求] https://gjxxpt.mot.gov.cn/appProdservice/anon/emergency/jtcommon/commonQuerryByName [方法: OPTIONS]
[2025-07-21 14:01:57] [爬虫发送请求] https://gjxxpt.mot.gov.cn/appProdservice/nvwa/getLoginContext [方法: OPTIONS]
[2025-07-21 14:01:57] [爬虫发送请求] https://gjxxpt.mot.gov.cn/appProdservice/yxjc/app/userPhone/getIsTip [方法: OPTIONS]
[2025-07-21 14:01:57] [爬虫发送请求] https://gjxxpt.mot.gov.cn/appProdservice/anon/emergency/jtcommon/commonQuerryByName [方法: POST]
[2025-07-21 14:01:57] [爬虫发送请求] https://gjxxpt.mot.gov.cn/appProdservice/nvwa/getLoginContext [方法: GET]
[2025-07-21 14:01:57] [爬虫发送请求] https://gjxxpt.mot.gov.cn/appProdservice/yxjc/app/userPhone/getIsTip [方法: POST]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/gangkqytb.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/channel-operation~xianq-detail.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/detail.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/forget-password.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/channel-operation~xianq-assistlist.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/gangkqytbDetail.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/gangkqytbInfo.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/haisyjml.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/gangkqytbEdit.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/hangdxq.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/hangdycjcyj~map~sfzmap-list~video~video-list~xianq-assistlist.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/hangdycjcyj.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/hangdycyjxx~hangdyxjcsj.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/hangdyx.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/hangdyxjcsj.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/hangdyx~login-app.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/home.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/index.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/jiaotwlbtbc.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/kuaqyxz.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/jiaotwlbtbc~yingjsj.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/map~qiaosjgjc.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/login-app.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/map.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/map~search-deatils.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/map~search-deatils~xianq-assistlist.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/map~sfzmap-list.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/map~sfzmap-list~video~video-list.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/hangdyx~login-app.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/output/i18n/zh-cn.json?timestamp=1753077717412 [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://gjxxpt.mot.gov.cn/appProdservice/yxjc/app/userPhone/getIsTip [状态码: 204]
[2025-07-21 14:01:57] [爬虫请求] https://gjxxpt.mot.gov.cn/appProdservice/anon/emergency/jtcommon/commonQuerryByName [状态码: 204]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/map~video.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/login-app.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫发送请求] https://**********/favicon.ico [方法: GET]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/map~xianq-assistlist.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫发送请求] https://gjxxpt.mot.gov.cn/appProdservice/yxjc/app/userPhone/getAccountIsTip [方法: POST]
[2025-07-21 14:01:57] [爬虫发送请求] https://gjxxpt.mot.gov.cn/appProdservice/yxjc/app/userPhone/getAccountIsTip [方法: OPTIONS]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/message.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/open-page.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://gjxxpt.mot.gov.cn/appProdservice/anon/emergency/jtcommon/commonQuerryByName [状态码: 502]
[2025-07-21 14:01:57] [爬虫请求] https://gjxxpt.mot.gov.cn/appProdservice/yxjc/app/userPhone/getIsTip [状态码: 502]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/qiaosjgjc.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/qiaosjgjc~xianq-assistlist~zhongdjsgc.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/favicon.ico [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/qxzhyj.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/search-deatils.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/shebgx.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://gjxxpt.mot.gov.cn/appProdservice/nvwa/getLoginContext [状态码: 204]
[2025-07-21 14:01:57] [爬虫请求] https://gjxxpt.mot.gov.cn/appProdservice/yxjc/app/userPhone/getAccountIsTip [状态码: 204]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/search-plai.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/sihncgl-yilyd-da-detail.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/sfzmap-list.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/sihncgl-yilyd-da.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/sihncgl-yilyd-xm.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/sihncgl-yilyd.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/tongjbg~yingjdw~yingjsj~yingjzy~yjct~yjfj~yjry~yjss.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/tongjbg.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://gjxxpt.mot.gov.cn/appProdservice/yxjc/app/userPhone/getAccountIsTip [状态码: 502]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/tongjfx.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/video-list.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://gjxxpt.mot.gov.cn/appProdservice/nvwa/getLoginContext [状态码: 502]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/xianq-assistlist.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/video.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/xianq-detail.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/xianq-history',.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/xianq-search.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/xianq-prediction.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/xianq-send.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/xianq-track~xinxlssgl.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/xianq-track.js [状态码: 200]
[2025-07-21 14:01:57] [爬虫请求] https://**********/js/xinxlssgl.js [状态码: 200]
[2025-07-21 14:01:58] [爬虫请求] https://**********/js/yingjdw.js [状态码: 200]
[2025-07-21 14:01:58] [爬虫请求] https://**********/js/yingjsj.js [状态码: 200]
[2025-07-21 14:01:58] [爬虫请求] https://**********/js/yingjzy.js [状态码: 200]
[2025-07-21 14:01:58] [爬虫请求] https://**********/js/yjct.js [状态码: 200]
[2025-07-21 14:01:58] [爬虫请求] https://**********/js/yjry.js [状态码: 200]
[2025-07-21 14:01:58] [爬虫请求] https://**********/js/yjfj.js [状态码: 200]
[2025-07-21 14:01:58] [爬虫请求] https://**********/js/yjss.js [状态码: 200]
[2025-07-21 14:01:58] [爬虫请求] https://**********/js/zhongyzj.js [状态码: 200]
[2025-07-21 14:01:58] [爬虫请求] https://**********/js/zhongdjsgc.js [状态码: 200]
[2025-07-21 14:01:58] [爬虫请求] https://**********/js/zhujhy.js [状态码: 200]
[2025-07-21 14:01:58] [爬虫请求] https://**********/js/zhxzzf.js [状态码: 200]
[2025-07-21 14:01:58] [爬虫请求] https://**********/js/hangdycyjxx.js [状态码: 200]
[2025-07-21 14:02:01] [crawler] 完成初步爬取，页面标题: 登录, 内容长度: 767276
[2025-07-21 14:02:01] [crawler] 爬虫捕获到 101 个资源链接，其中 90 个属于当前站点
[2025-07-21 14:02:01] ---站内资源URL列表开始---
[2025-07-21 14:02:01] https://**********/js/channel-operation~xianq-detail.js
[2025-07-21 14:02:01] https://**********/js/hangdyxjcsj.js
[2025-07-21 14:02:01] https://**********/js/map~sfzmap-list.js
[2025-07-21 14:02:01] https://**********/js/hangdycjcyj~map~sfzmap-list~video~video-list~xianq-assistlist.js
[2025-07-21 14:02:01] https://**********/js/index.js
[2025-07-21 14:02:01] https://**********/js/map~video.js
[2025-07-21 14:02:01] https://**********/js/open-page.js
[2025-07-21 14:02:01] https://**********/js/shebgx.js
[2025-07-21 14:02:01] https://**********/output/i18n/zh-cn.json?timestamp=1753077717412
[2025-07-21 14:02:01] https://**********/js/tongjfx.js
[2025-07-21 14:02:01] https://**********/js/yjry.js
[2025-07-21 14:02:01] https://**********/supermap/jquery.mini.js
[2025-07-21 14:02:01] https://**********/supermap/leaflet-heat.js
[2025-07-21 14:02:01] https://**********/js/app.js
[2025-07-21 14:02:01] https://**********/js/map~sfzmap-list~video~video-list.js
[2025-07-21 14:02:01] https://**********/js/search-deatils.js
[2025-07-21 14:02:01] https://**********/js/tongjbg.js
[2025-07-21 14:02:01] https://**********/js/video-list.js
[2025-07-21 14:02:01] https://**********/js/xianq-assistlist.js
[2025-07-21 14:02:01] https://**********/supermap/leaflet.js
[2025-07-21 14:02:01] https://**********/supermap/leaflet.markercluster.js
[2025-07-21 14:02:01] https://**********/js/bind-phone-num.js
[2025-07-21 14:02:01] https://**********/js/hangdycjcyj.js
[2025-07-21 14:02:01] https://**********/js/hangdycyjxx~hangdyxjcsj.js
[2025-07-21 14:02:01] https://**********/js/sihncgl-yilyd-xm.js
[2025-07-21 14:02:01] https://**********/js/zhongyzj.js
[2025-07-21 14:02:01] https://**********/js/channel-operation.js
[2025-07-21 14:02:01] https://**********/js/channel-operation~jiaotwlbtbc~qiaosjgjc~tongjfx~xianq-assistlist~zhongdjsgc.js
[2025-07-21 14:02:01] https://**********/js/sihncgl-yilyd.js
[2025-07-21 14:02:01] https://**********/js/video.js
[2025-07-21 14:02:01] https://**********/js/xianq-search.js
[2025-07-21 14:02:01] https://**********/favicon.ico
[2025-07-21 14:02:01] https://**********/supermap/leaflet.css
[2025-07-21 14:02:01] https://**********/js/forget-password.js
[2025-07-21 14:02:01] https://**********/js/hangdycyjxx.js
[2025-07-21 14:02:01] https://**********/js/hangdyx.js
[2025-07-21 14:02:01] https://**********/js/sfzmap-list.js
[2025-07-21 14:02:01] https://**********/js/yjct.js
[2025-07-21 14:02:01] https://**********/js/channel-operation~xianq-assistlist.js
[2025-07-21 14:02:01] https://**********/js/kuaqyxz.js
[2025-07-21 14:02:01] https://**********/js/xianq-send.js
[2025-07-21 14:02:01] https://**********/js/yjfj.js
[2025-07-21 14:02:01] https://**********/js/yjss.js
[2025-07-21 14:02:01] https://**********/js/search-plai.js
[2025-07-21 14:02:01] https://**********/js/xianq-track.js
[2025-07-21 14:02:01] https://**********/supermap/MarkerCluster.Default.css
[2025-07-21 14:02:01] https://**********/supermap/MarkerCluster.css
[2025-07-21 14:02:01] https://**********/js/haisyjml.js
[2025-07-21 14:02:01] https://**********/js/hangdxq.js
[2025-07-21 14:02:01] https://**********/js/map.js
[2025-07-21 14:02:01] https://**********/js/message.js
[2025-07-21 14:02:01] https://**********/js/qxzhyj.js
[2025-07-21 14:02:01] https://**********/js/sihncgl-yilyd-da.js
[2025-07-21 14:02:01] https://**********/supermap/iclient-leaflet-es6.js
[2025-07-21 14:02:01] https://**********/js/change-phone-num.js
[2025-07-21 14:02:01] https://**********/js/gangkqytbDetail.js
[2025-07-21 14:02:01] https://**********/js/gangkqytbInfo.js
[2025-07-21 14:02:01] https://**********/js/hangdyx~login-app.js
[2025-07-21 14:02:01] https://**********/js/map~xianq-assistlist.js
[2025-07-21 14:02:01] https://**********/js/xianq-detail.js
[2025-07-21 14:02:01] https://**********/
[2025-07-21 14:02:01] https://**********/js/detail.js
[2025-07-21 14:02:01] https://**********/js/xinxlssgl.js
[2025-07-21 14:02:01] https://**********/js/yingjsj.js
[2025-07-21 14:02:01] https://**********/js/zhxzzf.js
[2025-07-21 14:02:01] https://**********/js/qiaosjgjc~xianq-assistlist~zhongdjsgc.js
[2025-07-21 14:02:01] https://**********/js/sihncgl-yilyd-da-detail.js
[2025-07-21 14:02:01] https://**********/js/tongjbg~yingjdw~yingjsj~yingjzy~yjct~yjfj~yjry~yjss.js
[2025-07-21 14:02:01] https://**********/js/yingjdw.js
[2025-07-21 14:02:01] https://**********/js/yingjzy.js
[2025-07-21 14:02:01] https://**********/js/zhongdjsgc.js
[2025-07-21 14:02:01] https://**********/js/change-phone-num~forget-password.js
[2025-07-21 14:02:01] https://**********/js/gangkqytbEdit.js
[2025-07-21 14:02:01] https://**********/js/jiaotwlbtbc.js
[2025-07-21 14:02:01] https://**********/js/xianq-history',.js
[2025-07-21 14:02:01] https://**********/js/xianq-track~xinxlssgl.js
[2025-07-21 14:02:01] https://**********/js/zhujhy.js
[2025-07-21 14:02:01] https://**********/supermap/iclient-leaflet.css
[2025-07-21 14:02:01] https://**********/js/map~search-deatils.js
[2025-07-21 14:02:01] https://**********/js/qiaosjgjc.js
[2025-07-21 14:02:01] https://**********/output/@datav/utils/dist/app.bundle.js
[2025-07-21 14:02:01] https://**********/js/home.js
[2025-07-21 14:02:01] https://**********/js/xianq-prediction.js
[2025-07-21 14:02:01] https://**********/js/map~qiaosjgjc.js
[2025-07-21 14:02:01] https://**********/js/map~search-deatils~xianq-assistlist.js
[2025-07-21 14:02:01] https://**********/supermap/Markerext.js
[2025-07-21 14:02:01] https://**********/js/chunk-vendors.js
[2025-07-21 14:02:01] https://**********/js/gangkqytb.js
[2025-07-21 14:02:01] https://**********/js/jiaotwlbtbc~yingjsj.js
[2025-07-21 14:02:01] https://**********/js/login-app.js
[2025-07-21 14:02:01] ---站内资源URL列表结束---
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/supermap/jquery.mini.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/supermap/leaflet-heat.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/app.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/map~sfzmap-list~video~video-list.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/search-deatils.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/tongjbg.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/tongjfx.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/yjry.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/supermap/leaflet.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/supermap/leaflet.markercluster.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/bind-phone-num.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/hangdycjcyj.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/hangdycyjxx~hangdyxjcsj.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/sihncgl-yilyd-xm.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/video-list.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/xianq-assistlist.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/zhongyzj.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/channel-operation.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/channel-operation~jiaotwlbtbc~qiaosjgjc~tongjfx~xianq-assistlist~zhongdjsgc.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/sihncgl-yilyd.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/video.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/xianq-search.js
[2025-07-21 14:02:01] [crawler] 请求附加站内资源: https://**********/favicon.ico
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/supermap/leaflet.css
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/forget-password.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/hangdycyjxx.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/hangdyx.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/sfzmap-list.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/yjct.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/channel-operation~xianq-assistlist.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/kuaqyxz.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/xianq-send.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/yjfj.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/yjss.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/supermap/MarkerCluster.Default.css
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/supermap/MarkerCluster.css
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/haisyjml.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/hangdxq.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/map.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/message.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/search-plai.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/xianq-track.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/supermap/iclient-leaflet-es6.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/change-phone-num.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/gangkqytbDetail.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/gangkqytbInfo.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/hangdyx~login-app.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/map~xianq-assistlist.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/qxzhyj.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/sihncgl-yilyd-da.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/xianq-detail.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/detail.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/xinxlssgl.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/yingjsj.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/qiaosjgjc~xianq-assistlist~zhongdjsgc.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/sihncgl-yilyd-da-detail.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/tongjbg~yingjdw~yingjsj~yingjzy~yjct~yjfj~yjry~yjss.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/yingjdw.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/yingjzy.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/zhongdjsgc.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/zhxzzf.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/change-phone-num~forget-password.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/gangkqytbEdit.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/jiaotwlbtbc.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/xianq-history',.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/xianq-track~xinxlssgl.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/zhujhy.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/supermap/iclient-leaflet.css
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/map~search-deatils.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/qiaosjgjc.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/output/@datav/utils/dist/app.bundle.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/home.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/xianq-prediction.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/map~qiaosjgjc.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/map~search-deatils~xianq-assistlist.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/supermap/Markerext.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/chunk-vendors.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/gangkqytb.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/jiaotwlbtbc~yingjsj.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/login-app.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/channel-operation~xianq-detail.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/hangdyxjcsj.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/map~sfzmap-list.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/hangdycjcyj~map~sfzmap-list~video~video-list~xianq-assistlist.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/index.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/map~video.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/open-page.js
[2025-07-21 14:02:01] [crawler] 跳过已自动获取的资源类型: https://**********/js/shebgx.js
[2025-07-21 14:02:01] [crawler] 请求附加站内资源: https://**********/output/i18n/zh-cn.json?timestamp=1753077717412
[2025-07-21 14:02:02] [crawler] 摘要: 标题: [36m登录[0m, 获取了 2 个资源，合并后总大小: 767276 字节
[2025-07-21 14:02:04] [Finger] https://**********/ [200] [767276] [登录] [jQuery | 登录界面 | Nginx | 视频监控联网平台 | Exposure-NuGet-API-Key | JavaScript-App]
[2025-07-21 17:50:10] [crawler] 启用无头浏览器访问: https://gjxxpt.mot.gov.cn/appProdservice-sjfx
[2025-07-21 17:50:10] [crawler] 等待时间设置为 15 秒
[2025-07-21 17:50:10] [crawler] 开始执行爬虫任务...
[2025-07-21 17:50:11] [爬虫发送请求] https://gjxxpt.mot.gov.cn/appProdservice-sjfx [方法: GET]
[2025-07-21 17:50:11] [爬虫发送请求] http://**********:30665/appProdservice-sjfx/ [方法: GET]
[2025-07-21 17:50:11] [爬虫请求] http://**********:30665/appProdservice-sjfx/ [状态码: 200]
[2025-07-21 17:50:11] [爬虫发送请求] http://**********:30665/appProdservice-sjfx/config/os.config.js [方法: GET]
[2025-07-21 17:50:11] [爬虫发送请求] http://**********:30665/appProdservice-sjfx/static/stage/text.min.js [方法: GET]
[2025-07-21 17:50:11] [爬虫发送请求] http://**********:30665/appProdservice-sjfx/static/stage/proxy.min.js [方法: GET]
[2025-07-21 17:50:11] [爬虫发送请求] http://**********:30665/appProdservice-sjfx/static/lib/jquery.min.js [方法: GET]
[2025-07-21 17:50:11] [爬虫发送请求] http://**********:30665/appProdservice-sjfx/output/@nvwa/login/dist/app.bundle.js?t=1729675749048 [方法: GET]
[2025-07-21 17:50:11] [爬虫发送请求] http://**********:30665/appProdservice-sjfx/output/@tocc/login-gj/dist/app.bundle.js?t=1752897326776 [方法: GET]
[2025-07-21 17:50:11] [爬虫发送请求] http://**********:30665/appProdservice-sjfx/js/main.05c85b5d.bundle.js [方法: GET]
[2025-07-21 17:50:11] [爬虫请求] http://**********:30665/appProdservice-sjfx/config/os.config.js [状态码: 200]
[2025-07-21 17:50:11] [爬虫请求] http://**********:30665/appProdservice-sjfx/static/stage/text.min.js [状态码: 200]
[2025-07-21 17:50:11] [爬虫请求] http://**********:30665/appProdservice-sjfx/static/stage/proxy.min.js [状态码: 200]
[2025-07-21 17:50:11] [爬虫请求] http://**********:30665/appProdservice-sjfx/output/@nvwa/login/dist/app.bundle.js?t=1729675749048 [状态码: 200]
[2025-07-21 17:50:11] [爬虫请求] http://**********:30665/appProdservice-sjfx/static/lib/jquery.min.js [状态码: 200]
[2025-07-21 17:50:11] [爬虫请求] http://**********:30665/appProdservice-sjfx/output/@tocc/login-gj/dist/app.bundle.js?t=1752897326776 [状态码: 200]
[2025-07-21 17:50:11] [爬虫请求] http://**********:30665/appProdservice-sjfx/js/main.05c85b5d.bundle.js [状态码: 200]
[2025-07-21 17:50:12] [爬虫发送请求] http://**********:30665/appProdservice-sjfx/css/nr-theme.css [方法: GET]
[2025-07-21 17:50:12] [爬虫发送请求] http://**********:30665/appProdservice-sjfx/output/i18n/zh-cn.json?timestamp=1752897326776 [方法: GET]
[2025-07-21 17:50:12] [爬虫请求] http://**********:30665/appProdservice-sjfx/css/nr-theme.css [状态码: 200]
[2025-07-21 17:50:12] [爬虫请求] http://**********:30665/appProdservice-sjfx/output/i18n/zh-cn.json?timestamp=1752897326776 [状态码: 200]
[2025-07-21 17:50:12] [爬虫发送请求] https://gjxxpt.mot.gov.cn/appProdservice/nvwa/getLoginContext [方法: GET]
[2025-07-21 17:50:12] [爬虫发送请求] https://gjxxpt.mot.gov.cn/appProdservice/nvwa-nros/subserver/config [方法: GET]
[2025-07-21 17:50:12] [爬虫发送请求] http://**********:30665/appProdservice-sjfx/output/@tocc/nvwa-ui/lib/NvwaUi/index.css [方法: GET]
[2025-07-21 17:50:12] [爬虫发送请求] https://gjxxpt.mot.gov.cn/appProdservice/nvwa/getLoginContext [方法: OPTIONS]
[2025-07-21 17:50:12] [爬虫发送请求] https://gjxxpt.mot.gov.cn/appProdservice/nvwa-nros/subserver/config [方法: OPTIONS]
[2025-07-21 17:50:12] [爬虫请求] http://**********:30665/appProdservice-sjfx/output/@tocc/nvwa-ui/lib/NvwaUi/index.css [状态码: 200]
[2025-07-21 17:50:12] [爬虫发送请求] http://**********:30665/appProdservice-sjfx/favicon.ico [方法: GET]
[2025-07-21 17:50:12] [爬虫请求] http://**********:30665/appProdservice-sjfx/favicon.ico [状态码: 404]
[2025-07-21 17:50:13] [爬虫请求] https://gjxxpt.mot.gov.cn/appProdservice/nvwa/getLoginContext [状态码: 204]
[2025-07-21 17:50:13] [爬虫请求] https://gjxxpt.mot.gov.cn/appProdservice/nvwa-nros/subserver/config [状态码: 204]
[2025-07-21 17:50:13] [爬虫发送请求] http://**********:30665/appProdservice-sjfx/output/applist.json?times=1752897326776 [方法: GET]
[2025-07-21 17:50:13] [爬虫发送请求] https://gjxxpt.mot.gov.cn/appProdservice/api/portal?loginPath=undefined [方法: GET]
[2025-07-21 17:50:13] [爬虫发送请求] https://gjxxpt.mot.gov.cn/appProdservice/api/portal?loginPath=undefined [方法: OPTIONS]
[2025-07-21 17:50:13] [爬虫请求] https://gjxxpt.mot.gov.cn/appProdservice/api/portal?loginPath=undefined [状态码: 204]
[2025-07-21 17:50:13] [爬虫请求] http://**********:30665/appProdservice-sjfx/output/applist.json?times=1752897326776 [状态码: 200]
[2025-07-21 17:50:13] [爬虫发送请求] https://gjxxpt.mot.gov.cn/appProdservice/api/routes [方法: OPTIONS]
[2025-07-21 17:50:13] [爬虫发送请求] https://gjxxpt.mot.gov.cn/appProdservice/api/portal?loginPath=undefined [方法: OPTIONS]
[2025-07-21 17:50:13] [爬虫发送请求] https://gjxxpt.mot.gov.cn/appProdservice/api/routes [方法: GET]
[2025-07-21 17:50:13] [爬虫发送请求] https://gjxxpt.mot.gov.cn/appProdservice/api/portal?loginPath=undefined [方法: GET]
[2025-07-21 17:50:13] [爬虫请求] https://gjxxpt.mot.gov.cn/appProdservice/api/routes [状态码: 204]
[2025-07-21 17:50:13] [爬虫请求] https://gjxxpt.mot.gov.cn/appProdservice/api/portal?loginPath=undefined [状态码: 204]
[2025-07-21 17:50:16] [crawler] 完成初步爬取，页面标题: , 内容长度: 858041
[2025-07-21 17:50:16] [crawler] 爬虫捕获到 24 个资源链接，其中 5 个属于当前站点
[2025-07-21 17:50:16] ---站内资源URL列表开始---
[2025-07-21 17:50:16] https://gjxxpt.mot.gov.cn/appProdservice-sjfx
[2025-07-21 17:50:16] https://gjxxpt.mot.gov.cn/appProdservice/nvwa/getLoginContext
[2025-07-21 17:50:16] https://gjxxpt.mot.gov.cn/appProdservice/nvwa-nros/subserver/config
[2025-07-21 17:50:16] https://gjxxpt.mot.gov.cn/appProdservice/api/portal?loginPath=undefined
[2025-07-21 17:50:16] https://gjxxpt.mot.gov.cn/appProdservice/api/routes
[2025-07-21 17:50:16] ---站内资源URL列表结束---
[2025-07-21 17:50:16] [crawler] 请求附加站内资源: https://gjxxpt.mot.gov.cn/appProdservice/nvwa/getLoginContext
[2025-07-21 17:50:16] [crawler] 请求附加站内资源: https://gjxxpt.mot.gov.cn/appProdservice/nvwa-nros/subserver/config
[2025-07-21 17:50:16] [crawler] 请求附加站内资源: https://gjxxpt.mot.gov.cn/appProdservice/api/portal?loginPath=undefined
[2025-07-21 17:50:16] [crawler] 请求附加站内资源: https://gjxxpt.mot.gov.cn/appProdservice/api/routes
[2025-07-21 17:50:16] [crawler] 摘要: 获取了 4 个资源，合并后总大小: 858041 字节
[2025-07-21 17:50:32] [Finger] https://gjxxpt.mot.gov.cn/appProdservice-sjfx [200] [858041] [] [Nginx | 登录界面 | 久其财务报表系统 | jQuery | JavaScript-App]
