package webscan

import (
	"Lightx/pkg/clients"
	"Lightx/pkg/utils"
	"context"
	"fmt"
	"net/http"
	"net/url"
	"os"
	"regexp"
	"strings"
	"time"

	"github.com/chromedp/cdproto/dom"
	"github.com/chromedp/cdproto/network"
	"github.com/chromedp/chromedp"
	"github.com/projectdiscovery/gologger"
)

// 需要启用爬虫的特征
var jsRequiredPatterns = []string{
	"doesn't work properly without JavaScript enabled",
	"Please enable JavaScript",
	"JavaScript is required",
	"Enable JavaScript",
	"requires JavaScript",
	"webpack app",
	"vue-router",
	"react-router",
	"angular",
}

// CrawlerResult 存储爬虫结果
type CrawlerResult struct {
	HTML          string
	Headers       map[string]string
	Title         string
	Resources     []string
	SiteResources map[string][]byte // 存储属于当前站点的资源内容
	SiteJSContent string            // 合并的JS内容
	SiteHTMLs     map[string]string // 存储站内页面的HTML内容
}

// NeedsCrawler 检查页面内容是否需要启用爬虫
func NeedsCrawler(body string) bool {
	// 检查特定的字符串模式
	for _, pattern := range jsRequiredPatterns {
		if strings.Contains(strings.ToLower(body), strings.ToLower(pattern)) {
			return true
		}
	}

	// 检查HTML结构中的特征
	if strings.Contains(body, "<noscript>") &&
		strings.Contains(body, "</noscript>") {
		// 如果noscript标签中包含提示信息
		noscriptStart := strings.Index(body, "<noscript>")
		noscriptEnd := strings.Index(body, "</noscript>")
		if noscriptStart >= 0 && noscriptEnd > noscriptStart {
			noscriptContent := body[noscriptStart+10 : noscriptEnd]
			if strings.Contains(strings.ToLower(noscriptContent), "javascript") ||
				strings.Contains(strings.ToLower(noscriptContent), "enable") {
				return true
			}
		}
	}

	// 检查页面是否包含Vue或React等现代框架的标志
	if strings.Contains(body, "id=\"app\"") ||
		strings.Contains(body, "id=\"root\"") ||
		strings.Contains(body, "vue.js") ||
		strings.Contains(body, "react.js") ||
		strings.Contains(body, "vue-router") ||
		strings.Contains(body, "vue.") ||
		strings.Contains(body, "vue-") {
		return true
	}

	// 检查页面是否有大量JS文件但内容很少
	jsCount := strings.Count(body, ".js")
	htmlContentSize := len(body) - strings.Count(body, "<script")*100
	if jsCount > 5 && htmlContentSize < 1000 && strings.Count(body, "<div") < 10 {
		return true
	}

	return false
}

// FetchWithCrawler 使用无头浏览器获取页面内容
func FetchWithCrawler(targetURL string, timeout int, client *http.Client, debugMode bool, logFile string) (*CrawlerResult, error) {
	// 不再输出启用信息，由调用方统一处理
	// fmt.Fprintln(os.Stdout, "[crawler] 启用无头浏览器访问:", targetURL)
	logDebug(logFile, debugMode, fmt.Sprintf("[crawler] 启用无头浏览器访问: %s\n", targetURL))

	// 解析URL以获取域名信息
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		logDebug(logFile, debugMode, fmt.Sprintf("[爬虫错误] 解析URL失败: %v\n", err))
		return nil, fmt.Errorf("解析URL失败: %v", err)
	}

	hostDomain := parsedURL.Hostname()

	// 设置更长的超时时间以确保页面加载完成
	ctxTimeout := timeout
	if ctxTimeout < 15 {
		ctxTimeout = 15 // 最少给15秒加载时间
	}

	if debugMode {
		logCrawlerInfo(fmt.Sprintf("等待时间设置为 %d 秒", ctxTimeout), debugMode)
		logDebug(logFile, debugMode, fmt.Sprintf("[crawler] 等待时间设置为 %d 秒\n", ctxTimeout))
	}

	// 设置超时上下文
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(ctxTimeout)*time.Second)
	defer cancel()

	// 检查client是否设置了代理
	var proxyURL string
	if client != nil && client.Transport != nil {
		if transport, ok := client.Transport.(*http.Transport); ok && transport.Proxy != nil {
			// 尝试获取代理URL
			if proxyFunc := transport.Proxy; proxyFunc != nil {
				if proxy, err := proxyFunc(&http.Request{URL: parsedURL}); err == nil && proxy != nil {
					proxyURL = proxy.String()
					if debugMode {
						logCrawlerInfo(fmt.Sprintf("使用代理: %s", proxyURL), debugMode)
					}
					logDebug(logFile, debugMode, fmt.Sprintf("[crawler] 使用代理: %s\n", proxyURL))
				}
			}
		}
	}

	// 创建无头浏览器选项
	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.Flag("headless", true),
		chromedp.Flag("disable-gpu", true),
		chromedp.Flag("no-sandbox", true),
		chromedp.Flag("disable-dev-shm-usage", true),
		chromedp.Flag("disable-web-security", false),                         // 禁用安全策略，允许跨域
		chromedp.Flag("disable-features", "IsolateOrigins,site-per-process"), // 禁用站点隔离
		chromedp.Flag("ignore-certificate-errors", true),                     // 忽略证书错误
		chromedp.WindowSize(1920, 1080),                                      // 设置较大的窗口尺寸
		chromedp.UserAgent(utils.RandomUA()),
	)

	// 如果有代理设置，添加到Chrome选项中
	if proxyURL != "" {
		opts = append(opts, chromedp.ProxyServer(proxyURL))
	}

	// 创建浏览器上下文
	allocCtx, cancel := chromedp.NewExecAllocator(ctx, opts...)
	defer cancel()

	// 创建新的Chrome实例，减少日志输出
	taskCtx, cancel := chromedp.NewContext(allocCtx, chromedp.WithLogf(func(format string, args ...interface{}) {
		if debugMode && (strings.Contains(format, "error") || strings.Contains(format, "failed")) {
			fmt.Fprintln(os.Stderr, fmt.Sprintf(format, args...))
			logDebug(logFile, debugMode, fmt.Sprintf("[爬虫ChromeDP] %s\n", fmt.Sprintf(format, args...)))
		}
	}))
	defer cancel()

	// 设置网络请求监听器
	headers := make(map[string]string)
	var resources []string
	// 用于存储ChromeDP已获取的资源内容
	resourceContent := make(map[string][]byte)

	// 统计请求数量，用于简化输出
	requestCount := 0
	successCount := 0
	jsCount := 0
	cssCount := 0
	imgCount := 0
	apiCount := 0

	chromedp.ListenTarget(taskCtx, func(ev interface{}) {
		switch e := ev.(type) {
		case *network.EventResponseReceived:
			if e.Response.URL == targetURL {
				for k, v := range e.Response.Headers {
					headers[k] = fmt.Sprintf("%v", v)
				}
			}
			// 调试模式下输出所有请求的状态码
			if debugMode && strings.HasPrefix(e.Response.URL, "http") {
				logDebug(logFile, true, fmt.Sprintf("[爬虫请求] %s [状态码: %d]\n", e.Response.URL, e.Response.Status))
				successCount++
			}

			// 存储所有响应数据
			if strings.HasPrefix(e.Response.URL, "http") && e.Response.Status == 200 {
				// 记录资源类型
				respType := e.Response.MimeType
				// 只保存JS和CSS资源
				if strings.Contains(respType, "javascript") ||
					strings.Contains(respType, "css") ||
					strings.HasSuffix(e.Response.URL, ".js") ||
					strings.HasSuffix(e.Response.URL, ".css") {
					// 获取响应体
					go func(requestID string, url string) {
						body, err := network.GetResponseBody(e.RequestID).Do(taskCtx)
						if err == nil && len(body) > 0 {
							resourceContent[url] = body
							if debugMode {
								logCrawlerInfo(fmt.Sprintf("自动捕获资源: %s [大小: %d 字节]", url, len(body)), debugMode)
							}
						}
					}(string(e.RequestID), e.Response.URL)
				}
			}
		case *network.EventRequestWillBeSent:
			resources = append(resources, e.Request.URL)
			requestCount++

			// 统计不同类型的资源
			if strings.HasSuffix(e.Request.URL, ".js") || strings.Contains(e.Request.URL, ".js?") {
				jsCount++
			} else if strings.HasSuffix(e.Request.URL, ".css") || strings.Contains(e.Request.URL, ".css?") {
				cssCount++
			} else if strings.HasSuffix(e.Request.URL, ".png") || strings.HasSuffix(e.Request.URL, ".jpg") ||
				strings.HasSuffix(e.Request.URL, ".gif") || strings.HasSuffix(e.Request.URL, ".ico") {
				imgCount++
			} else if strings.Contains(e.Request.URL, "/api/") || strings.Contains(e.Request.URL, "/server/") {
				apiCount++
			}

			// 调试模式下输出所有发送的请求
			if debugMode && strings.HasPrefix(e.Request.URL, "http") {
				logDebug(logFile, true, fmt.Sprintf("[爬虫发送请求] %s [方法: %s]\n", e.Request.URL, e.Request.Method))
			}
		}
	})

	// 存储页面内容和标题
	var html, title string

	if debugMode {
		logCrawlerInfo("开始执行爬虫任务...", debugMode)
		logDebug(logFile, debugMode, "[crawler] 开始执行爬虫任务...\n")
	}

	// 执行爬虫任务
	err = chromedp.Run(taskCtx,
		network.Enable(),
		chromedp.Navigate(targetURL),
		// 等待页面加载完成
		chromedp.WaitVisible("body", chromedp.ByQuery),
		// 等待JavaScript执行完成（等待2秒确保渲染）
		chromedp.Sleep(2*time.Second),
		// 滚动页面以触发懒加载内容
		chromedp.Evaluate(`window.scrollTo(0, document.body.scrollHeight)`, nil),
		// 再等待一段时间确保所有内容加载完成
		chromedp.Sleep(2*time.Second),
		// 获取完整DOM
		chromedp.ActionFunc(func(ctx context.Context) error {
			node, err := dom.GetDocument().Do(ctx)
			if err != nil {
				return err
			}
			html, err = dom.GetOuterHTML().WithNodeID(node.NodeID).Do(ctx)
			return err
		}),
		// 获取页面标题
		chromedp.Title(&title),
	)

	if err != nil {
		errMsg := fmt.Sprintf("初次爬取失败: %v，尝试重试", err)
		gologger.Warning().Msg(fmt.Sprintf("[crawler] %s", errMsg))
		logDebug(logFile, debugMode, "[爬虫警告] "+errMsg+"\n")
		return nil, fmt.Errorf("浏览器爬取失败: %v", err)
	}

	// 输出请求统计信息 - 保留这条关键信息
	if debugMode {
		fmt.Fprintln(os.Stdout, fmt.Sprintf("\n[crawler] 请求统计: 总计 %d 个请求 (JS: %d, CSS: %d, 图片: %d, API: %d)",
			requestCount, jsCount, cssCount, imgCount, apiCount))
	}

	if debugMode {
		infoMsg := fmt.Sprintf("完成初步爬取，页面标题: %s, 内容长度: %d", title, len(html))
		logCrawlerInfo(infoMsg, debugMode)
		logDebug(logFile, debugMode, "[crawler] "+infoMsg+"\n")
	}

	// 如果页面内容为空，尝试再等待并重新获取
	if html == "" || html == "<html><head></head><body></body></html>" {
		if debugMode {
			warnMsg := "页面内容为空，尝试再次爬取..."
			logCrawlerInfo(warnMsg, debugMode)
			logDebug(logFile, debugMode, "[爬虫警告] "+warnMsg+"\n")
		}

		err = chromedp.Run(taskCtx,
			// 再等待一段时间
			chromedp.Sleep(3*time.Second),
			// 重新获取DOM
			chromedp.ActionFunc(func(ctx context.Context) error {
				node, err := dom.GetDocument().Do(ctx)
				if err != nil {
					return err
				}
				html, err = dom.GetOuterHTML().WithNodeID(node.NodeID).Do(ctx)
				return err
			}),
			// 更新标题
			chromedp.Title(&title),
		)

		if err != nil {
			errMsg := fmt.Sprintf("重试爬取失败: %v", err)
			gologger.Warning().Msg(fmt.Sprintf("[crawler] %s", errMsg))
			logDebug(logFile, debugMode, "[爬虫警告] "+errMsg+"\n")
		} else if debugMode {
			infoMsg := fmt.Sprintf("重试爬取成功，页面标题: %s, 内容长度: %d", title, len(html))
			logCrawlerInfo(infoMsg, debugMode)
			logDebug(logFile, debugMode, "[crawler] "+infoMsg+"\n")
		}
	}

	// 过滤资源URL并获取站内资源
	filteredResources := make([]string, 0)
	siteResources := make(map[string][]byte)
	siteHTMLs := make(map[string]string)

	var jsContent strings.Builder

	// 创建一个队列用于BFS
	var uniqueURLs = make(map[string]bool)
	uniqueURLs[targetURL] = true // 标记初始URL为已访问

	// 首先收集所有站内URL
	for _, res := range resources {
		if strings.HasPrefix(res, "http") {
			filteredResources = append(filteredResources, res)

			// 检查是否属于当前站点
			resURL, err := url.Parse(res)
			if err != nil {
				continue
			}

			// 如果资源属于当前站点
			if resURL.Hostname() == hostDomain {
				// 避免重复请求
				if _, visited := uniqueURLs[res]; !visited {
					uniqueURLs[res] = true
				}
			}
		}
	}

	infoMsg := fmt.Sprintf("爬虫捕获到 %d 个资源链接，其中 %d 个属于当前站点", len(filteredResources), len(uniqueURLs))
	logCrawlerInfo(infoMsg, debugMode)
	logDebug(logFile, debugMode, "[crawler] "+infoMsg+"\n")

	if debugMode {
		// 记录所有捕获的资源URL
		logDebug(logFile, debugMode, "---站内资源URL列表开始---\n")
		for url := range uniqueURLs {
			logDebug(logFile, debugMode, url+"\n")
		}
		logDebug(logFile, debugMode, "---站内资源URL列表结束---\n")
	}

	// 处理Chrome已经自动获取的JS和CSS资源
	autoFetchedCount := 0
	for resURL, content := range resourceContent {
		// 检查是否属于当前站点
		resURLParsed, err := url.Parse(resURL)
		if err != nil || resURLParsed.Hostname() != hostDomain {
			continue
		}

		// 保存资源内容
		siteResources[resURL] = content
		autoFetchedCount++

		// 如果是JS文件，添加到JS内容中
		if strings.HasSuffix(resURL, ".js") || strings.Contains(resURL, ".js?") {
			jsContent.WriteString("\n// Auto-fetched: " + resURL + "\n")
			jsContent.WriteString(string(content))

			if debugMode {
				jsMsg := fmt.Sprintf("自动获取JS文件: %s [大小: %d 字节]", resURL, len(content))
				logCrawlerInfo(jsMsg, debugMode)
				logDebug(logFile, debugMode, "[crawler] "+jsMsg+"\n")
			}
		}
	}

	if autoFetchedCount > 0 && debugMode {
		logCrawlerInfo(fmt.Sprintf("爬虫自动获取了 %d 个站内JS/CSS资源", autoFetchedCount), debugMode)
	}

	// 请求站内资源（仅请求未自动获取的关键资源）
	count := 0
	visited := make(map[string]bool) // 跟踪已访问的URL
	maxRequestResources := 100       // 限制额外请求资源数量

	for resURL := range uniqueURLs {
		// 避免重复请求同一URL
		if visited[resURL] {
			continue
		}
		visited[resURL] = true

		// 如果资源已经通过Chrome自动获取，则跳过
		if _, ok := resourceContent[resURL]; ok {
			continue
		}

		if count > maxRequestResources { // 限制请求数量，避免过多请求
			limitMsg := fmt.Sprintf("已达到额外资源请求上限(%d)，停止获取更多资源", maxRequestResources)
			logCrawlerInfo(limitMsg, debugMode)
			logDebug(logFile, debugMode, "[crawler] "+limitMsg+"\n")
			break
		}

		// 跳过初始URL
		if resURL == targetURL {
			continue
		}

		// 检查资源类型
		isJSFile := strings.HasSuffix(resURL, ".js") || strings.Contains(resURL, ".js?")
		isImageFile := strings.HasSuffix(resURL, ".png") || strings.HasSuffix(resURL, ".jpg") ||
			strings.HasSuffix(resURL, ".jpeg") || strings.HasSuffix(resURL, ".gif") ||
			strings.HasSuffix(resURL, ".ico")
		isCSSFile := strings.HasSuffix(resURL, ".css") || strings.Contains(resURL, ".css?")

		// 跳过所有CSS和JS资源（已由Chrome自动获取）
		if isJSFile || isCSSFile {
			if debugMode {
				skipMsg := fmt.Sprintf("跳过已自动获取的资源类型: %s", resURL)
				logCrawlerInfo(skipMsg, debugMode)
				logDebug(logFile, debugMode, "[crawler] "+skipMsg+"\n")
			}
			continue
		}

		// 只过滤图片资源
		if isImageFile && !strings.Contains(resURL, "favicon.ico") {
			if debugMode {
				skipMsg := fmt.Sprintf("跳过图片资源: %s", resURL)
				logCrawlerInfo(skipMsg, debugMode)
				logDebug(logFile, debugMode, "[crawler] "+skipMsg+"\n")
			}
			continue
		}

		if debugMode {
			reqMsg := fmt.Sprintf("请求附加站内资源: %s", resURL)
			logCrawlerInfo(reqMsg, debugMode)
			logDebug(logFile, debugMode, "[crawler] "+reqMsg+"\n")
		}

		resp, body, err := clients.NewSimpleGetRequest(resURL, client)
		if err != nil || resp == nil {
			if debugMode {
				errMsg := fmt.Sprintf("请求站内资源失败: %s", resURL)
				logCrawlerInfo(errMsg, debugMode)
				logDebug(logFile, debugMode, "[爬虫警告] "+errMsg+"\n")
			}
			continue
		}

		// 保存资源内容
		siteResources[resURL] = body

		// 如果是HTML页面，保存内容并尝试提取更多URL，但只处理主要HTML页面
		contentType := resp.Header.Get("Content-Type")
		if strings.Contains(contentType, "text/html") {
			htmlContent := string(body)
			siteHTMLs[resURL] = htmlContent

			// 从HTML中提取更多URL，但控制深度
			if len(siteHTMLs) <= 2 { // 限制HTML页面数量
				extractedURLs := extractURLsFromHTML(htmlContent, hostDomain, parsedURL.Scheme)
				for _, u := range extractedURLs {
					if _, visited := uniqueURLs[u]; !visited && len(uniqueURLs) < 50 {
						uniqueURLs[u] = true
					}
				}

				if debugMode {
					htmlMsg := fmt.Sprintf("从HTML页面提取到 %d 个新URL: %s", len(extractedURLs), resURL)
					logCrawlerInfo(htmlMsg, debugMode)
					logDebug(logFile, debugMode, "[crawler] "+htmlMsg+"\n")
				}
			}
		}

		count++
	}

	// 最终摘要信息 - 保留这条关键信息，但简化内容并合并标题
	var titleInfo string
	if title != "" {
		// 使用青色(cyan)显示标题
		titleInfo = fmt.Sprintf("标题: \x1b[36m%s\x1b[0m, ", title)
	} else {
		titleInfo = ""
	}

	summaryMsg := fmt.Sprintf("摘要: %s获取了 %d 个资源，合并后总大小: %d 字节",
		titleInfo, len(siteResources), len(html)+jsContent.Len())
	if debugMode {
		fmt.Fprintln(os.Stdout, "[crawler]", summaryMsg)
	}
	logDebug(logFile, debugMode, "[crawler] "+summaryMsg+"\n")

	// 返回爬虫结果
	return &CrawlerResult{
		HTML:          html,
		Headers:       headers,
		Title:         title,
		Resources:     utils.RemoveDuplicates(filteredResources),
		SiteResources: siteResources,
		SiteJSContent: jsContent.String(),
		SiteHTMLs:     siteHTMLs,
	}, nil
}

// 从HTML中提取URL
func extractURLsFromHTML(html, hostDomain, scheme string) []string {
	var results []string

	// 简单的a标签href提取
	hrefRegex := regexp.MustCompile(`href=["']([^"']+)["']`)
	matches := hrefRegex.FindAllStringSubmatch(html, -1)

	for _, match := range matches {
		if len(match) >= 2 {
			href := match[1]

			// 处理相对路径
			if strings.HasPrefix(href, "/") {
				href = scheme + "://" + hostDomain + href
			} else if !strings.HasPrefix(href, "http") && !strings.HasPrefix(href, "//") {
				continue // 跳过非HTTP链接和相对路径
			}

			// 处理//开头的URL
			if strings.HasPrefix(href, "//") {
				href = scheme + ":" + href
			}

			// 只保留当前域名的URL
			parsedURL, err := url.Parse(href)
			if err == nil && parsedURL.Hostname() == hostDomain {
				results = append(results, href)
			}
		}
	}

	// 提取JavaScript中的URLs
	jsURLRegex := regexp.MustCompile(`["']((https?://|/)[^"']+\.(js|css|json|xml|html))["']`)
	jsMatches := jsURLRegex.FindAllStringSubmatch(html, -1)

	for _, match := range jsMatches {
		if len(match) >= 2 {
			jsURL := match[1]

			// 处理相对路径
			if strings.HasPrefix(jsURL, "/") {
				jsURL = scheme + "://" + hostDomain + jsURL
			} else if !strings.HasPrefix(jsURL, "http") {
				continue
			}

			// 只保留当前域名的URL
			parsedURL, err := url.Parse(jsURL)
			if err == nil && parsedURL.Hostname() == hostDomain {
				results = append(results, jsURL)
			}
		}
	}

	return utils.RemoveDuplicates(results)
}

// logDebug 将调试信息写入日志文件
func logDebug(logPath string, debugMode bool, message string) {
	if !debugMode || logPath == "" {
		return
	}

	f, err := os.OpenFile(logPath, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		fmt.Fprintln(os.Stderr, "无法写入爬虫日志:", err)
		return
	}
	defer f.Close()

	// 获取当前时间
	timestamp := time.Now().Format("2006-01-02 15:04:05")
	logMessage := fmt.Sprintf("[%s] %s", timestamp, message)

	if _, err := f.WriteString(logMessage); err != nil {
		fmt.Fprintln(os.Stderr, "写入爬虫日志失败:", err)
	}
}

// logCrawlerInfo 输出简洁的爬虫信息
func logCrawlerInfo(message string, debugMode bool) {
	if debugMode {
		// 调试模式下直接输出，但不输出"跳过"相关的信息
		if !strings.Contains(message, "跳过") {
			fmt.Fprintln(os.Stdout, "[crawler]", message)
		}
	} else {
		// 非调试模式下，只在特定情况输出重要信息
		if strings.HasPrefix(message, "爬虫摘要") ||
			strings.HasPrefix(message, "完成") ||
			strings.Contains(message, "成功获取页面") {
			fmt.Fprintln(os.Stdout, "[crawler]", message)
		}
	}
}
