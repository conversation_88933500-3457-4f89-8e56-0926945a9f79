# github.com/go-gl/gl v0.0.0-20211210172815-726fda9656d6
## explicit; go 1.12
github.com/go-gl/gl/v2.1/gl
github.com/go-gl/gl/v2.1/gl/KHR
# github.com/go-gl/glfw/v3.3/glfw v0.0.0-20220622232848-a6c407ee30a0
## explicit; go 1.10
github.com/go-gl/glfw/v3.3/glfw
github.com/go-gl/glfw/v3.3/glfw/glfw/deps
github.com/go-gl/glfw/v3.3/glfw/glfw/deps/glad
github.com/go-gl/glfw/v3.3/glfw/glfw/deps/mingw
github.com/go-gl/glfw/v3.3/glfw/glfw/deps/vs2008
github.com/go-gl/glfw/v3.3/glfw/glfw/include/GLFW
github.com/go-gl/glfw/v3.3/glfw/glfw/src
# github.com/golang/freetype v0.0.0-20170609003504-e2365dfdc4a0
## explicit
github.com/golang/freetype/raster
github.com/golang/freetype/truetype
# github.com/google/gxui v0.0.0-20151028112939-f85e0a97b3a4
## explicit
github.com/google/gxui
github.com/google/gxui/drivers/gl
github.com/google/gxui/drivers/gl/platform
github.com/google/gxui/gxfont
github.com/google/gxui/interval
github.com/google/gxui/math
github.com/google/gxui/mixins
github.com/google/gxui/mixins/base
github.com/google/gxui/mixins/outer
github.com/google/gxui/mixins/parts
github.com/google/gxui/samples/flags
github.com/google/gxui/themes/basic
github.com/google/gxui/themes/dark
github.com/google/gxui/themes/light
# github.com/googollee/go-socket.io v1.4.4
## explicit; go 1.13
github.com/googollee/go-socket.io
github.com/googollee/go-socket.io/engineio
github.com/googollee/go-socket.io/engineio/base
github.com/googollee/go-socket.io/engineio/packet
github.com/googollee/go-socket.io/engineio/payload
github.com/googollee/go-socket.io/engineio/transport
github.com/googollee/go-socket.io/engineio/transport/polling
github.com/googollee/go-socket.io/engineio/transport/websocket
github.com/googollee/go-socket.io/parser
# github.com/gopherjs/gopherjs v1.17.2
## explicit; go 1.17
github.com/gopherjs/gopherjs/js
# github.com/gorilla/websocket v1.4.2
## explicit; go 1.12
github.com/gorilla/websocket
# github.com/goxjs/gl v0.0.0-20210104184919-e3fafc6f8f2a
## explicit
github.com/goxjs/gl
# github.com/goxjs/glfw v0.0.0-20220119044647-4bcee99381f2
## explicit
github.com/goxjs/glfw
# github.com/huin/asn1ber v0.0.0-20120622192748-af09f62e6358
## explicit
github.com/huin/asn1ber
# github.com/icodeface/tls v0.0.0-20190904083142-17aec93c60e5
## explicit
github.com/icodeface/tls
github.com/icodeface/tls/internal/cpu
github.com/icodeface/tls/internal/x/crypto/chacha20poly1305
github.com/icodeface/tls/internal/x/crypto/cryptobyte
github.com/icodeface/tls/internal/x/crypto/cryptobyte/asn1
github.com/icodeface/tls/internal/x/crypto/curve25519
github.com/icodeface/tls/internal/x/crypto/hkdf
github.com/icodeface/tls/internal/x/crypto/internal/chacha20
github.com/icodeface/tls/internal/x/crypto/poly1305
# github.com/lunixbochs/struc v0.0.0-20200707160740-784aaebc1d40
## explicit; go 1.12
github.com/lunixbochs/struc
# github.com/shirou/w32 v0.0.0-20160930032740-bb4de0191aa4
## explicit
github.com/shirou/w32
# github.com/stretchr/testify v1.5.1
## explicit; go 1.13
# github.com/tomatome/win v0.3.1
## explicit
github.com/tomatome/win
# golang.org/x/crypto v0.0.0-20220622213112-05595931fe9d
## explicit; go 1.17
golang.org/x/crypto/md4
# golang.org/x/image v0.0.0-20220617043117-41969df76e82
## explicit; go 1.12
golang.org/x/image/font
golang.org/x/image/math/fixed
# honnef.co/go/js/dom v0.0.0-20210725211120-f030747120f2
## explicit
honnef.co/go/js/dom
