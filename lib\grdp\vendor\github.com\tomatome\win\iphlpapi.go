// This file was automatically generated by https://github.com/kbinani/win/blob/generator/internal/cmd/gen/gen.go
// go run internal/cmd/gen/gen.go

// +build windows

package win

import (
	"syscall"
	"unsafe"
)

var (
	// Library
	libiphlpapi uintptr

	// Functions
	addIPAddress                            uintptr
	cancelIPChangeNotify                    uintptr
	createIpForwardEntry                    uintptr
	createIpNetEntry                        uintptr
	createPersistentTcpPortReservation      uintptr
	createPersistentUdpPortReservation      uintptr
	createProxyArpEntry                     uintptr
	deleteIPAddress                         uintptr
	deleteIpForwardEntry                    uintptr
	deleteIpNetEntry                        uintptr
	deletePersistentTcpPortReservation      uintptr
	deletePersistentUdpPortReservation      uintptr
	deleteProxyArpEntry                     uintptr
	disableMediaSense                       uintptr
	enableRouter                            uintptr
	flushIpNetTable                         uintptr
	getAdapterIndex                         uintptr
	getAdapterOrderMap                      uintptr
	getAdaptersAddresses                    uintptr
	getAdaptersInfo                         uintptr
	getBestInterface                        uintptr
	getBestInterfaceEx                      uintptr
	getBestRoute                            uintptr
	getExtendedTcpTable                     uintptr
	getExtendedUdpTable                     uintptr
	getFriendlyIfIndex                      uintptr
	getIcmpStatistics                       uintptr
	getIcmpStatisticsEx                     uintptr
	getIfEntry                              uintptr
	getIfTable                              uintptr
	getInterfaceInfo                        uintptr
	getIpAddrTable                          uintptr
	getIpErrorString                        uintptr
	getIpForwardTable                       uintptr
	getIpNetTable                           uintptr
	getIpStatistics                         uintptr
	getIpStatisticsEx                       uintptr
	getNetworkParams                        uintptr
	getNumberOfInterfaces                   uintptr
	getOwnerModuleFromTcp6Entry             uintptr
	getOwnerModuleFromTcpEntry              uintptr
	getOwnerModuleFromUdp6Entry             uintptr
	getOwnerModuleFromUdpEntry              uintptr
	getPerAdapterInfo                       uintptr
	getPerTcp6ConnectionEStats              uintptr
	getPerTcpConnectionEStats               uintptr
	getRTTAndHopCount                       uintptr
	getTcp6Table                            uintptr
	getTcp6Table2                           uintptr
	getTcpStatistics                        uintptr
	getTcpStatisticsEx                      uintptr
	getTcpTable                             uintptr
	getTcpTable2                            uintptr
	getUdp6Table                            uintptr
	getUdpStatistics                        uintptr
	getUdpStatisticsEx                      uintptr
	getUdpTable                             uintptr
	getUniDirectionalAdapterInfo            uintptr
	icmp6CreateFile                         uintptr
	icmp6ParseReplies                       uintptr
	icmp6SendEcho2                          uintptr
	icmpCloseHandle                         uintptr
	icmpCreateFile                          uintptr
	icmpParseReplies                        uintptr
	icmpSendEcho                            uintptr
	icmpSendEcho2                           uintptr
	ipReleaseAddress                        uintptr
	ipRenewAddress                          uintptr
	lookupPersistentTcpPortReservation      uintptr
	lookupPersistentUdpPortReservation      uintptr
	nhpAllocateAndGetInterfaceInfoFromStack uintptr
	notifyAddrChange                        uintptr
	notifyRouteChange                       uintptr
	parseNetworkString                      uintptr
	resolveNeighbor                         uintptr
	restoreMediaSense                       uintptr
	sendARP                                 uintptr
	setIfEntry                              uintptr
	setIpForwardEntry                       uintptr
	setIpNetEntry                           uintptr
	setIpStatistics                         uintptr
	setIpStatisticsEx                       uintptr
	setIpTTL                                uintptr
	setPerTcp6ConnectionEStats              uintptr
	setPerTcpConnectionEStats               uintptr
	setTcpEntry                             uintptr
	unenableRouter                          uintptr
	allocateAndGetIfTableFromStack          uintptr
	allocateAndGetIpAddrTableFromStack      uintptr
	allocateAndGetIpForwardTableFromStack   uintptr
	allocateAndGetIpNetTableFromStack       uintptr
	allocateAndGetTcpTableFromStack         uintptr
	allocateAndGetUdpTableFromStack         uintptr
	cancelMibChangeNotify2                  uintptr
	convertInterfaceGuidToLuid              uintptr
	convertInterfaceIndexToLuid             uintptr
	convertInterfaceLuidToGuid              uintptr
	convertInterfaceLuidToIndex             uintptr
	convertInterfaceLuidToName              uintptr
	convertInterfaceNameToLuid              uintptr
	createSortedAddressPairs                uintptr
	freeMibTable                            uintptr
	getIfEntry2                             uintptr
	getIfTable2                             uintptr
	icmpSendEcho2Ex                         uintptr
	notifyIpInterfaceChange                 uintptr
	pfBindInterfaceToIPAddress              uintptr
	pfCreateInterface                       uintptr
	pfDeleteInterface                       uintptr
	pfUnBindInterface                       uintptr
)

func init() {
	// Library
	libiphlpapi = doLoadLibrary("iphlpapi.dll")

	// Functions
	addIPAddress = doGetProcAddress(libiphlpapi, "AddIPAddress")
	cancelIPChangeNotify = doGetProcAddress(libiphlpapi, "CancelIPChangeNotify")
	createIpForwardEntry = doGetProcAddress(libiphlpapi, "CreateIpForwardEntry")
	createIpNetEntry = doGetProcAddress(libiphlpapi, "CreateIpNetEntry")
	createPersistentTcpPortReservation = doGetProcAddress(libiphlpapi, "CreatePersistentTcpPortReservation")
	createPersistentUdpPortReservation = doGetProcAddress(libiphlpapi, "CreatePersistentUdpPortReservation")
	createProxyArpEntry = doGetProcAddress(libiphlpapi, "CreateProxyArpEntry")
	deleteIPAddress = doGetProcAddress(libiphlpapi, "DeleteIPAddress")
	deleteIpForwardEntry = doGetProcAddress(libiphlpapi, "DeleteIpForwardEntry")
	deleteIpNetEntry = doGetProcAddress(libiphlpapi, "DeleteIpNetEntry")
	deletePersistentTcpPortReservation = doGetProcAddress(libiphlpapi, "DeletePersistentTcpPortReservation")
	deletePersistentUdpPortReservation = doGetProcAddress(libiphlpapi, "DeletePersistentUdpPortReservation")
	deleteProxyArpEntry = doGetProcAddress(libiphlpapi, "DeleteProxyArpEntry")
	disableMediaSense = doGetProcAddress(libiphlpapi, "DisableMediaSense")
	enableRouter = doGetProcAddress(libiphlpapi, "EnableRouter")
	flushIpNetTable = doGetProcAddress(libiphlpapi, "FlushIpNetTable")
	getAdapterIndex = doGetProcAddress(libiphlpapi, "GetAdapterIndex")
	getAdapterOrderMap = doGetProcAddress(libiphlpapi, "GetAdapterOrderMap")
	getAdaptersAddresses = doGetProcAddress(libiphlpapi, "GetAdaptersAddresses")
	getAdaptersInfo = doGetProcAddress(libiphlpapi, "GetAdaptersInfo")
	getBestInterface = doGetProcAddress(libiphlpapi, "GetBestInterface")
	getBestInterfaceEx = doGetProcAddress(libiphlpapi, "GetBestInterfaceEx")
	getBestRoute = doGetProcAddress(libiphlpapi, "GetBestRoute")
	getExtendedTcpTable = doGetProcAddress(libiphlpapi, "GetExtendedTcpTable")
	getExtendedUdpTable = doGetProcAddress(libiphlpapi, "GetExtendedUdpTable")
	getFriendlyIfIndex = doGetProcAddress(libiphlpapi, "GetFriendlyIfIndex")
	getIcmpStatistics = doGetProcAddress(libiphlpapi, "GetIcmpStatistics")
	getIcmpStatisticsEx = doGetProcAddress(libiphlpapi, "GetIcmpStatisticsEx")
	getIfEntry = doGetProcAddress(libiphlpapi, "GetIfEntry")
	getIfTable = doGetProcAddress(libiphlpapi, "GetIfTable")
	getInterfaceInfo = doGetProcAddress(libiphlpapi, "GetInterfaceInfo")
	getIpAddrTable = doGetProcAddress(libiphlpapi, "GetIpAddrTable")
	getIpErrorString = doGetProcAddress(libiphlpapi, "GetIpErrorString")
	getIpForwardTable = doGetProcAddress(libiphlpapi, "GetIpForwardTable")
	getIpNetTable = doGetProcAddress(libiphlpapi, "GetIpNetTable")
	getIpStatistics = doGetProcAddress(libiphlpapi, "GetIpStatistics")
	getIpStatisticsEx = doGetProcAddress(libiphlpapi, "GetIpStatisticsEx")
	getNetworkParams = doGetProcAddress(libiphlpapi, "GetNetworkParams")
	getNumberOfInterfaces = doGetProcAddress(libiphlpapi, "GetNumberOfInterfaces")
	getOwnerModuleFromTcp6Entry = doGetProcAddress(libiphlpapi, "GetOwnerModuleFromTcp6Entry")
	getOwnerModuleFromTcpEntry = doGetProcAddress(libiphlpapi, "GetOwnerModuleFromTcpEntry")
	getOwnerModuleFromUdp6Entry = doGetProcAddress(libiphlpapi, "GetOwnerModuleFromUdp6Entry")
	getOwnerModuleFromUdpEntry = doGetProcAddress(libiphlpapi, "GetOwnerModuleFromUdpEntry")
	getPerAdapterInfo = doGetProcAddress(libiphlpapi, "GetPerAdapterInfo")
	getPerTcp6ConnectionEStats = doGetProcAddress(libiphlpapi, "GetPerTcp6ConnectionEStats")
	getPerTcpConnectionEStats = doGetProcAddress(libiphlpapi, "GetPerTcpConnectionEStats")
	getRTTAndHopCount = doGetProcAddress(libiphlpapi, "GetRTTAndHopCount")
	getTcp6Table = doGetProcAddress(libiphlpapi, "GetTcp6Table")
	getTcp6Table2 = doGetProcAddress(libiphlpapi, "GetTcp6Table2")
	getTcpStatistics = doGetProcAddress(libiphlpapi, "GetTcpStatistics")
	getTcpStatisticsEx = doGetProcAddress(libiphlpapi, "GetTcpStatisticsEx")
	getTcpTable = doGetProcAddress(libiphlpapi, "GetTcpTable")
	getTcpTable2 = doGetProcAddress(libiphlpapi, "GetTcpTable2")
	getUdp6Table = doGetProcAddress(libiphlpapi, "GetUdp6Table")
	getUdpStatistics = doGetProcAddress(libiphlpapi, "GetUdpStatistics")
	getUdpStatisticsEx = doGetProcAddress(libiphlpapi, "GetUdpStatisticsEx")
	getUdpTable = doGetProcAddress(libiphlpapi, "GetUdpTable")
	getUniDirectionalAdapterInfo = doGetProcAddress(libiphlpapi, "GetUniDirectionalAdapterInfo")
	icmp6CreateFile = doGetProcAddress(libiphlpapi, "Icmp6CreateFile")
	icmp6ParseReplies = doGetProcAddress(libiphlpapi, "Icmp6ParseReplies")
	icmp6SendEcho2 = doGetProcAddress(libiphlpapi, "Icmp6SendEcho2")
	icmpCloseHandle = doGetProcAddress(libiphlpapi, "IcmpCloseHandle")
	icmpCreateFile = doGetProcAddress(libiphlpapi, "IcmpCreateFile")
	icmpParseReplies = doGetProcAddress(libiphlpapi, "IcmpParseReplies")
	icmpSendEcho = doGetProcAddress(libiphlpapi, "IcmpSendEcho")
	icmpSendEcho2 = doGetProcAddress(libiphlpapi, "IcmpSendEcho2")
	ipReleaseAddress = doGetProcAddress(libiphlpapi, "IpReleaseAddress")
	ipRenewAddress = doGetProcAddress(libiphlpapi, "IpRenewAddress")
	lookupPersistentTcpPortReservation = doGetProcAddress(libiphlpapi, "LookupPersistentTcpPortReservation")
	lookupPersistentUdpPortReservation = doGetProcAddress(libiphlpapi, "LookupPersistentUdpPortReservation")
	nhpAllocateAndGetInterfaceInfoFromStack = doGetProcAddress(libiphlpapi, "NhpAllocateAndGetInterfaceInfoFromStack")
	notifyAddrChange = doGetProcAddress(libiphlpapi, "NotifyAddrChange")
	notifyRouteChange = doGetProcAddress(libiphlpapi, "NotifyRouteChange")
	parseNetworkString = doGetProcAddress(libiphlpapi, "ParseNetworkString")
	resolveNeighbor = doGetProcAddress(libiphlpapi, "ResolveNeighbor")
	restoreMediaSense = doGetProcAddress(libiphlpapi, "RestoreMediaSense")
	sendARP = doGetProcAddress(libiphlpapi, "SendARP")
	setIfEntry = doGetProcAddress(libiphlpapi, "SetIfEntry")
	setIpForwardEntry = doGetProcAddress(libiphlpapi, "SetIpForwardEntry")
	setIpNetEntry = doGetProcAddress(libiphlpapi, "SetIpNetEntry")
	setIpStatistics = doGetProcAddress(libiphlpapi, "SetIpStatistics")
	setIpStatisticsEx = doGetProcAddress(libiphlpapi, "SetIpStatisticsEx")
	setIpTTL = doGetProcAddress(libiphlpapi, "SetIpTTL")
	setPerTcp6ConnectionEStats = doGetProcAddress(libiphlpapi, "SetPerTcp6ConnectionEStats")
	setPerTcpConnectionEStats = doGetProcAddress(libiphlpapi, "SetPerTcpConnectionEStats")
	setTcpEntry = doGetProcAddress(libiphlpapi, "SetTcpEntry")
	unenableRouter = doGetProcAddress(libiphlpapi, "UnenableRouter")
	allocateAndGetIfTableFromStack = doGetProcAddress(libiphlpapi, "AllocateAndGetIfTableFromStack")
	allocateAndGetIpAddrTableFromStack = doGetProcAddress(libiphlpapi, "AllocateAndGetIpAddrTableFromStack")
	allocateAndGetIpForwardTableFromStack = doGetProcAddress(libiphlpapi, "AllocateAndGetIpForwardTableFromStack")
	allocateAndGetIpNetTableFromStack = doGetProcAddress(libiphlpapi, "AllocateAndGetIpNetTableFromStack")
	allocateAndGetTcpTableFromStack = doGetProcAddress(libiphlpapi, "AllocateAndGetTcpTableFromStack")
	allocateAndGetUdpTableFromStack = doGetProcAddress(libiphlpapi, "AllocateAndGetUdpTableFromStack")
	cancelMibChangeNotify2 = doGetProcAddress(libiphlpapi, "CancelMibChangeNotify2")
	convertInterfaceGuidToLuid = doGetProcAddress(libiphlpapi, "ConvertInterfaceGuidToLuid")
	convertInterfaceIndexToLuid = doGetProcAddress(libiphlpapi, "ConvertInterfaceIndexToLuid")
	convertInterfaceLuidToGuid = doGetProcAddress(libiphlpapi, "ConvertInterfaceLuidToGuid")
	convertInterfaceLuidToIndex = doGetProcAddress(libiphlpapi, "ConvertInterfaceLuidToIndex")
	convertInterfaceLuidToName = doGetProcAddress(libiphlpapi, "ConvertInterfaceLuidToNameW")
	convertInterfaceNameToLuid = doGetProcAddress(libiphlpapi, "ConvertInterfaceNameToLuidW")
	createSortedAddressPairs = doGetProcAddress(libiphlpapi, "CreateSortedAddressPairs")
	freeMibTable = doGetProcAddress(libiphlpapi, "FreeMibTable")
	getIfEntry2 = doGetProcAddress(libiphlpapi, "GetIfEntry2")
	getIfTable2 = doGetProcAddress(libiphlpapi, "GetIfTable2")
	icmpSendEcho2Ex = doGetProcAddress(libiphlpapi, "IcmpSendEcho2Ex")
	notifyIpInterfaceChange = doGetProcAddress(libiphlpapi, "NotifyIpInterfaceChange")
	pfBindInterfaceToIPAddress = doGetProcAddress(libiphlpapi, "PfBindInterfaceToIPAddress")
	pfCreateInterface = doGetProcAddress(libiphlpapi, "PfCreateInterface")
	pfDeleteInterface = doGetProcAddress(libiphlpapi, "PfDeleteInterface")
	pfUnBindInterface = doGetProcAddress(libiphlpapi, "PfUnBindInterface")
}

func AddIPAddress(address IPAddr, ipMask IPMask, ifIndex DWORD, nTEContext *uint32, nTEInstance *uint32) DWORD {
	ret1 := syscall6(addIPAddress, 5,
		uintptr(address),
		uintptr(ipMask),
		uintptr(ifIndex),
		uintptr(unsafe.Pointer(nTEContext)),
		uintptr(unsafe.Pointer(nTEInstance)),
		0)
	return DWORD(ret1)
}

func CancelIPChangeNotify(notifyOverlapped *OVERLAPPED) bool {
	ret1 := syscall3(cancelIPChangeNotify, 1,
		uintptr(unsafe.Pointer(notifyOverlapped)),
		0,
		0)
	return ret1 != 0
}

func CreateIpForwardEntry(pRoute PMIB_IPFORWARDROW) DWORD {
	ret1 := syscall3(createIpForwardEntry, 1,
		uintptr(unsafe.Pointer(pRoute)),
		0,
		0)
	return DWORD(ret1)
}

func CreateIpNetEntry(pArpEntry PMIB_IPNETROW) DWORD {
	ret1 := syscall3(createIpNetEntry, 1,
		uintptr(unsafe.Pointer(pArpEntry)),
		0,
		0)
	return DWORD(ret1)
}

func CreatePersistentTcpPortReservation(startPort USHORT, numberOfPorts USHORT, token PULONG64) ULONG {
	ret1 := syscall3(createPersistentTcpPortReservation, 3,
		uintptr(startPort),
		uintptr(numberOfPorts),
		uintptr(unsafe.Pointer(token)))
	return ULONG(ret1)
}

func CreatePersistentUdpPortReservation(startPort USHORT, numberOfPorts USHORT, token PULONG64) ULONG {
	ret1 := syscall3(createPersistentUdpPortReservation, 3,
		uintptr(startPort),
		uintptr(numberOfPorts),
		uintptr(unsafe.Pointer(token)))
	return ULONG(ret1)
}

func CreateProxyArpEntry(dwAddress DWORD, dwMask DWORD, dwIfIndex DWORD) DWORD {
	ret1 := syscall3(createProxyArpEntry, 3,
		uintptr(dwAddress),
		uintptr(dwMask),
		uintptr(dwIfIndex))
	return DWORD(ret1)
}

func DeleteIPAddress(nTEContext ULONG) DWORD {
	ret1 := syscall3(deleteIPAddress, 1,
		uintptr(nTEContext),
		0,
		0)
	return DWORD(ret1)
}

func DeleteIpForwardEntry(pRoute PMIB_IPFORWARDROW) DWORD {
	ret1 := syscall3(deleteIpForwardEntry, 1,
		uintptr(unsafe.Pointer(pRoute)),
		0,
		0)
	return DWORD(ret1)
}

func DeleteIpNetEntry(pArpEntry PMIB_IPNETROW) DWORD {
	ret1 := syscall3(deleteIpNetEntry, 1,
		uintptr(unsafe.Pointer(pArpEntry)),
		0,
		0)
	return DWORD(ret1)
}

func DeletePersistentTcpPortReservation(startPort USHORT, numberOfPorts USHORT) ULONG {
	ret1 := syscall3(deletePersistentTcpPortReservation, 2,
		uintptr(startPort),
		uintptr(numberOfPorts),
		0)
	return ULONG(ret1)
}

func DeletePersistentUdpPortReservation(startPort USHORT, numberOfPorts USHORT) ULONG {
	ret1 := syscall3(deletePersistentUdpPortReservation, 2,
		uintptr(startPort),
		uintptr(numberOfPorts),
		0)
	return ULONG(ret1)
}

func DeleteProxyArpEntry(dwAddress DWORD, dwMask DWORD, dwIfIndex DWORD) DWORD {
	ret1 := syscall3(deleteProxyArpEntry, 3,
		uintptr(dwAddress),
		uintptr(dwMask),
		uintptr(dwIfIndex))
	return DWORD(ret1)
}

func DisableMediaSense(pHandle *HANDLE, pOverLapped *OVERLAPPED) DWORD {
	ret1 := syscall3(disableMediaSense, 2,
		uintptr(unsafe.Pointer(pHandle)),
		uintptr(unsafe.Pointer(pOverLapped)),
		0)
	return DWORD(ret1)
}

func EnableRouter(pHandle *HANDLE, pOverlapped *OVERLAPPED) DWORD {
	ret1 := syscall3(enableRouter, 2,
		uintptr(unsafe.Pointer(pHandle)),
		uintptr(unsafe.Pointer(pOverlapped)),
		0)
	return DWORD(ret1)
}

func FlushIpNetTable(dwIfIndex DWORD) DWORD {
	ret1 := syscall3(flushIpNetTable, 1,
		uintptr(dwIfIndex),
		0,
		0)
	return DWORD(ret1)
}

func GetAdapterIndex(adapterName LPWSTR, ifIndex *uint32) DWORD {
	ret1 := syscall3(getAdapterIndex, 2,
		uintptr(unsafe.Pointer(adapterName)),
		uintptr(unsafe.Pointer(ifIndex)),
		0)
	return DWORD(ret1)
}

func GetAdapterOrderMap() PIP_ADAPTER_ORDER_MAP {
	ret1 := syscall3(getAdapterOrderMap, 0,
		0,
		0,
		0)
	return (PIP_ADAPTER_ORDER_MAP)(unsafe.Pointer(ret1))
}

func GetAdaptersAddresses(family ULONG, flags ULONG, reserved uintptr, adapterAddresses PIP_ADAPTER_ADDRESSES, sizePointer *uint32) ULONG {
	ret1 := syscall6(getAdaptersAddresses, 5,
		uintptr(family),
		uintptr(flags),
		reserved,
		uintptr(unsafe.Pointer(adapterAddresses)),
		uintptr(unsafe.Pointer(sizePointer)),
		0)
	return ULONG(ret1)
}

func GetAdaptersInfo(adapterInfo PIP_ADAPTER_INFO, sizePointer *uint32) ULONG {
	ret1 := syscall3(getAdaptersInfo, 2,
		uintptr(unsafe.Pointer(adapterInfo)),
		uintptr(unsafe.Pointer(sizePointer)),
		0)
	return ULONG(ret1)
}

func GetBestInterface(dwDestAddr IPAddr, pdwBestIfIndex *DWORD) DWORD {
	ret1 := syscall3(getBestInterface, 2,
		uintptr(dwDestAddr),
		uintptr(unsafe.Pointer(pdwBestIfIndex)),
		0)
	return DWORD(ret1)
}

func GetBestInterfaceEx(pDestAddr *Sockaddr, pdwBestIfIndex *DWORD) DWORD {
	ret1 := syscall3(getBestInterfaceEx, 2,
		uintptr(unsafe.Pointer(pDestAddr)),
		uintptr(unsafe.Pointer(pdwBestIfIndex)),
		0)
	return DWORD(ret1)
}

func GetBestRoute(dwDestAddr DWORD, dwSourceAddr DWORD, pBestRoute PMIB_IPFORWARDROW) DWORD {
	ret1 := syscall3(getBestRoute, 3,
		uintptr(dwDestAddr),
		uintptr(dwSourceAddr),
		uintptr(unsafe.Pointer(pBestRoute)))
	return DWORD(ret1)
}

func GetExtendedTcpTable(pTcpTable uintptr, pdwSize *DWORD, bOrder bool, ulAf ULONG, tableClass TCP_TABLE_CLASS, reserved ULONG) DWORD {
	ret1 := syscall6(getExtendedTcpTable, 6,
		pTcpTable,
		uintptr(unsafe.Pointer(pdwSize)),
		getUintptrFromBool(bOrder),
		uintptr(ulAf),
		uintptr(tableClass),
		uintptr(reserved))
	return DWORD(ret1)
}

func GetExtendedUdpTable(pUdpTable uintptr, pdwSize *DWORD, bOrder bool, ulAf ULONG, tableClass UDP_TABLE_CLASS, reserved ULONG) DWORD {
	ret1 := syscall6(getExtendedUdpTable, 6,
		pUdpTable,
		uintptr(unsafe.Pointer(pdwSize)),
		getUintptrFromBool(bOrder),
		uintptr(ulAf),
		uintptr(tableClass),
		uintptr(reserved))
	return DWORD(ret1)
}

func GetFriendlyIfIndex(ifIndex DWORD) DWORD {
	ret1 := syscall3(getFriendlyIfIndex, 1,
		uintptr(ifIndex),
		0,
		0)
	return DWORD(ret1)
}

func GetIcmpStatistics(statistics PMIB_ICMP) ULONG {
	ret1 := syscall3(getIcmpStatistics, 1,
		uintptr(unsafe.Pointer(statistics)),
		0,
		0)
	return ULONG(ret1)
}

func GetIcmpStatisticsEx(statistics PMIB_ICMP_EX, family ULONG) ULONG {
	ret1 := syscall3(getIcmpStatisticsEx, 2,
		uintptr(unsafe.Pointer(statistics)),
		uintptr(family),
		0)
	return ULONG(ret1)
}

func GetIfEntry(pIfRow PMIB_IFROW) DWORD {
	ret1 := syscall3(getIfEntry, 1,
		uintptr(unsafe.Pointer(pIfRow)),
		0,
		0)
	return DWORD(ret1)
}

func GetIfTable(pIfTable PMIB_IFTABLE, pdwSize *uint32, bOrder bool) DWORD {
	ret1 := syscall3(getIfTable, 3,
		uintptr(unsafe.Pointer(pIfTable)),
		uintptr(unsafe.Pointer(pdwSize)),
		getUintptrFromBool(bOrder))
	return DWORD(ret1)
}

func GetInterfaceInfo(pIfTable PIP_INTERFACE_INFO, dwOutBufLen *uint32) DWORD {
	ret1 := syscall3(getInterfaceInfo, 2,
		uintptr(unsafe.Pointer(pIfTable)),
		uintptr(unsafe.Pointer(dwOutBufLen)),
		0)
	return DWORD(ret1)
}

func GetIpAddrTable(pIpAddrTable PMIB_IPADDRTABLE, pdwSize *uint32, bOrder bool) DWORD {
	ret1 := syscall3(getIpAddrTable, 3,
		uintptr(unsafe.Pointer(pIpAddrTable)),
		uintptr(unsafe.Pointer(pdwSize)),
		getUintptrFromBool(bOrder))
	return DWORD(ret1)
}

func GetIpErrorString(errorCode IP_STATUS, buffer PWSTR, size *DWORD) DWORD {
	ret1 := syscall3(getIpErrorString, 3,
		uintptr(errorCode),
		uintptr(unsafe.Pointer(buffer)),
		uintptr(unsafe.Pointer(size)))
	return DWORD(ret1)
}

func GetIpForwardTable(pIpForwardTable PMIB_IPFORWARDTABLE, pdwSize *uint32, bOrder bool) DWORD {
	ret1 := syscall3(getIpForwardTable, 3,
		uintptr(unsafe.Pointer(pIpForwardTable)),
		uintptr(unsafe.Pointer(pdwSize)),
		getUintptrFromBool(bOrder))
	return DWORD(ret1)
}

func GetIpNetTable(ipNetTable PMIB_IPNETTABLE, sizePointer *uint32, order bool) ULONG {
	ret1 := syscall3(getIpNetTable, 3,
		uintptr(unsafe.Pointer(ipNetTable)),
		uintptr(unsafe.Pointer(sizePointer)),
		getUintptrFromBool(order))
	return ULONG(ret1)
}

func GetIpStatistics(statistics PMIB_IPSTATS) ULONG {
	ret1 := syscall3(getIpStatistics, 1,
		uintptr(unsafe.Pointer(statistics)),
		0,
		0)
	return ULONG(ret1)
}

func GetIpStatisticsEx(statistics PMIB_IPSTATS, family ULONG) ULONG {
	ret1 := syscall3(getIpStatisticsEx, 2,
		uintptr(unsafe.Pointer(statistics)),
		uintptr(family),
		0)
	return ULONG(ret1)
}

func GetNetworkParams(pFixedInfo PFIXED_INFO, pOutBufLen *uint32) DWORD {
	ret1 := syscall3(getNetworkParams, 2,
		uintptr(unsafe.Pointer(pFixedInfo)),
		uintptr(unsafe.Pointer(pOutBufLen)),
		0)
	return DWORD(ret1)
}

func GetNumberOfInterfaces(pdwNumIf *DWORD) DWORD {
	ret1 := syscall3(getNumberOfInterfaces, 1,
		uintptr(unsafe.Pointer(pdwNumIf)),
		0,
		0)
	return DWORD(ret1)
}

func GetOwnerModuleFromTcp6Entry(pTcpEntry PMIB_TCP6ROW_OWNER_MODULE, class TCPIP_OWNER_MODULE_INFO_CLASS, pBuffer uintptr, pdwSize *DWORD) DWORD {
	ret1 := syscall6(getOwnerModuleFromTcp6Entry, 4,
		uintptr(unsafe.Pointer(pTcpEntry)),
		uintptr(class),
		pBuffer,
		uintptr(unsafe.Pointer(pdwSize)),
		0,
		0)
	return DWORD(ret1)
}

func GetOwnerModuleFromTcpEntry(pTcpEntry PMIB_TCPROW_OWNER_MODULE, class TCPIP_OWNER_MODULE_INFO_CLASS, pBuffer uintptr, pdwSize *DWORD) DWORD {
	ret1 := syscall6(getOwnerModuleFromTcpEntry, 4,
		uintptr(unsafe.Pointer(pTcpEntry)),
		uintptr(class),
		pBuffer,
		uintptr(unsafe.Pointer(pdwSize)),
		0,
		0)
	return DWORD(ret1)
}

func GetOwnerModuleFromUdp6Entry(pUdpEntry PMIB_UDP6ROW_OWNER_MODULE, class TCPIP_OWNER_MODULE_INFO_CLASS, pBuffer uintptr, pdwSize *DWORD) DWORD {
	ret1 := syscall6(getOwnerModuleFromUdp6Entry, 4,
		uintptr(unsafe.Pointer(pUdpEntry)),
		uintptr(class),
		pBuffer,
		uintptr(unsafe.Pointer(pdwSize)),
		0,
		0)
	return DWORD(ret1)
}

func GetOwnerModuleFromUdpEntry(pUdpEntry PMIB_UDPROW_OWNER_MODULE, class TCPIP_OWNER_MODULE_INFO_CLASS, pBuffer uintptr, pdwSize *DWORD) DWORD {
	ret1 := syscall6(getOwnerModuleFromUdpEntry, 4,
		uintptr(unsafe.Pointer(pUdpEntry)),
		uintptr(class),
		pBuffer,
		uintptr(unsafe.Pointer(pdwSize)),
		0,
		0)
	return DWORD(ret1)
}

func GetPerAdapterInfo(ifIndex ULONG, pPerAdapterInfo PIP_PER_ADAPTER_INFO, pOutBufLen *uint32) DWORD {
	ret1 := syscall3(getPerAdapterInfo, 3,
		uintptr(ifIndex),
		uintptr(unsafe.Pointer(pPerAdapterInfo)),
		uintptr(unsafe.Pointer(pOutBufLen)))
	return DWORD(ret1)
}

func GetPerTcp6ConnectionEStats(row PMIB_TCP6ROW, estatsType TCP_ESTATS_TYPE, rw PUCHAR, rwVersion ULONG, rwSize ULONG, ros PUCHAR, rosVersion ULONG, rosSize ULONG, rod PUCHAR, rodVersion ULONG, rodSize ULONG) ULONG {
	ret1 := syscall12(getPerTcp6ConnectionEStats, 11,
		uintptr(unsafe.Pointer(row)),
		uintptr(estatsType),
		uintptr(unsafe.Pointer(rw)),
		uintptr(rwVersion),
		uintptr(rwSize),
		uintptr(unsafe.Pointer(ros)),
		uintptr(rosVersion),
		uintptr(rosSize),
		uintptr(unsafe.Pointer(rod)),
		uintptr(rodVersion),
		uintptr(rodSize),
		0)
	return ULONG(ret1)
}

func GetPerTcpConnectionEStats(row PMIB_TCPROW, estatsType TCP_ESTATS_TYPE, rw PUCHAR, rwVersion ULONG, rwSize ULONG, ros PUCHAR, rosVersion ULONG, rosSize ULONG, rod PUCHAR, rodVersion ULONG, rodSize ULONG) ULONG {
	ret1 := syscall12(getPerTcpConnectionEStats, 11,
		uintptr(unsafe.Pointer(row)),
		uintptr(estatsType),
		uintptr(unsafe.Pointer(rw)),
		uintptr(rwVersion),
		uintptr(rwSize),
		uintptr(unsafe.Pointer(ros)),
		uintptr(rosVersion),
		uintptr(rosSize),
		uintptr(unsafe.Pointer(rod)),
		uintptr(rodVersion),
		uintptr(rodSize),
		0)
	return ULONG(ret1)
}

func GetRTTAndHopCount(destIpAddress IPAddr, hopCount *uint32, maxHops ULONG, rTT *uint32) bool {
	ret1 := syscall6(getRTTAndHopCount, 4,
		uintptr(destIpAddress),
		uintptr(unsafe.Pointer(hopCount)),
		uintptr(maxHops),
		uintptr(unsafe.Pointer(rTT)),
		0,
		0)
	return ret1 != 0
}

func GetTcp6Table(tcpTable PMIB_TCP6TABLE, sizePointer *uint32, order bool) ULONG {
	ret1 := syscall3(getTcp6Table, 3,
		uintptr(unsafe.Pointer(tcpTable)),
		uintptr(unsafe.Pointer(sizePointer)),
		getUintptrFromBool(order))
	return ULONG(ret1)
}

func GetTcp6Table2(tcpTable PMIB_TCP6TABLE2, sizePointer *uint32, order bool) ULONG {
	ret1 := syscall3(getTcp6Table2, 3,
		uintptr(unsafe.Pointer(tcpTable)),
		uintptr(unsafe.Pointer(sizePointer)),
		getUintptrFromBool(order))
	return ULONG(ret1)
}

func GetTcpStatistics(statistics PMIB_TCPSTATS) ULONG {
	ret1 := syscall3(getTcpStatistics, 1,
		uintptr(unsafe.Pointer(statistics)),
		0,
		0)
	return ULONG(ret1)
}

func GetTcpStatisticsEx(statistics PMIB_TCPSTATS, family ULONG) ULONG {
	ret1 := syscall3(getTcpStatisticsEx, 2,
		uintptr(unsafe.Pointer(statistics)),
		uintptr(family),
		0)
	return ULONG(ret1)
}

func GetTcpTable(tcpTable PMIB_TCPTABLE, sizePointer *uint32, order bool) ULONG {
	ret1 := syscall3(getTcpTable, 3,
		uintptr(unsafe.Pointer(tcpTable)),
		uintptr(unsafe.Pointer(sizePointer)),
		getUintptrFromBool(order))
	return ULONG(ret1)
}

func GetTcpTable2(tcpTable PMIB_TCPTABLE2, sizePointer *uint32, order bool) ULONG {
	ret1 := syscall3(getTcpTable2, 3,
		uintptr(unsafe.Pointer(tcpTable)),
		uintptr(unsafe.Pointer(sizePointer)),
		getUintptrFromBool(order))
	return ULONG(ret1)
}

func GetUdp6Table(udp6Table PMIB_UDP6TABLE, sizePointer *uint32, order bool) ULONG {
	ret1 := syscall3(getUdp6Table, 3,
		uintptr(unsafe.Pointer(udp6Table)),
		uintptr(unsafe.Pointer(sizePointer)),
		getUintptrFromBool(order))
	return ULONG(ret1)
}

func GetUdpStatistics(stats PMIB_UDPSTATS) ULONG {
	ret1 := syscall3(getUdpStatistics, 1,
		uintptr(unsafe.Pointer(stats)),
		0,
		0)
	return ULONG(ret1)
}

func GetUdpStatisticsEx(statistics PMIB_UDPSTATS, family ULONG) ULONG {
	ret1 := syscall3(getUdpStatisticsEx, 2,
		uintptr(unsafe.Pointer(statistics)),
		uintptr(family),
		0)
	return ULONG(ret1)
}

func GetUdpTable(udpTable PMIB_UDPTABLE, sizePointer *uint32, order bool) ULONG {
	ret1 := syscall3(getUdpTable, 3,
		uintptr(unsafe.Pointer(udpTable)),
		uintptr(unsafe.Pointer(sizePointer)),
		getUintptrFromBool(order))
	return ULONG(ret1)
}

func GetUniDirectionalAdapterInfo(pIPIfInfo PIP_UNIDIRECTIONAL_ADAPTER_ADDRESS, dwOutBufLen *uint32) DWORD {
	ret1 := syscall3(getUniDirectionalAdapterInfo, 2,
		uintptr(unsafe.Pointer(pIPIfInfo)),
		uintptr(unsafe.Pointer(dwOutBufLen)),
		0)
	return DWORD(ret1)
}

func Icmp6CreateFile() HANDLE {
	ret1 := syscall3(icmp6CreateFile, 0,
		0,
		0,
		0)
	return HANDLE(ret1)
}

func Icmp6ParseReplies(replyBuffer LPVOID, replySize DWORD) DWORD {
	ret1 := syscall3(icmp6ParseReplies, 2,
		uintptr(unsafe.Pointer(replyBuffer)),
		uintptr(replySize),
		0)
	return DWORD(ret1)
}

func Icmp6SendEcho2(icmpHandle HANDLE, event HANDLE, apcRoutine PIO_APC_ROUTINE, apcContext uintptr, sourceAddress *SOCKADDR_IN6_LH, destinationAddress *SOCKADDR_IN6_LH, requestData LPVOID, requestSize WORD, requestOptions PIP_OPTION_INFORMATION, replyBuffer LPVOID, replySize DWORD, timeout DWORD) DWORD {
	apcRoutineCallback := syscall.NewCallback(apcRoutine)
	ret1 := syscall12(icmp6SendEcho2, 12,
		uintptr(icmpHandle),
		uintptr(event),
		apcRoutineCallback,
		apcContext,
		uintptr(unsafe.Pointer(sourceAddress)),
		uintptr(unsafe.Pointer(destinationAddress)),
		uintptr(unsafe.Pointer(requestData)),
		uintptr(requestSize),
		uintptr(unsafe.Pointer(requestOptions)),
		uintptr(unsafe.Pointer(replyBuffer)),
		uintptr(replySize),
		uintptr(timeout))
	return DWORD(ret1)
}

func IcmpCloseHandle(icmpHandle HANDLE) bool {
	ret1 := syscall3(icmpCloseHandle, 1,
		uintptr(icmpHandle),
		0,
		0)
	return ret1 != 0
}

func IcmpCreateFile() HANDLE {
	ret1 := syscall3(icmpCreateFile, 0,
		0,
		0,
		0)
	return HANDLE(ret1)
}

func IcmpParseReplies(replyBuffer LPVOID, replySize DWORD) DWORD {
	ret1 := syscall3(icmpParseReplies, 2,
		uintptr(unsafe.Pointer(replyBuffer)),
		uintptr(replySize),
		0)
	return DWORD(ret1)
}

func IcmpSendEcho(icmpHandle HANDLE, destinationAddress IPAddr, requestData LPVOID, requestSize WORD, requestOptions PIP_OPTION_INFORMATION, replyBuffer LPVOID, replySize DWORD, timeout DWORD) DWORD {
	ret1 := syscall9(icmpSendEcho, 8,
		uintptr(icmpHandle),
		uintptr(destinationAddress),
		uintptr(unsafe.Pointer(requestData)),
		uintptr(requestSize),
		uintptr(unsafe.Pointer(requestOptions)),
		uintptr(unsafe.Pointer(replyBuffer)),
		uintptr(replySize),
		uintptr(timeout),
		0)
	return DWORD(ret1)
}

func IcmpSendEcho2(icmpHandle HANDLE, event HANDLE, apcRoutine PIO_APC_ROUTINE, apcContext uintptr, destinationAddress IPAddr, requestData LPVOID, requestSize WORD, requestOptions PIP_OPTION_INFORMATION, replyBuffer LPVOID, replySize DWORD, timeout DWORD) DWORD {
	apcRoutineCallback := syscall.NewCallback(apcRoutine)
	ret1 := syscall12(icmpSendEcho2, 11,
		uintptr(icmpHandle),
		uintptr(event),
		apcRoutineCallback,
		apcContext,
		uintptr(destinationAddress),
		uintptr(unsafe.Pointer(requestData)),
		uintptr(requestSize),
		uintptr(unsafe.Pointer(requestOptions)),
		uintptr(unsafe.Pointer(replyBuffer)),
		uintptr(replySize),
		uintptr(timeout),
		0)
	return DWORD(ret1)
}

func IpReleaseAddress(adapterInfo PIP_ADAPTER_INDEX_MAP) DWORD {
	ret1 := syscall3(ipReleaseAddress, 1,
		uintptr(unsafe.Pointer(adapterInfo)),
		0,
		0)
	return DWORD(ret1)
}

func IpRenewAddress(adapterInfo PIP_ADAPTER_INDEX_MAP) DWORD {
	ret1 := syscall3(ipRenewAddress, 1,
		uintptr(unsafe.Pointer(adapterInfo)),
		0,
		0)
	return DWORD(ret1)
}

func LookupPersistentTcpPortReservation(startPort USHORT, numberOfPorts USHORT, token PULONG64) ULONG {
	ret1 := syscall3(lookupPersistentTcpPortReservation, 3,
		uintptr(startPort),
		uintptr(numberOfPorts),
		uintptr(unsafe.Pointer(token)))
	return ULONG(ret1)
}

func LookupPersistentUdpPortReservation(startPort USHORT, numberOfPorts USHORT, token PULONG64) ULONG {
	ret1 := syscall3(lookupPersistentUdpPortReservation, 3,
		uintptr(startPort),
		uintptr(numberOfPorts),
		uintptr(unsafe.Pointer(token)))
	return ULONG(ret1)
}

func NhpAllocateAndGetInterfaceInfoFromStack(ppTable **IP_INTERFACE_NAME_INFO, pdwCount *DWORD, bOrder bool, hHeap HANDLE, dwFlags DWORD) DWORD {
	ret1 := syscall6(nhpAllocateAndGetInterfaceInfoFromStack, 5,
		uintptr(unsafe.Pointer(ppTable)),
		uintptr(unsafe.Pointer(pdwCount)),
		getUintptrFromBool(bOrder),
		uintptr(hHeap),
		uintptr(dwFlags),
		0)
	return DWORD(ret1)
}

func NotifyAddrChange(handle *HANDLE, overlapped *OVERLAPPED) DWORD {
	ret1 := syscall3(notifyAddrChange, 2,
		uintptr(unsafe.Pointer(handle)),
		uintptr(unsafe.Pointer(overlapped)),
		0)
	return DWORD(ret1)
}

func NotifyRouteChange(handle *HANDLE, overlapped *OVERLAPPED) DWORD {
	ret1 := syscall3(notifyRouteChange, 2,
		uintptr(unsafe.Pointer(handle)),
		uintptr(unsafe.Pointer(overlapped)),
		0)
	return DWORD(ret1)
}

func ParseNetworkString(networkString /*const*/ *WCHAR, types DWORD, addressInfo PNET_ADDRESS_INFO, portNumber *USHORT, prefixLength *byte) DWORD {
	ret1 := syscall6(parseNetworkString, 5,
		uintptr(unsafe.Pointer(networkString)),
		uintptr(types),
		uintptr(unsafe.Pointer(addressInfo)),
		uintptr(unsafe.Pointer(portNumber)),
		uintptr(unsafe.Pointer(prefixLength)),
		0)
	return DWORD(ret1)
}

func ResolveNeighbor(networkAddress *SOCKADDR, physicalAddress uintptr, physicalAddressLength *uint32) ULONG {
	ret1 := syscall3(resolveNeighbor, 3,
		uintptr(unsafe.Pointer(networkAddress)),
		physicalAddress,
		uintptr(unsafe.Pointer(physicalAddressLength)))
	return ULONG(ret1)
}

func RestoreMediaSense(pOverlapped *OVERLAPPED, lpdwEnableCount *uint32) DWORD {
	ret1 := syscall3(restoreMediaSense, 2,
		uintptr(unsafe.Pointer(pOverlapped)),
		uintptr(unsafe.Pointer(lpdwEnableCount)),
		0)
	return DWORD(ret1)
}

func SendARP(destIP IPAddr, srcIP IPAddr, pMacAddr uintptr, phyAddrLen *uint32) DWORD {
	ret1 := syscall6(sendARP, 4,
		uintptr(destIP),
		uintptr(srcIP),
		pMacAddr,
		uintptr(unsafe.Pointer(phyAddrLen)),
		0,
		0)
	return DWORD(ret1)
}

func SetIfEntry(pIfRow PMIB_IFROW) DWORD {
	ret1 := syscall3(setIfEntry, 1,
		uintptr(unsafe.Pointer(pIfRow)),
		0,
		0)
	return DWORD(ret1)
}

func SetIpForwardEntry(pRoute PMIB_IPFORWARDROW) DWORD {
	ret1 := syscall3(setIpForwardEntry, 1,
		uintptr(unsafe.Pointer(pRoute)),
		0,
		0)
	return DWORD(ret1)
}

func SetIpNetEntry(pArpEntry PMIB_IPNETROW) DWORD {
	ret1 := syscall3(setIpNetEntry, 1,
		uintptr(unsafe.Pointer(pArpEntry)),
		0,
		0)
	return DWORD(ret1)
}

func SetIpStatistics(pIpStats PMIB_IPSTATS) DWORD {
	ret1 := syscall3(setIpStatistics, 1,
		uintptr(unsafe.Pointer(pIpStats)),
		0,
		0)
	return DWORD(ret1)
}

func SetIpStatisticsEx(statistics PMIB_IPSTATS, family ULONG) ULONG {
	ret1 := syscall3(setIpStatisticsEx, 2,
		uintptr(unsafe.Pointer(statistics)),
		uintptr(family),
		0)
	return ULONG(ret1)
}

func SetIpTTL(nTTL UINT) DWORD {
	ret1 := syscall3(setIpTTL, 1,
		uintptr(nTTL),
		0,
		0)
	return DWORD(ret1)
}

func SetPerTcp6ConnectionEStats(row PMIB_TCP6ROW, estatsType TCP_ESTATS_TYPE, rw PUCHAR, rwVersion ULONG, rwSize ULONG, offset ULONG) ULONG {
	ret1 := syscall6(setPerTcp6ConnectionEStats, 6,
		uintptr(unsafe.Pointer(row)),
		uintptr(estatsType),
		uintptr(unsafe.Pointer(rw)),
		uintptr(rwVersion),
		uintptr(rwSize),
		uintptr(offset))
	return ULONG(ret1)
}

func SetPerTcpConnectionEStats(row PMIB_TCPROW, estatsType TCP_ESTATS_TYPE, rw PUCHAR, rwVersion ULONG, rwSize ULONG, offset ULONG) ULONG {
	ret1 := syscall6(setPerTcpConnectionEStats, 6,
		uintptr(unsafe.Pointer(row)),
		uintptr(estatsType),
		uintptr(unsafe.Pointer(rw)),
		uintptr(rwVersion),
		uintptr(rwSize),
		uintptr(offset))
	return ULONG(ret1)
}

func SetTcpEntry(pTcpRow PMIB_TCPROW) DWORD {
	ret1 := syscall3(setTcpEntry, 1,
		uintptr(unsafe.Pointer(pTcpRow)),
		0,
		0)
	return DWORD(ret1)
}

func UnenableRouter(pOverlapped *OVERLAPPED, lpdwEnableCount *uint32) DWORD {
	ret1 := syscall3(unenableRouter, 2,
		uintptr(unsafe.Pointer(pOverlapped)),
		uintptr(unsafe.Pointer(lpdwEnableCount)),
		0)
	return DWORD(ret1)
}

func AllocateAndGetIfTableFromStack(ppIfTable *PMIB_IFTABLE, bOrder bool, heap HANDLE, flags DWORD) DWORD {
	ret1 := syscall6(allocateAndGetIfTableFromStack, 4,
		uintptr(unsafe.Pointer(ppIfTable)),
		getUintptrFromBool(bOrder),
		uintptr(heap),
		uintptr(flags),
		0,
		0)
	return DWORD(ret1)
}

func AllocateAndGetIpAddrTableFromStack(ppIpAddrTable *PMIB_IPADDRTABLE, bOrder bool, heap HANDLE, flags DWORD) DWORD {
	ret1 := syscall6(allocateAndGetIpAddrTableFromStack, 4,
		uintptr(unsafe.Pointer(ppIpAddrTable)),
		getUintptrFromBool(bOrder),
		uintptr(heap),
		uintptr(flags),
		0,
		0)
	return DWORD(ret1)
}

func AllocateAndGetIpForwardTableFromStack(ppIpForwardTable *PMIB_IPFORWARDTABLE, bOrder bool, heap HANDLE, flags DWORD) DWORD {
	ret1 := syscall6(allocateAndGetIpForwardTableFromStack, 4,
		uintptr(unsafe.Pointer(ppIpForwardTable)),
		getUintptrFromBool(bOrder),
		uintptr(heap),
		uintptr(flags),
		0,
		0)
	return DWORD(ret1)
}

func AllocateAndGetIpNetTableFromStack(ppIpNetTable *PMIB_IPNETTABLE, bOrder bool, heap HANDLE, flags DWORD) DWORD {
	ret1 := syscall6(allocateAndGetIpNetTableFromStack, 4,
		uintptr(unsafe.Pointer(ppIpNetTable)),
		getUintptrFromBool(bOrder),
		uintptr(heap),
		uintptr(flags),
		0,
		0)
	return DWORD(ret1)
}

func AllocateAndGetTcpTableFromStack(ppTcpTable *PMIB_TCPTABLE, bOrder bool, heap HANDLE, flags DWORD) DWORD {
	ret1 := syscall6(allocateAndGetTcpTableFromStack, 4,
		uintptr(unsafe.Pointer(ppTcpTable)),
		getUintptrFromBool(bOrder),
		uintptr(heap),
		uintptr(flags),
		0,
		0)
	return DWORD(ret1)
}

func AllocateAndGetUdpTableFromStack(ppUdpTable *PMIB_UDPTABLE, bOrder bool, heap HANDLE, flags DWORD) DWORD {
	ret1 := syscall6(allocateAndGetUdpTableFromStack, 4,
		uintptr(unsafe.Pointer(ppUdpTable)),
		getUintptrFromBool(bOrder),
		uintptr(heap),
		uintptr(flags),
		0,
		0)
	return DWORD(ret1)
}

func CancelMibChangeNotify2(handle HANDLE) DWORD {
	ret1 := syscall3(cancelMibChangeNotify2, 1,
		uintptr(handle),
		0,
		0)
	return DWORD(ret1)
}

func ConvertInterfaceGuidToLuid(guid /*const*/ *GUID, luid *NET_LUID) DWORD {
	ret1 := syscall3(convertInterfaceGuidToLuid, 2,
		uintptr(unsafe.Pointer(guid)),
		uintptr(unsafe.Pointer(luid)),
		0)
	return DWORD(ret1)
}

func ConvertInterfaceIndexToLuid(index NET_IFINDEX, luid *NET_LUID) DWORD {
	ret1 := syscall3(convertInterfaceIndexToLuid, 2,
		uintptr(index),
		uintptr(unsafe.Pointer(luid)),
		0)
	return DWORD(ret1)
}

func ConvertInterfaceLuidToGuid(luid /*const*/ *NET_LUID, guid *GUID) DWORD {
	ret1 := syscall3(convertInterfaceLuidToGuid, 2,
		uintptr(unsafe.Pointer(luid)),
		uintptr(unsafe.Pointer(guid)),
		0)
	return DWORD(ret1)
}

func ConvertInterfaceLuidToIndex(luid /*const*/ *NET_LUID, index *NET_IFINDEX) DWORD {
	ret1 := syscall3(convertInterfaceLuidToIndex, 2,
		uintptr(unsafe.Pointer(luid)),
		uintptr(unsafe.Pointer(index)),
		0)
	return DWORD(ret1)
}

func ConvertInterfaceLuidToName(luid /*const*/ *NET_LUID, name *WCHAR, aLen SIZE_T) DWORD {
	ret1 := syscall3(convertInterfaceLuidToName, 3,
		uintptr(unsafe.Pointer(luid)),
		uintptr(unsafe.Pointer(name)),
		uintptr(aLen))
	return DWORD(ret1)
}

func ConvertInterfaceNameToLuid(name /*const*/ *WCHAR, luid *NET_LUID) DWORD {
	ret1 := syscall3(convertInterfaceNameToLuid, 2,
		uintptr(unsafe.Pointer(name)),
		uintptr(unsafe.Pointer(luid)),
		0)
	return DWORD(ret1)
}

func CreateSortedAddressPairs(src_list /*const*/ PSOCKADDR_IN6, src_count DWORD, dst_list /*const*/ PSOCKADDR_IN6, dst_count DWORD, options DWORD, pair_list *PSOCKADDR_IN6_PAIR, pair_count *uint32) DWORD {
	ret1 := syscall9(createSortedAddressPairs, 7,
		uintptr(unsafe.Pointer(src_list)),
		uintptr(src_count),
		uintptr(unsafe.Pointer(dst_list)),
		uintptr(dst_count),
		uintptr(options),
		uintptr(unsafe.Pointer(pair_list)),
		uintptr(unsafe.Pointer(pair_count)),
		0,
		0)
	return DWORD(ret1)
}

func FreeMibTable(ptr uintptr) {
	syscall3(freeMibTable, 1,
		ptr,
		0,
		0)
}

func GetIfEntry2(row2 *MIB_IF_ROW2) DWORD {
	ret1 := syscall3(getIfEntry2, 1,
		uintptr(unsafe.Pointer(row2)),
		0,
		0)
	return DWORD(ret1)
}

func GetIfTable2(table **MIB_IF_TABLE2) DWORD {
	ret1 := syscall3(getIfTable2, 1,
		uintptr(unsafe.Pointer(table)),
		0,
		0)
	return DWORD(ret1)
}

func IcmpSendEcho2Ex(icmpHandle HANDLE, event HANDLE, apcRoutine PIO_APC_ROUTINE, apcContext uintptr, sourceAddress IPAddr, destinationAddress IPAddr, requestData LPVOID, requestSize WORD, requestOptions PIP_OPTION_INFORMATION, replyBuffer LPVOID, replySize DWORD, timeout DWORD) DWORD {
	apcRoutineCallback := syscall.NewCallback(apcRoutine)
	ret1 := syscall12(icmpSendEcho2Ex, 12,
		uintptr(icmpHandle),
		uintptr(event),
		apcRoutineCallback,
		apcContext,
		uintptr(sourceAddress),
		uintptr(destinationAddress),
		uintptr(unsafe.Pointer(requestData)),
		uintptr(requestSize),
		uintptr(unsafe.Pointer(requestOptions)),
		uintptr(unsafe.Pointer(replyBuffer)),
		uintptr(replySize),
		uintptr(timeout))
	return DWORD(ret1)
}

func NotifyIpInterfaceChange(family ULONG, callback uintptr, context uintptr, init_notify BOOLEAN, handle *HANDLE) DWORD {
	ret1 := syscall6(notifyIpInterfaceChange, 5,
		uintptr(family),
		callback,
		context,
		uintptr(init_notify),
		uintptr(unsafe.Pointer(handle)),
		0)
	return DWORD(ret1)
}

func PfBindInterfaceToIPAddress(aInterface INTERFACE_HANDLE, aType PFADDRESSTYPE, ip *byte) DWORD {
	ret1 := syscall3(pfBindInterfaceToIPAddress, 3,
		uintptr(aInterface),
		uintptr(aType),
		uintptr(unsafe.Pointer(ip)))
	return DWORD(ret1)
}

func PfCreateInterface(dwName DWORD, inAction PFFORWARD_ACTION, outAction PFFORWARD_ACTION, bUseLog bool, bMustBeUnique bool, ppInterface *INTERFACE_HANDLE) DWORD {
	ret1 := syscall6(pfCreateInterface, 6,
		uintptr(dwName),
		uintptr(inAction),
		uintptr(outAction),
		getUintptrFromBool(bUseLog),
		getUintptrFromBool(bMustBeUnique),
		uintptr(unsafe.Pointer(ppInterface)))
	return DWORD(ret1)
}

func PfDeleteInterface(aInterface INTERFACE_HANDLE) DWORD {
	ret1 := syscall3(pfDeleteInterface, 1,
		uintptr(aInterface),
		0,
		0)
	return DWORD(ret1)
}

func PfUnBindInterface(aInterface INTERFACE_HANDLE) DWORD {
	ret1 := syscall3(pfUnBindInterface, 1,
		uintptr(aInterface),
		0,
		0)
	return DWORD(ret1)
}
