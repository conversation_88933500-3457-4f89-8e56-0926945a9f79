// Code generated by glow (https://github.com/go-gl/glow). DO NOT EDIT.

// This file implements GlowGetProcAddress for every supported platform. The
// correct version is chosen automatically based on build tags:
//
// windows: WGL
// darwin: CGL
// linux freebsd netbsd openbsd: GLX
//
// Use of EGL instead of the platform's default (listed above) is made possible
// via the "egl" build tag.
//
// It is also possible to install your own function outside this package for
// retrieving OpenGL function pointers, to do this see InitWithProcAddrFunc.

package gl

/*
#cgo windows CFLAGS: -DTAG_WINDOWS
#cgo !gles2,windows       LDFLAGS: -lopengl32
#cgo gles2,windows        LDFLAGS: -lGLESv2
#cgo darwin CFLAGS: -DTAG_DARWIN
#cgo !gles2,darwin LDFLAGS: -framework OpenGL
#cgo gles2,darwin  LDFLAGS: -framework OpenGLES
#cgo linux freebsd netbsd openbsd CFLAGS: -DTAG_POSIX
#cgo !egl,linux !egl,freebsd !egl,netbsd !egl,openbsd pkg-config: gl
#cgo egl,linux egl,freebsd egl,netbsd egl,openbsd egl,windows CFLAGS: -DTAG_EGL
#cgo egl,linux egl,freebsd egl,netbsd egl,openbsd pkg-config: egl
#cgo egl,windows LDFLAGS: -lEGL
#cgo egl,darwin  LDFLAGS: -lEGL
// Check the EGL tag first as it takes priority over the platform's default
// configuration of WGL/GLX/CGL.
#if defined(TAG_EGL)
	#include <stdlib.h>
	#include <EGL/egl.h>
	void* GlowGetProcAddress_gl21(const char* name) {
		return eglGetProcAddress(name);
	}
#elif defined(TAG_WINDOWS)
	#define WIN32_LEAN_AND_MEAN 1
	#include <windows.h>
	#include <stdlib.h>
	static HMODULE ogl32dll = NULL;
	void* GlowGetProcAddress_gl21(const char* name) {
		void* pf = wglGetProcAddress((LPCSTR) name);
		if (pf) {
			return pf;
		}
		if (ogl32dll == NULL) {
			ogl32dll = LoadLibraryA("opengl32.dll");
		}
		return GetProcAddress(ogl32dll, (LPCSTR) name);
	}
#elif defined(TAG_DARWIN)
	#include <stdlib.h>
	#include <dlfcn.h>
	void* GlowGetProcAddress_gl21(const char* name) {
		return dlsym(RTLD_DEFAULT, name);
	}
#elif defined(TAG_POSIX)
	#include <stdlib.h>
	#include <GL/glx.h>
	void* GlowGetProcAddress_gl21(const char* name) {
		return glXGetProcAddress((const GLubyte *) name);
	}
#endif
*/
import "C"
import "unsafe"

func getProcAddress(namea string) unsafe.Pointer {
	cname := C.CString(namea)
	defer C.free(unsafe.Pointer(cname))
	return C.GlowGetProcAddress_gl21(cname)
}
