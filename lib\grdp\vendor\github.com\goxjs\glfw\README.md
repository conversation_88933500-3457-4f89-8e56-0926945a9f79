glfw
====

[![Go Reference](https://pkg.go.dev/badge/github.com/goxjs/glfw.svg)](https://pkg.go.dev/github.com/goxjs/glfw)

Package glfw experimentally provides a glfw-like API
with desktop (via glfw) and browser (via HTML5 canvas) backends.

It is used for creating a GL context and receiving events.

**Note:** This package is currently in development. The API is incomplete and may change.

Installation
------------

```bash
go get github.com/goxjs/glfw
```

Directories
-----------

| Path                                                                | Synopsis                                                           |
|---------------------------------------------------------------------|--------------------------------------------------------------------|
| [test/events](https://pkg.go.dev/github.com/goxjs/glfw/test/events) | events hooks every available callback and outputs their arguments. |

License
-------

-	[MIT License](LICENSE)
