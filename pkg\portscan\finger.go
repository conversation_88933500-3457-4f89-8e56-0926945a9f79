package portscan

import (
	"Lightx/lib/gonmap"
	"Lightx/pkg/clients"
	"fmt"
	"net"
	"strconv"
	"strings"
	"time"
)

// PortResult 存储端口扫描结果
type PortResult struct {
	Status    bool
	IP        string
	Port      int
	Server    string
	Link      string
	HttpTitle string
	Banner    string
	Protocol  string
	Product   string
	Version   string
}

// Connect 连接到指定IP/域名和端口，并进行服务识别（支持IPv4、IPv6和域名）
func Connect(target string, port, timeout int, proxy clients.Proxy) PortResult {
	return ConnectWithFingerprint(target, port, timeout, proxy, true)
}

// ConnectWithFingerprint 连接到指定IP/域名和端口，可选择是否进行服务指纹识别
func ConnectWithFingerprint(target string, port, timeout int, proxy clients.Proxy, enableFingerprint bool) PortResult {
	var pr PortResult

	// 如果禁用指纹识别，只进行简单的TCP连接测试
	if !enableFingerprint {
		return ConnectSimple(target, port, timeout)
	}

	// 第一阶段：快速TCP连接测试（使用较短超时）
	quickTimeout := time.Duration(timeout/3) * time.Second
	if quickTimeout < time.Second {
		quickTimeout = time.Second // 最少1秒
	}

	conn, err := WrapperTcpWithTimeout("tcp", fmt.Sprintf("%s:%d", target, port), quickTimeout)
	if err != nil {
		// 端口关闭，直接返回
		pr.Status = false
		return pr
	}
	conn.Close() // 立即关闭连接，为指纹识别让路

	// 第二阶段：端口开放，进行详细的指纹识别
	return ConnectWithGonmapFingerprint(target, port, timeout, proxy)
}

// ConnectWithGonmapFingerprint 对已确认开放的端口进行 gonmap 指纹识别
func ConnectWithGonmapFingerprint(target string, port, timeout int, proxy clients.Proxy) PortResult {
	var pr PortResult
	scanner := gonmap.New()
	scanTimeout := time.Second * time.Duration(timeout)

	// 执行端口扫描
	status, response := scanner.Scan(target, port, scanTimeout)

	// 处理扫描结果
	switch status {
	case gonmap.Closed:
		pr.Status = false
	case gonmap.Unknown:
		pr.Status = true
		pr.Server = "filter"
	default:
		pr.Status = true
	}

	if response != nil {
		// 记录banner信息
		if response.Raw != "" {
			pr.Banner = strings.ReplaceAll(strings.ReplaceAll(response.Raw, "\r", ""), "\n", "")
			if len(pr.Banner) > 50 {
				pr.Banner = pr.Banner[:50] + "..."
			}
		}

		// 记录服务信息
		if response.FingerPrint != nil {
			if response.FingerPrint.Service != "" {
				pr.Server = response.FingerPrint.Service
				pr.Protocol = response.FingerPrint.Service
			} else {
				// 未识别的服务设为 "tcp"
				pr.Server = "tcp"
			}

			// 记录产品信息
			if response.FingerPrint.ProductName != "" {
				pr.Product = response.FingerPrint.ProductName
			}

			// 记录版本信息
			if response.FingerPrint.Version != "" {
				pr.Version = response.FingerPrint.Version
			}
		} else {
			// 如果FingerPrint为nil，设置默认服务名
			pr.Server = "tcp"
		}

		// 处理具体服务类型
		pr.Link = fmt.Sprintf("%v://%v:%v", pr.Server, target, port)

		// 根据端口判断未识别出的服务
		if pr.Server == "tcp" {
			pr.Server = GuessServiceByPort(port)

			// 更新链接格式
			if pr.Server == "http" {
				pr.Link = fmt.Sprintf("http://%v:%v", target, port)
			} else if pr.Server == "https" {
				pr.Link = fmt.Sprintf("https://%v:%v", target, port)
			} else {
				pr.Link = fmt.Sprintf("%v://%v:%v", pr.Server, target, port)
			}
		}

		// 对HTTP/HTTPS服务进行额外探测
		if pr.Server == "http" || pr.Server == "https" {
			DetectHttpService(&pr, proxy)

			// 检查是否为ClickHouse服务
			if port == 8123 || (pr.HttpTitle != "" && strings.Contains(pr.HttpTitle, "ClickHouse")) {
				pr.Server = "clickhouse"
				pr.Protocol = "http"
				pr.Product = "ClickHouse"
				pr.Link = fmt.Sprintf("http://%v:%v", target, port)
			}
		}

	} else {
		// 如果response为nil，设置默认服务名
		pr.Server = "tcp"
	}

	// 设置目标和端口（可以是IP地址或域名）
	pr.IP = target
	pr.Port = port

	return pr
}

// ConnectSimple 简单的TCP连接测试，不进行指纹识别，直接根据端口号猜测服务类型
func ConnectSimple(target string, port, timeout int) PortResult {
	var pr PortResult

	// 尝试TCP连接
	conn, err := WrapperTcpWithTimeout("tcp", fmt.Sprintf("%s:%d", target, port), time.Duration(timeout)*time.Second)
	if err != nil {
		pr.Status = false
		return pr
	}
	defer conn.Close()

	// 连接成功
	pr.Status = true
	pr.IP = target
	pr.Port = port

	// 根据端口号猜测服务类型
	pr.Server = GuessServiceByPort(port)
	pr.Protocol = pr.Server

	// 设置链接格式
	if pr.Server == "http" {
		pr.Link = fmt.Sprintf("http://%s:%d", target, port)
	} else if pr.Server == "https" {
		pr.Link = fmt.Sprintf("https://%s:%d", target, port)
	} else {
		pr.Link = fmt.Sprintf("%s://%s:%d", pr.Server, target, port)
	}

	return pr
}

// ConnectWithConnectionReuse 连接复用的指纹识别
// 先建立连接进行快速检测，如果端口开放则保持连接进行指纹识别
func ConnectWithConnectionReuse(target string, port, timeout int, proxy clients.Proxy, enableFingerprint bool) PortResult {
	var pr PortResult

	// 如果禁用指纹识别，只进行简单的TCP连接测试
	if !enableFingerprint {
		return ConnectSimple(target, port, timeout)
	}

	// 建立TCP连接
	quickTimeout := time.Duration(timeout/3) * time.Second
	if quickTimeout < time.Second {
		quickTimeout = time.Second
	}

	conn, err := WrapperTcpWithTimeout("tcp", fmt.Sprintf("%s:%d", target, port), quickTimeout)
	if err != nil {
		// 端口关闭，直接返回
		pr.Status = false
		return pr
	}

	// 端口开放，进行指纹识别（复用连接）
	pr.Status = true
	pr.IP = target
	pr.Port = port

	// 使用gonmap进行指纹识别，但先尝试从现有连接读取banner
	scanner := gonmap.New()
	scanTimeout := time.Second * time.Duration(timeout)

	// 关闭快速连接，让gonmap重新建立连接进行完整扫描
	// 注意：gonmap需要完全控制连接，所以这里还是需要关闭连接
	conn.Close()

	// 执行gonmap扫描
	status, response := scanner.Scan(target, port, scanTimeout)

	// 处理扫描结果
	switch status {
	case gonmap.Closed:
		pr.Status = false
	case gonmap.Unknown:
		pr.Status = true
		pr.Server = "filter"
	default:
		pr.Status = true
	}

	if pr.Status {
		pr.IP = target
		pr.Port = port

		// 解析gonmap响应
		if response != nil && response.FingerPrint != nil {
			pr.Server = response.FingerPrint.Service
			pr.Protocol = response.FingerPrint.Service

			// 如果gonmap没有识别出服务，根据端口号猜测
			if pr.Server == "" {
				pr.Server = GuessServiceByPort(port)
				pr.Protocol = pr.Server
			}

			// 设置链接格式
			if pr.Server == "http" {
				pr.Link = fmt.Sprintf("http://%s:%d", target, port)
			} else if pr.Server == "https" {
				pr.Link = fmt.Sprintf("https://%s:%d", target, port)
			} else {
				pr.Link = fmt.Sprintf("%s://%s:%d", pr.Server, target, port)
			}

			// 对HTTP/HTTPS服务进行额外探测
			if pr.Server == "http" || pr.Server == "https" {
				DetectHttpService(&pr, proxy)
			}
		} else {
			// 如果gonmap没有返回响应，根据端口号猜测服务
			pr.Server = GuessServiceByPort(port)
			pr.Protocol = pr.Server

			// 设置链接格式
			if pr.Server == "http" {
				pr.Link = fmt.Sprintf("http://%s:%d", target, port)
			} else if pr.Server == "https" {
				pr.Link = fmt.Sprintf("https://%s:%d", target, port)
			} else {
				pr.Link = fmt.Sprintf("%s://%s:%d", pr.Server, target, port)
			}
		}
	}

	return pr
}

// GuessServiceByPort 根据端口号猜测服务类型
func GuessServiceByPort(port int) string {
	switch port {
	// 文件传输服务
	case 21, 2121:
		return "ftp"
	case 22, 2222, 10022, 20022:
		return "ssh"
	case 873:
		return "rsync"

	// 远程管理服务
	case 23, 2323:
		return "telnet"
	case 3389, 13389, 23389, 33389, 43389, 53389, 33890, 33891:
		return "rdp"
	case 5900, 5901, 5902, 5903, 5904, 5905, 5906, 5907, 5908, 5909, 5910:
		return "vnc"

	// 邮件服务
	case 25, 465, 587:
		return "smtp"
	case 110, 995:
		return "pop3"
	case 143, 993:
		return "imap"

	// 网络服务
	case 53:
		return "dns"
	case 161, 162:
		return "snmp"
	case 389, 636:
		return "ldap"

	// Web服务
	case 80, 8080, 8000, 8008, 8081, 8888, 8090, 8091, 8092, 8093, 8094, 8095, 8096, 8097, 8098, 8099:
		return "http"
	case 443, 8443:
		return "https"

	// 文件共享服务
	case 139, 445:
		return "smb"
	case 2049:
		return "nfs"

	// 数据库服务
	case 1433, 1434, 2433:
		return "mssql"
	case 1521, 1522, 1523, 1524, 1526, 2483, 2484:
		return "oracle"
	case 3306, 3307, 13306, 23306, 33060:
		return "mysql"
	case 5432, 5433:
		return "postgresql"
	case 6379, 6380, 6381, 6382, 16379, 26379:
		return "redis"
	case 9200, 9300:
		return "elasticsearch"
	case 27017, 27018, 27019, 28017:
		return "mongodb"
	case 4000, 10080:
		return "tidb"
	case 8123, 9000, 9440:
		return "clickhouse"
	case 9042, 9160:
		return "cassandra"
	case 7474, 7687:
		return "neo4j"
	case 11211, 11212:
		return "memcached"
	case 5236, 5237, 5238:
		return "dm"
	case 6432, 54321:
		return "kingbase"

	// 消息队列服务
	case 5672, 15672, 25672, 4369:
		return "rabbitmq"
	case 9092, 9093:
		return "kafka"
	case 61616, 8161:
		return "activemq"
	case 1883, 8883:
		return "mqtt"
	case 2181, 2182, 2183:
		return "zookeeper"

	// 代理服务
	case 1080, 1081, 1090, 10800, 10801, 10808:
		return "socks5"

	// 调试和开发服务
	case 8453, 8454, 8455:
		return "jdwp"
	case 1098, 1099, 10999, 11099:
		return "rmi"
	case 5555, 5037:
		return "adb"

	// 工业控制协议
	case 502, 503, 504:
		return "modbus"

	// 流媒体服务
	case 554, 8554, 10554, 1935:
		return "rtsp"

	// Tomcat AJP
	case 8009, 8019, 8029, 8039, 8049:
		return "ajp"

	default:
		return "tcp"
	}
}

// DetectHttpService 对HTTP/HTTPS服务进行额外探测
func DetectHttpService(pr *PortResult, proxy clients.Proxy) {
	client := clients.DefaultClient()
	if proxy.Enabled {
		proxyURL := proxy.Mode + "://" + proxy.Address + ":" + strconv.Itoa(proxy.Port)
		client = clients.DefaultWithProxyClient(proxyURL)
	}

	if resp, b, err := clients.NewSimpleGetRequest(pr.Link, client); err == nil && resp != nil {
		// 过滤云防护
		if resp.StatusCode == 422 {
			pr.Status = false
		}
		if title := clients.GetTitle(b); title != "" {
			pr.HttpTitle = title
		} else {
			pr.HttpTitle = "-"
		}
	}
}

// WrapperTcpWithTimeout 带超时的TCP连接
func WrapperTcpWithTimeout(network, address string, timeout time.Duration) (net.Conn, error) {
	return net.DialTimeout(network, address, timeout)
}

// WrapperTCP 使用自定义拨号器的TCP连接
func WrapperTCP(network, address string, forward *net.Dialer) (net.Conn, error) {
	return forward.Dial(network, address)
}
