

# Lightx 安全扫描工具

<div align="center">
    <h3>轻量级网络安全扫描与漏洞检测工具</h3>
</div>


## 项目简介

Lightx 是一款轻量级、高效率的网络安全扫描工具，专为安全研究人员和渗透测试工程师设计。它集成了端口扫描、服务识别、Web指纹识别、漏洞扫描和弱口令检测等功能，提供全面的安全评估能力。

### 主要特点

- **多目标扫描**：支持单个URL/IP、IP段、域名和文件列表等多种输入方式
- **高效端口扫描**：快速识别开放端口和服务类型
- **Web指纹识别**：精准识别Web应用程序、框架、CMS等技术栈
- **漏洞检测**：基于Nuclei引擎，支持多种漏洞检测能力
- **弱口令检测**：内置多种服务的弱口令爆破插件
- **插件化架构**：易于扩展的插件系统，支持自定义安全检测模块
- **美观的HTML报告**：生成详细、直观的扫描结果报告
- **低资源占用**：优化的性能设计，适合在资源有限的环境中运行
- **国产数据库支持**: 支持对达梦、人大金仓数据库弱口令检测
- **实时进度条显示**：对于每项任务都能根据任务完成量显示进度条
- **域名扫描增强**：支持直接域名扫描和解析IP扫描两种模式
- **CDN和WAF检测**：自动识别CDN保护和WAF防护，支持真实IP发现
- **IPv6支持**：支持IPv6地址扫描和优先级设置
- **泛解析检测**：自动检测域名泛解析，提高扫描准确性

### 支持插件

Lightx支持以下40种服务的插件：

- **数据库服务**：MySQL、MSSQL、PostgreSQL、Oracle、MongoDB、Redis、Cassandra、Neo4j、达梦(DM)、人大金仓(Kingbase)、TiDB、clickhouse、GaussDB
- **远程管理**：SSH、RDP、Telnet、VNC、ADB
- **Web服务**：Tomcat AJP、Elasticsearch
- **文件传输**：FTP、SMB、RSYNC
- **邮件服务**：SMTP、POP3、IMAP
- **消息队列**：RabbitMQ、Kafka、ActiveMQ、MQTT
- **其他服务**：LDAP、SNMP、Socks5、Memcached、ZooKeeper、Modbus、JDWP、RMI、RTSP
- **安全检测**：MS17010漏洞扫描、网络信息探测

### 待办计划

- [x] **配置文件完全嵌入式加载**：需要修改nuclei引擎
- [ ] **删减nuclei不需要的功能**：缩小工具体积
- [x] **优化报告样式**：提升报告的可读性和美观度
- [ ] **降低go版本至1.20**：支持更多平台，多平台编译打包（目前由于dm、rdp库的原因32位系统无法编译）
- [x] **域名CDN检测功能和域名解析IP功能**：增强域名信息收集能力
- [ ] **网络空间资产引擎加入**：暂不考虑
- [ ] **MCP服务**：支持mcp服务、以及web辅助界面
- [x] **端口扫描支持IPv6、及域名**
- [x] **CDN检测和WAF识别**：自动检测CDN保护和WAF防护
- [ ] **端口扫描代理**：端口扫描、插件扫描支持socks代理（目前只实现了web指纹扫描、漏洞扫描代理逻辑）

## 安装方法

### 直接下载可执行文件

从[发布页面](https://github.com/onewinner/Lightx/releases)下载适合您操作系统的预编译二进制文件。

### 从源码编译

```bash
# 克隆仓库
git clone https://github.com/onewin/Lightx.git

# 进入项目目录
cd Lightx

# 编译项目
go build -o Lightx

# 或使用打包脚本
# Windows
./一键打包.bat

# Linux/MacOS
chmod +x build.sh
./build.sh
```

## 快速开始

### 基本用法

```bash
# 扫描单个URL
Lightx -t http://example.com

# 扫描IP地址
Lightx -t ***********

# 扫描IP段
Lightx -t ***********/24

# 扫描域名
Lightx -t example.com

# 从文件读取目标
Lightx -t targets.txt
```

### 常用场景

```bash
# 完整扫描(端口扫描+Web扫描+漏洞检测)
Lightx -t example.com

# 指定端口范围
Lightx -t *********** -p 80,443,8000-9000

# 使用预定义端口列表
Lightx -t *********** -p web  # 扫描Web相关端口

# 排除扫描端口8080
Lightx -t *********** -p web  -pn 8080

# 禁用存活探测(扫描所有IP)
Lightx -t ***********/24 --np

# 启用无痕模式(减少WAF检测风险)
Lightx -t example.com --st

# 生成HTML报告
Lightx -t example.com -r report.html

# 禁用web扫描
Lightx -t example.com -noweb

# 禁用插件扫描
Lightx -t example.com -nobr

# 禁用web漏洞探测扫描
Lightx -t example.com -vul=false

# 域名扫描相关
# 直接域名扫描（默认）
Lightx -t example.com

# 解析IP模式扫描
Lightx -t example.com --ra

# 只进行域名解析
Lightx -t example.com --dr

# 跳过CDN保护的域名
Lightx -t example.com --sc

# 强制使用真实IP扫描
Lightx -t example.com --frp

# IPv6优先扫描
Lightx -t example.com --ipv6
```

## 使用截图

![image-20250701091507450](./assets/image-20250701091507450.png)

![image-20250622174355408](./assets/image-20250622174355408.png)

![image-20250621230509879](./assets/image-20250621230509879.png)

![image-20250621224236167](./assets/image-20250621224236167.png)

![image-20250622174543444](./assets/image-20250622174543444.png)

![image-20250622174557052](./assets/image-20250622174557052.png)

![image-20250622174613414](./assets/image-20250622174613414.png)

![image-20250622174628319](./assets/image-20250622174628319.png)

![image-20250622174715500](./assets/image-20250622174715500.png)

## 参数说明

```
Lightx.exe -h

    ___       ___       ___       ___       ___       ___
   /\__\     /\  \     /\  \     /\__\     /\  \     /\__\
  /:/  /    _\:\  \   /::\  \   /:/__/_    \:\  \   |::L__L
 /:/__/    /\/::\__\ /:/\:\__\ /::\/\__\   /::\__\ /::::\__\
 \:\  \    \::/\/__/ \:\:\/__/ \/\::/  /  /:/\/__/ \;::;/__/
  \:\__\    \:\__\    \::/  /    /:/  /  /:/__/     |::|__|
   \/__/     \/__/     \/__/     \/__/   \/__/       \/__/

                         Lightx version: 1.3.0   by onewin

NAME:
   Lightx - 一款轻量化的Web指纹识别与漏洞扫描工具 / A lightweight web fingerprint identification and vulnerability scanning tool

USAGE:
   Lightx -u http://127.0.0.1

VERSION:
   1.2.0

COMMANDS:
   help, h  Shows a list of commands or help for one command

GLOBAL OPTIONS:
   -t value, --target value                目标URL或IP地址或IP段或域名 / Target URL, IP address, IP range or domain
   -d, --deep                              启用深度扫描以检查更多指纹，例如nacos、xxl-job / Enable deepScan to check more fingerprints, e.g nacos, xxl-job (default: true)
   --rp, --root-path                       深度指纹扫描是否采用根路径扫描 / Does the deep fingerprint adopt root path scanning (default: true)
   --vul value, --vulnerability value      禁用漏洞扫描或选择扫描模式 / Disable vulnerability scanning or select scan mode (more: 多线程, one: 单线程，false: 禁用) (default: "more")
   --tp value, --template value            模板文件或目录 / Template file or directory
   --th value, --thread value              指纹扫描线程数 / Fingerscan thread count (default: 100)
   --tag value, --tags value               POC标签，例如：cve,sqli / POC tag, e.g: cve,sqli
   --itags value, --include-tags value     包含的POC标签，例如：cve,sqli / Include POC tags, e.g: cve,sqli
   --etags value, --exclude-tags value     排除的POC标签，例如：dos,fuzz / Exclude POC tags, e.g: dos,fuzz (default: "Nginx,Apache-Tomcat")
   --to value, --timeout value             Web请求超时时间（秒） / Web request timeout in seconds (default: 7)
   --dbg, --debug                          显示请求和响应数据包 / Show request and response data packet (default: false)
   --ndns, --nodnslog                      禁用Interactsh引擎（多线程模式下无法禁用） / Disable Interactsh engine (default: false)
   --dns value, --dnslog-server value      自定义DNSLog服务器地址 / Custom DNSLog server address
   --dnstoken value, --dnslog-token value  自定义DNSLog授权令牌 / Custom DNSLog authorization token
   --cd, --condel                          删除配置文件 / Delete configuration file (default: true)
   -x value, --proxy value                 设置代理，例如：http://127.0.0.1:8080 | sock5://127.0.0.1 / Set proxy, e.g: http://127.0.0.1:8080 | sock://127.0.0.1
   -r value, --report value                输出HTML文件路径 / Output HTML file path (default: "2025-06-22 17-32-46.html")
   -o value, --output value                输出日志记录 / Output log (default: "log.txt")
   --np, --noping                          禁用存活探测，对所有IP进行端口扫描 / Disable alive detection, scan all IPs (default: false)
   -p value, --port value                  指定端口扫描范围，例如：80,443,8080-8090，或使用预定义列表：db(数据库)、web(网站)、mail(邮件)、remote(远程管理)、file(文件服务)、mq(消息队列)、vuln(漏洞)、unauth(未授权)、top100(常用端口)、plugin(所有插件端口) / Specify port scan range, e.g: 80,443,8080-8090, or use predefined lists: db, web, mail, remote, file, mq, vuln, unauth, top100, plugin (default: "default")
   --pn value, --exclude-port value        指定不需要扫描的端口范围，例如：8080,8443,9000-9100 / Specify ports to exclude from scanning, e.g: 8080,8443,9000-9100
   --pf, --portfinger                      启用服务指纹识别 / Enable service fingerprint identification (default: true)
   --nw, --noweb                           禁用Web扫描，仅执行IP/端口扫描 / Disable web scanning, only perform IP/port scanning (default: false)
   --rport, --randomport                   随机化端口扫描顺序 / Randomize port scan order (default: false)
   --tt value, --tcp-threads value         TCP端口扫描线程数 / TCP port scan thread count (default: 1000)
   --tm value, --tcp-timeout value         TCP端口扫描超时时间（秒） / TCP port scan timeout in seconds (default: 5)
   --pt value, --ports-threshold value     端口阈值，超过此值认为可能是防火墙 / Port threshold, if exceeded, it may be a firewall (default: 1000)
   --df, --disable-fastjson                禁用Fastjson探测 / Disable Fastjson detection (default: false)
   --ds, --disable-shiro                   禁用Shiro探测 / Disable Shiro detection (default: false)
   --l4j, --log4j                          启用Log4j2、SpringBoot-Actuator未授权检查漏洞检查 / Enable Log4j2、SpringBoot-Actuator vulnerability check (default: true)
   --st, --stealth                         无痕模式，关闭所有主动探测(Fastjson、Shiro、深度指纹扫描、Log4j)以防止WAF封禁 / Stealth mode, disable all active detection to prevent WAF blocking (default: false)
   --cr value, --crawler value             爬虫模式：auto（自动检测需要JS的网站）、force（强制所有网站使用爬虫）、off（完全禁用爬虫） / Crawler mode: auto, force, off (default: "auto")
   --crd, --crawler-debug                  启用爬虫调试模式，输出详细日志（使用[crawler]前缀） / Enable crawler debug mode with detailed logs (default: false)
   --nobr, --noplugin                      禁用插件系统 / Disable plugin system (default: false)
   --user value                            指定用户名列表，可以是逗号分隔的字符串或文件路径（每行一个用户名）/ Specify usernames, comma-separated string or file path (one username per line)
   --pwd value                             指定密码列表，可以是逗号分隔的字符串或文件路径（每行一个密码）/ Specify passwords, comma-separated string or file path (one password per line)
   --usera value                           追加用户名列表，可以是逗号分隔的字符串或文件路径（每行一个用户名）/ Append usernames, comma-separated string or file path (one username per line)
   --pwda value                            追加密码列表，可以是逗号分隔的字符串或文件路径（每行一个密码）/ Append passwords, comma-separated string or file path (one password per line)
   --domain value                          设置SMB认证的域名 / Set domain name for SMB authentication
   --help, -h                              show help
   --version, -v                           print the version
```



### 目标设置
| 参数 | 说明 |
|------|------|
| `-t, --target` | 目标URL、IP地址、IP段或域名 |
| `-p, --port` | 端口扫描范围，如：80,443,8000-9000 或预定义列表：web,db,top100 |
| `--pn, --exclude-port` | 排除指定端口，如：8080,8443 |
| `--np, --noping` | 禁用存活探测，对所有IP进行端口扫描 |

### 扫描控制
| 参数 | 说明 |
|------|------|
| `-d, --deep` | 启用深度扫描以检查更多指纹 (默认: true) |
| `--rp, --root-path` | 深度指纹扫描是否采用根路径扫描 (默认: true) |
| `--vul` | 漏洞扫描模式：more(多线程)、one(单线程)、false(禁用) |
| `--nw, --noweb` | 禁用Web扫描，仅执行IP/端口扫描 |
| `--st, --stealth` | 无痕模式，关闭所有主动探测以防止WAF封禁 |
| `--nobr, --noplugin` | 禁用插件系统 |

### 性能设置
| 参数 | 说明 |
|------|------|
| `--th, --thread` | 指纹扫描线程数 (默认: 100) |
| `--to, --timeout` | Web请求超时时间(秒) (默认: 7) |
| `--tt, --tcp-threads` | TCP端口扫描线程数 (默认: 1000) |
| `--tm, --tcp-timeout` | TCP端口扫描超时时间(秒) (默认: 5) |
| `--rport, --randomport` | 随机化端口扫描顺序 |

### 漏洞扫描
| 参数 | 说明 |
|------|------|
| `--tag, --tags` | POC标签，如：cve,sqli |
| `--itags, --include-tags` | 包含的POC标签 |
| `--etags, --exclude-tags` | 排除的POC标签 (默认: "Nginx,Apache-Tomcat") |
| `--tp, --template` | 模板文件或目录 |
| `--l4j, --log4j` | 启用Log4j2、SpringBoot-Actuator漏洞检查 (默认: true) |

### 爆破设置
| 参数 | 说明 |
|------|------|
| `--user` | 指定用户名列表，逗号分隔或文件路径 |
| `--pwd` | 指定密码列表，逗号分隔或文件路径 |
| `--usera` | 追加用户名列表 |
| `--pwda` | 追加密码列表 |
| `--domain` | 设置SMB认证的域名 |

### 域名扫描
| 参数 | 说明 |
|------|------|
| `--dr, --domain-resolve` | 只进行域名解析，不进行端口扫描 |
| `--ra, --resolve-all` | 使用解析IP模式：先解析域名为IP再扫描（默认使用直接域名扫描） |
| `--ipv6, --prefer-ipv6` | 优先使用IPv6地址进行扫描 |
| `--skip-cdn, --sc` | 跳过CDN保护的域名 (默认: true) |
| `--force-real-ip, --fri` | 强制使用真实IP进行扫描（尝试绕过CDN） |

### 输出控制
| 参数 | 说明 |
|------|------|
| `-r, --report` | 输出HTML报告文件路径 |
| `-o, --output` | 输出日志文件 (默认: "log.txt") |
| `--dbg, --debug` | 显示请求和响应数据包 |
| `--crd, --crawler-debug` | 启用爬虫调试模式 |

### 其他选项
| 参数 | 说明 |
|------|------|
| `-x, --proxy` | 设置代理，如：http://127.0.0.1:8080 |
| `--cd, --condel` | 扫描结束后删除临时配置文件 (默认: true) |
| `--cr, --crawler` | 爬虫模式：auto、force、off (默认: "auto") |
| `--dns, --dnslog-server` | 自定义DNSLog服务器地址 |
| `--dnstoken, --dnslog-token` | 自定义DNSLog授权令牌 |

## 预定义端口列表

Lightx 支持以下预定义端口列表：

- `db`: 数据库相关端口 (MySQL, MSSQL, PostgreSQL等)
- `web`: Web服务相关端口 (80, 443, 8080等)
- `mail`: 邮件服务相关端口 (25, 110, 143等)
- `remote`: 远程管理相关端口 (22, 3389等)
- `file`: 文件服务相关端口 (21, 445等)
- `mq`: 消息队列相关端口 (5672, 61616等)
- `vuln`: 常见漏洞端口
- `unauth`: 常见未授权访问端口
- `top100`: 常用前100个端口
- `plugin`: 所有插件支持的端口
- `default`: 默认扫描端口

## 扫描报告

Lightx 生成的HTML报告包含以下信息：

1. **扫描概览**：扫描目标、时间、发现的漏洞数量等
2. **端口扫描结果**：开放端口、服务类型、版本信息
3. **Web指纹识别**：识别到的Web应用、框架、CMS等
4. **漏洞信息**：发现的安全漏洞详情
5. **爆破结果**：弱口令检测结果
6. **统计信息**：各类扫描结果的统计数据

## 使用示例

### 完整扫描流程

```bash
# 对目标网站进行完整扫描
Lightx -t https://example.com -r report.html
```

### 特定漏洞扫描

```bash
# 仅扫描SQL注入和XSS漏洞
Lightx -t https://example.com --tag sqli,xss
```

### 多目标批量扫描

```bash
# 创建targets.txt文件，每行一个目标
echo "example.com" > targets.txt
echo "***********" >> targets.txt
echo "https://test.com" >> targets.txt

# 执行批量扫描
Lightx -t targets.txt -r batch_report.html
```

### 自定义爆破字典

```bash
# 使用自定义用户名和密码列表
Lightx -t *********** --user users.txt --pwd passwords.txt
```

### 域名扫描功能

```bash
# 直接域名扫描（默认模式）
Lightx -t example.com

# 解析IP模式扫描（先解析域名为IP再扫描）
Lightx -t example.com --ra

# 只进行域名解析，不扫描端口
Lightx -t example.com --dr

# 优先使用IPv6地址扫描
Lightx -t example.com --ipv6

# 跳过CDN保护的域名
Lightx -t example.com --skip-cdn

# 强制使用真实IP扫描（尝试绕过CDN）
Lightx -t example.com --force-real-ip
```

## 插件系统

Lightx 提供了强大的插件系统，支持自定义扩展功能。详细的插件开发指南请参考 [插件开发文档](./plugins/README.md)。

## 常见问题

**Q: 如何减少扫描被WAF拦截的风险？**  
A: 使用 `--st` 参数启用无痕模式，它会禁用所有主动探测功能。

**Q: 如何加快扫描速度？**  
A: 可以增加线程数 `--th 200`，或减少超时时间 `--to 3`，但可能会影响准确性。

**Q: 为什么没有发现任何漏洞？**  
A: 检查是否使用了正确的标签 `--tag`，或尝试使用 `--deep` 进行深度扫描。

**Q: 如何只扫描特定服务？**  
A: 使用 `-p` 参数指定端口，如 `-p 3306` 只扫描MySQL服务。

**Q: 为什么web扫描时探测不到指纹或漏洞？**
A: 工具的配置文件会在运行时会在当前目录生产config、里面包含pocs、webfinger、dir配置，扫不到指纹说明webfinger配置不存在。

**Q: 域名扫描时应该选择哪种模式？**
A: 默认使用直接域名扫描模式，如果需要获取更详细的DNS信息或绕过CDN，可以使用 `--ra` 参数启用解析IP模式。

**Q: 如何处理CDN保护的域名？**
A: 可以使用 `--force-real-ip` 尝试发现真实IP，或使用 `--skip-cdn` 跳过CDN保护的域名。

**Q: IPv6扫描有什么注意事项？**
A: IPv6扫描功能目前处于测试阶段，建议在测试环境中使用。可以使用 `--ipv6` 参数优先扫描IPv6地址。

**Q: WAF检测结果如何理解？**
A: 工具会自动识别常见的WAF产品，如果检测到WAF，建议启用无痕模式 `--st` 以减少被拦截的风险。

## 版本更新

### 1.4.0

1. **报告优化**：优化HTML报告中爆破结果显示，新增导出格式支持XLSX表格
2. **IPv6支持**：新增支持IPv6扫描探测（测试阶段，可能存在未知bug）
3. **域名扫描增强**：
   - 支持直接域名端口扫描和域名解析IP端口扫描两种模式
   - 新增 `--ra` 参数：解析IP模式，先解析域名为IP再扫描
   - 新增 `--dr` 参数：只进行域名解析，不进行端口扫描
   - 新增 `--ipv6` 参数：优先使用IPv6地址进行扫描
4. **CDN和WAF检测**：
   - 实现CDN检测功能，支持主流CDN提供商识别
   - 新增 `--skip-cdn` 参数：跳过CDN保护的域名
   - 新增 `--force-real-ip` 参数：强制使用真实IP进行扫描
   - 实现子域名泛解析检测和真实IP发现功能
5. **指纹扫描增强**：
   - 新增CDN过滤功能（状态码422检测）
   - 新增WAF检测功能，支持识别多种WAF产品
   - 支持的WAF包括：阿里云、腾讯云、百度云、360、知道创宇、华为云等
6. **配置文件完全嵌入式加载**：优先从本地加载，如果本地当前目录下不存在config则默认使用embed
7. **工具启动时间校验**：（防止他人使用）（ -tags auth -ldflags "-s -w -X 'main.Version=92a71fa' -X 'main.BuildTime=2025-07-11 01:56:53' -X 'Lightx/internal/common.ValidMinutesStr=1' -X 'Lightx/internal/common.BuildTimestamp=2025-07-11 01:56:53'"）(在编译时`-tags auth`启用，正常编译不会启用)
   - **反调试检测**：检测调试器存在、父进程分析、时序攻击检测、虚拟机环境检测
   - **完整性校验**：验证可执行文件是否被修改或篡改
   - **代码混淆**：使用虚假变量、间接调用、控制流混淆等技术
   - **编译时间戳验证**：基于编译时嵌入的时间戳，防止文件时间被篡改
   - **可执行文件时间检查**：基于文件的修改时间计算有效期，包含异常时间检测
   - **时间锚点检查**：创建隐藏的时间锚点文件，防止系统时间被往前调整
   - **时间一致性检查**：通过创建临时文件验证系统时间的一致性

8. **过滤web扫描子域名泛解析**：导致于扫描大量重复目标问题（待测试）
   - 新增`-nwc`参数：禁用过滤子域名泛解析功能

9. **cdn检测逻辑完善**：
   - 扫到域名存在cdn时只扫描常见web端口
   - cdn检测增强，新增正则匹配以及新增配置字典

10. **重构项目结构**：使代码文件逻辑清晰

11. **修复YAML指纹数量显示和部分漏洞poc没有tags标签**

### 1.3.3

1. 新增国产数据库gaussdb爆破插件及其端口指纹识别
2. 新增参数-m用来指定调用特定插件，使用-m参数时必须得指定-p
3. 修复getRuleData 函数中可能导致 slice bounds out of range [11:10] 错误的问题
4. 由于1.3.1删除了（空间搜索引擎、fuzz功能、程序更新功能 Headless 浏览器和 JavaScript 引擎功能）导致于flow:语法poc无法执行，目前先暂时还原引擎，后续再重新删减

### 1.3.2

1. 修复-noweb时爆破存在结果时写入报告不存在bug
2. 优化ssh、smb爆破逻辑先爆破用户空口令在爆密码
3. 优化CVE-2020-1938 Apache Tomcat AJP文件读取漏洞检测插件读取响应逻辑
4. 新增国产数据库TiDB爆破插件及其端口指纹识别
5. 新增支持clickhouse数据库爆破插件及其端口指纹识别

### 1.3.1

1. 新增rtsp爆破插件
2. 新增-pp插件自定义参数、-bt爆破线程数、-bto爆破超时时间、-bma最大爆破尝试次数，命令参数参数
3. 优化部分代码逻辑
4. 删除部分nuclei无用引擎代码，减小程序体积
