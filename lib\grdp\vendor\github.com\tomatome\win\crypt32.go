// This file was automatically generated by https://github.com/kbinani/win/blob/generator/internal/cmd/gen/gen.go
// go run internal/cmd/gen/gen.go

// +build windows

package win

import (
	"unsafe"
)

var (
	// Library
	libcrypt32 uintptr

	// Functions
	certAddCRLContextToStore                           uintptr
	certAddCRLLinkToStore                              uintptr
	certAddCTLContextToStore                           uintptr
	certAddCTLLinkToStore                              uintptr
	certAddCertificateContextToStore                   uintptr
	certAddCertificateLinkToStore                      uintptr
	certAddEncodedCRLToStore                           uintptr
	certAddEncodedCTLToStore                           uintptr
	certAddEncodedCertificateToStore                   uintptr
	certAddEncodedCertificateToSystemStore             uintptr
	certAddEnhancedKeyUsageIdentifier                  uintptr
	certAddSerializedElementToStore                    uintptr
	certAddStoreToCollection                           uintptr
	certAlgIdToOID                                     uintptr
	certCloseStore                                     uintptr
	certCompareCertificate                             uintptr
	certCompareCertificateName                         uintptr
	certCompareIntegerBlob                             uintptr
	certControlStore                                   uintptr
	certCreateCRLContext                               uintptr
	certCreateCTLContext                               uintptr
	certCreateCTLEntryFromCertificateContextProperties uintptr
	certCreateCertificateContext                       uintptr
	certDeleteCRLFromStore                             uintptr
	certDeleteCTLFromStore                             uintptr
	certDeleteCertificateFromStore                     uintptr
	certDuplicateCRLContext                            uintptr
	certDuplicateCTLContext                            uintptr
	certDuplicateCertificateContext                    uintptr
	certDuplicateStore                                 uintptr
	certEnumCRLContextProperties                       uintptr
	certEnumCRLsInStore                                uintptr
	certEnumCTLContextProperties                       uintptr
	certEnumCTLsInStore                                uintptr
	certEnumCertificateContextProperties               uintptr
	certEnumCertificatesInStore                        uintptr
	certEnumSubjectInSortedCTL                         uintptr
	certFindAttribute                                  uintptr
	certFindCRLInStore                                 uintptr
	certFindCTLInStore                                 uintptr
	certFindCertificateInCRL                           uintptr
	certFindCertificateInStore                         uintptr
	certFindExtension                                  uintptr
	certFindSubjectInCTL                               uintptr
	certFindSubjectInSortedCTL                         uintptr
	certFreeCRLContext                                 uintptr
	certFreeCTLContext                                 uintptr
	certFreeCertificateContext                         uintptr
	certGetCRLContextProperty                          uintptr
	certGetCRLFromStore                                uintptr
	certGetCTLContextProperty                          uintptr
	certGetCertificateContextProperty                  uintptr
	certGetIntendedKeyUsage                            uintptr
	certGetIssuerCertificateFromStore                  uintptr
	certGetNameString                                  uintptr
	certGetStoreProperty                               uintptr
	certGetSubjectCertificateFromStore                 uintptr
	certGetValidUsages                                 uintptr
	certIsValidCRLForCertificate                       uintptr
	certNameToStr                                      uintptr
	certOIDToAlgId                                     uintptr
	certRDNValueToStr                                  uintptr
	certRemoveEnhancedKeyUsageIdentifier               uintptr
	certRemoveStoreFromCollection                      uintptr
	certSaveStore                                      uintptr
	certSerializeCRLStoreElement                       uintptr
	certSerializeCTLStoreElement                       uintptr
	certSerializeCertificateStoreElement               uintptr
	certSetCRLContextProperty                          uintptr
	certSetCTLContextProperty                          uintptr
	certSetCertificateContextPropertiesFromCTLEntry    uintptr
	certSetCertificateContextProperty                  uintptr
	certSetStoreProperty                               uintptr
	certStrToName                                      uintptr
	certUnregisterPhysicalStore                        uintptr
	certUnregisterSystemStore                          uintptr
	certVerifyCRLRevocation                            uintptr
	certVerifyCRLTimeValidity                          uintptr
	certVerifySubjectCertificateContext                uintptr
	certVerifyTimeValidity                             uintptr
	certVerifyValidityNesting                          uintptr
	cryptBinaryToString                                uintptr
	cryptDecodeObject                                  uintptr
	cryptEncodeObject                                  uintptr
	cryptExportPKCS8                                   uintptr
	cryptFindCertificateKeyProvInfo                    uintptr
	cryptFindLocalizedName                             uintptr
	cryptFormatObject                                  uintptr
	cryptGetKeyIdentifierProperty                      uintptr
	cryptGetMessageSignerCount                         uintptr
	cryptGetOIDFunctionValue                           uintptr
	cryptMemAlloc                                      uintptr
	cryptMemFree                                       uintptr
	cryptMemRealloc                                    uintptr
	cryptMsgCalculateEncodedLength                     uintptr
	cryptMsgClose                                      uintptr
	cryptMsgControl                                    uintptr
	cryptMsgDuplicate                                  uintptr
	cryptMsgGetAndVerifySigner                         uintptr
	cryptMsgGetParam                                   uintptr
	cryptMsgUpdate                                     uintptr
	cryptProtectMemory                                 uintptr
	cryptQueryObject                                   uintptr
	cryptRegisterDefaultOIDFunction                    uintptr
	cryptRegisterOIDFunction                           uintptr
	cryptSIPRemoveProvider                             uintptr
	cryptSIPRetrieveSubjectGuid                        uintptr
	cryptSIPRetrieveSubjectGuidForCatalogFile          uintptr
	cryptSetKeyIdentifierProperty                      uintptr
	cryptSetOIDFunctionValue                           uintptr
	cryptStringToBinary                                uintptr
	cryptUnprotectMemory                               uintptr
	cryptUnregisterDefaultOIDFunction                  uintptr
	cryptUnregisterOIDFunction                         uintptr
	pFXExportCertStore                                 uintptr
	pFXExportCertStoreEx                               uintptr
	pFXImportCertStore                                 uintptr
	pFXIsPFXBlob                                       uintptr
	pFXVerifyPassword                                  uintptr
	i_CertUpdateStore                                  uintptr
	i_CryptAllocTls                                    uintptr
	i_CryptDetachTls                                   uintptr
	i_CryptFindLruEntry                                uintptr
	i_CryptFindLruEntryData                            uintptr
	i_CryptFreeTls                                     uintptr
	i_CryptGetDefaultCryptProv                         uintptr
	i_CryptGetOssGlobal                                uintptr
	i_CryptGetTls                                      uintptr
	i_CryptInstallOssGlobal                            uintptr
	i_CryptReadTrustedPublisherDWORDValueFromRegistry  uintptr
	i_CryptSetTls                                      uintptr
)

func init() {
	// Library
	libcrypt32 = doLoadLibrary("crypt32.dll")

	// Functions
	certAddCRLContextToStore = doGetProcAddress(libcrypt32, "CertAddCRLContextToStore")
	certAddCRLLinkToStore = doGetProcAddress(libcrypt32, "CertAddCRLLinkToStore")
	certAddCTLContextToStore = doGetProcAddress(libcrypt32, "CertAddCTLContextToStore")
	certAddCTLLinkToStore = doGetProcAddress(libcrypt32, "CertAddCTLLinkToStore")
	certAddCertificateContextToStore = doGetProcAddress(libcrypt32, "CertAddCertificateContextToStore")
	certAddCertificateLinkToStore = doGetProcAddress(libcrypt32, "CertAddCertificateLinkToStore")
	certAddEncodedCRLToStore = doGetProcAddress(libcrypt32, "CertAddEncodedCRLToStore")
	certAddEncodedCTLToStore = doGetProcAddress(libcrypt32, "CertAddEncodedCTLToStore")
	certAddEncodedCertificateToStore = doGetProcAddress(libcrypt32, "CertAddEncodedCertificateToStore")
	certAddEncodedCertificateToSystemStore = doGetProcAddress(libcrypt32, "CertAddEncodedCertificateToSystemStoreW")
	certAddEnhancedKeyUsageIdentifier = doGetProcAddress(libcrypt32, "CertAddEnhancedKeyUsageIdentifier")
	certAddSerializedElementToStore = doGetProcAddress(libcrypt32, "CertAddSerializedElementToStore")
	certAddStoreToCollection = doGetProcAddress(libcrypt32, "CertAddStoreToCollection")
	certAlgIdToOID = doGetProcAddress(libcrypt32, "CertAlgIdToOID")
	certCloseStore = doGetProcAddress(libcrypt32, "CertCloseStore")
	certCompareCertificate = doGetProcAddress(libcrypt32, "CertCompareCertificate")
	certCompareCertificateName = doGetProcAddress(libcrypt32, "CertCompareCertificateName")
	certCompareIntegerBlob = doGetProcAddress(libcrypt32, "CertCompareIntegerBlob")
	certControlStore = doGetProcAddress(libcrypt32, "CertControlStore")
	certCreateCRLContext = doGetProcAddress(libcrypt32, "CertCreateCRLContext")
	certCreateCTLContext = doGetProcAddress(libcrypt32, "CertCreateCTLContext")
	certCreateCTLEntryFromCertificateContextProperties = doGetProcAddress(libcrypt32, "CertCreateCTLEntryFromCertificateContextProperties")
	certCreateCertificateContext = doGetProcAddress(libcrypt32, "CertCreateCertificateContext")
	certDeleteCRLFromStore = doGetProcAddress(libcrypt32, "CertDeleteCRLFromStore")
	certDeleteCTLFromStore = doGetProcAddress(libcrypt32, "CertDeleteCTLFromStore")
	certDeleteCertificateFromStore = doGetProcAddress(libcrypt32, "CertDeleteCertificateFromStore")
	certDuplicateCRLContext = doGetProcAddress(libcrypt32, "CertDuplicateCRLContext")
	certDuplicateCTLContext = doGetProcAddress(libcrypt32, "CertDuplicateCTLContext")
	certDuplicateCertificateContext = doGetProcAddress(libcrypt32, "CertDuplicateCertificateContext")
	certDuplicateStore = doGetProcAddress(libcrypt32, "CertDuplicateStore")
	certEnumCRLContextProperties = doGetProcAddress(libcrypt32, "CertEnumCRLContextProperties")
	certEnumCRLsInStore = doGetProcAddress(libcrypt32, "CertEnumCRLsInStore")
	certEnumCTLContextProperties = doGetProcAddress(libcrypt32, "CertEnumCTLContextProperties")
	certEnumCTLsInStore = doGetProcAddress(libcrypt32, "CertEnumCTLsInStore")
	certEnumCertificateContextProperties = doGetProcAddress(libcrypt32, "CertEnumCertificateContextProperties")
	certEnumCertificatesInStore = doGetProcAddress(libcrypt32, "CertEnumCertificatesInStore")
	certEnumSubjectInSortedCTL = doGetProcAddress(libcrypt32, "CertEnumSubjectInSortedCTL")
	certFindAttribute = doGetProcAddress(libcrypt32, "CertFindAttribute")
	certFindCRLInStore = doGetProcAddress(libcrypt32, "CertFindCRLInStore")
	certFindCTLInStore = doGetProcAddress(libcrypt32, "CertFindCTLInStore")
	certFindCertificateInCRL = doGetProcAddress(libcrypt32, "CertFindCertificateInCRL")
	certFindCertificateInStore = doGetProcAddress(libcrypt32, "CertFindCertificateInStore")
	certFindExtension = doGetProcAddress(libcrypt32, "CertFindExtension")
	certFindSubjectInCTL = doGetProcAddress(libcrypt32, "CertFindSubjectInCTL")
	certFindSubjectInSortedCTL = doGetProcAddress(libcrypt32, "CertFindSubjectInSortedCTL")
	certFreeCRLContext = doGetProcAddress(libcrypt32, "CertFreeCRLContext")
	certFreeCTLContext = doGetProcAddress(libcrypt32, "CertFreeCTLContext")
	certFreeCertificateContext = doGetProcAddress(libcrypt32, "CertFreeCertificateContext")
	certGetCRLContextProperty = doGetProcAddress(libcrypt32, "CertGetCRLContextProperty")
	certGetCRLFromStore = doGetProcAddress(libcrypt32, "CertGetCRLFromStore")
	certGetCTLContextProperty = doGetProcAddress(libcrypt32, "CertGetCTLContextProperty")
	certGetCertificateContextProperty = doGetProcAddress(libcrypt32, "CertGetCertificateContextProperty")
	certGetIntendedKeyUsage = doGetProcAddress(libcrypt32, "CertGetIntendedKeyUsage")
	certGetIssuerCertificateFromStore = doGetProcAddress(libcrypt32, "CertGetIssuerCertificateFromStore")
	certGetNameString = doGetProcAddress(libcrypt32, "CertGetNameStringW")
	certGetStoreProperty = doGetProcAddress(libcrypt32, "CertGetStoreProperty")
	certGetSubjectCertificateFromStore = doGetProcAddress(libcrypt32, "CertGetSubjectCertificateFromStore")
	certGetValidUsages = doGetProcAddress(libcrypt32, "CertGetValidUsages")
	certIsValidCRLForCertificate = doGetProcAddress(libcrypt32, "CertIsValidCRLForCertificate")
	certNameToStr = doGetProcAddress(libcrypt32, "CertNameToStrW")
	certOIDToAlgId = doGetProcAddress(libcrypt32, "CertOIDToAlgId")
	certRDNValueToStr = doGetProcAddress(libcrypt32, "CertRDNValueToStrW")
	certRemoveEnhancedKeyUsageIdentifier = doGetProcAddress(libcrypt32, "CertRemoveEnhancedKeyUsageIdentifier")
	certRemoveStoreFromCollection = doGetProcAddress(libcrypt32, "CertRemoveStoreFromCollection")
	certSaveStore = doGetProcAddress(libcrypt32, "CertSaveStore")
	certSerializeCRLStoreElement = doGetProcAddress(libcrypt32, "CertSerializeCRLStoreElement")
	certSerializeCTLStoreElement = doGetProcAddress(libcrypt32, "CertSerializeCTLStoreElement")
	certSerializeCertificateStoreElement = doGetProcAddress(libcrypt32, "CertSerializeCertificateStoreElement")
	certSetCRLContextProperty = doGetProcAddress(libcrypt32, "CertSetCRLContextProperty")
	certSetCTLContextProperty = doGetProcAddress(libcrypt32, "CertSetCTLContextProperty")
	certSetCertificateContextPropertiesFromCTLEntry = doGetProcAddress(libcrypt32, "CertSetCertificateContextPropertiesFromCTLEntry")
	certSetCertificateContextProperty = doGetProcAddress(libcrypt32, "CertSetCertificateContextProperty")
	certSetStoreProperty = doGetProcAddress(libcrypt32, "CertSetStoreProperty")
	certStrToName = doGetProcAddress(libcrypt32, "CertStrToNameW")
	certUnregisterPhysicalStore = doGetProcAddress(libcrypt32, "CertUnregisterPhysicalStore")
	certUnregisterSystemStore = doGetProcAddress(libcrypt32, "CertUnregisterSystemStore")
	certVerifyCRLRevocation = doGetProcAddress(libcrypt32, "CertVerifyCRLRevocation")
	certVerifyCRLTimeValidity = doGetProcAddress(libcrypt32, "CertVerifyCRLTimeValidity")
	certVerifySubjectCertificateContext = doGetProcAddress(libcrypt32, "CertVerifySubjectCertificateContext")
	certVerifyTimeValidity = doGetProcAddress(libcrypt32, "CertVerifyTimeValidity")
	certVerifyValidityNesting = doGetProcAddress(libcrypt32, "CertVerifyValidityNesting")
	cryptBinaryToString = doGetProcAddress(libcrypt32, "CryptBinaryToStringW")
	cryptDecodeObject = doGetProcAddress(libcrypt32, "CryptDecodeObject")
	cryptEncodeObject = doGetProcAddress(libcrypt32, "CryptEncodeObject")
	cryptExportPKCS8 = doGetProcAddress(libcrypt32, "CryptExportPKCS8")
	cryptFindCertificateKeyProvInfo = doGetProcAddress(libcrypt32, "CryptFindCertificateKeyProvInfo")
	cryptFindLocalizedName = doGetProcAddress(libcrypt32, "CryptFindLocalizedName")
	cryptFormatObject = doGetProcAddress(libcrypt32, "CryptFormatObject")
	cryptGetKeyIdentifierProperty = doGetProcAddress(libcrypt32, "CryptGetKeyIdentifierProperty")
	cryptGetMessageSignerCount = doGetProcAddress(libcrypt32, "CryptGetMessageSignerCount")
	cryptGetOIDFunctionValue = doGetProcAddress(libcrypt32, "CryptGetOIDFunctionValue")
	cryptMemAlloc = doGetProcAddress(libcrypt32, "CryptMemAlloc")
	cryptMemFree = doGetProcAddress(libcrypt32, "CryptMemFree")
	cryptMemRealloc = doGetProcAddress(libcrypt32, "CryptMemRealloc")
	cryptMsgCalculateEncodedLength = doGetProcAddress(libcrypt32, "CryptMsgCalculateEncodedLength")
	cryptMsgClose = doGetProcAddress(libcrypt32, "CryptMsgClose")
	cryptMsgControl = doGetProcAddress(libcrypt32, "CryptMsgControl")
	cryptMsgDuplicate = doGetProcAddress(libcrypt32, "CryptMsgDuplicate")
	cryptMsgGetAndVerifySigner = doGetProcAddress(libcrypt32, "CryptMsgGetAndVerifySigner")
	cryptMsgGetParam = doGetProcAddress(libcrypt32, "CryptMsgGetParam")
	cryptMsgUpdate = doGetProcAddress(libcrypt32, "CryptMsgUpdate")
	cryptProtectMemory = doGetProcAddress(libcrypt32, "CryptProtectMemory")
	cryptQueryObject = doGetProcAddress(libcrypt32, "CryptQueryObject")
	cryptRegisterDefaultOIDFunction = doGetProcAddress(libcrypt32, "CryptRegisterDefaultOIDFunction")
	cryptRegisterOIDFunction = doGetProcAddress(libcrypt32, "CryptRegisterOIDFunction")
	cryptSIPRemoveProvider = doGetProcAddress(libcrypt32, "CryptSIPRemoveProvider")
	cryptSIPRetrieveSubjectGuid = doGetProcAddress(libcrypt32, "CryptSIPRetrieveSubjectGuid")
	cryptSIPRetrieveSubjectGuidForCatalogFile = doGetProcAddress(libcrypt32, "CryptSIPRetrieveSubjectGuidForCatalogFile")
	cryptSetKeyIdentifierProperty = doGetProcAddress(libcrypt32, "CryptSetKeyIdentifierProperty")
	cryptSetOIDFunctionValue = doGetProcAddress(libcrypt32, "CryptSetOIDFunctionValue")
	cryptStringToBinary = doGetProcAddress(libcrypt32, "CryptStringToBinaryW")
	cryptUnprotectMemory = doGetProcAddress(libcrypt32, "CryptUnprotectMemory")
	cryptUnregisterDefaultOIDFunction = doGetProcAddress(libcrypt32, "CryptUnregisterDefaultOIDFunction")
	cryptUnregisterOIDFunction = doGetProcAddress(libcrypt32, "CryptUnregisterOIDFunction")
	pFXExportCertStore = doGetProcAddress(libcrypt32, "PFXExportCertStore")
	pFXExportCertStoreEx = doGetProcAddress(libcrypt32, "PFXExportCertStoreEx")
	pFXImportCertStore = doGetProcAddress(libcrypt32, "PFXImportCertStore")
	pFXIsPFXBlob = doGetProcAddress(libcrypt32, "PFXIsPFXBlob")
	pFXVerifyPassword = doGetProcAddress(libcrypt32, "PFXVerifyPassword")
	i_CertUpdateStore = doGetProcAddress(libcrypt32, "I_CertUpdateStore")
	i_CryptAllocTls = doGetProcAddress(libcrypt32, "I_CryptAllocTls")
	i_CryptDetachTls = doGetProcAddress(libcrypt32, "I_CryptDetachTls")
	i_CryptFindLruEntry = doGetProcAddress(libcrypt32, "I_CryptFindLruEntry")
	i_CryptFindLruEntryData = doGetProcAddress(libcrypt32, "I_CryptFindLruEntryData")
	i_CryptFreeTls = doGetProcAddress(libcrypt32, "I_CryptFreeTls")
	i_CryptGetDefaultCryptProv = doGetProcAddress(libcrypt32, "I_CryptGetDefaultCryptProv")
	i_CryptGetOssGlobal = doGetProcAddress(libcrypt32, "I_CryptGetOssGlobal")
	i_CryptGetTls = doGetProcAddress(libcrypt32, "I_CryptGetTls")
	i_CryptInstallOssGlobal = doGetProcAddress(libcrypt32, "I_CryptInstallOssGlobal")
	i_CryptReadTrustedPublisherDWORDValueFromRegistry = doGetProcAddress(libcrypt32, "I_CryptReadTrustedPublisherDWORDValueFromRegistry")
	i_CryptSetTls = doGetProcAddress(libcrypt32, "I_CryptSetTls")
}

func CertAddCRLContextToStore(hCertStore HCERTSTORE, pCrlContext PCCRL_CONTEXT, dwAddDisposition DWORD, ppStoreContext *PCCRL_CONTEXT) bool {
	ret1 := syscall6(certAddCRLContextToStore, 4,
		uintptr(hCertStore),
		uintptr(unsafe.Pointer(pCrlContext)),
		uintptr(dwAddDisposition),
		uintptr(unsafe.Pointer(ppStoreContext)),
		0,
		0)
	return ret1 != 0
}

func CertAddCRLLinkToStore(hCertStore HCERTSTORE, pCrlContext PCCRL_CONTEXT, dwAddDisposition DWORD, ppStoreContext *PCCRL_CONTEXT) bool {
	ret1 := syscall6(certAddCRLLinkToStore, 4,
		uintptr(hCertStore),
		uintptr(unsafe.Pointer(pCrlContext)),
		uintptr(dwAddDisposition),
		uintptr(unsafe.Pointer(ppStoreContext)),
		0,
		0)
	return ret1 != 0
}

func CertAddCTLContextToStore(hCertStore HCERTSTORE, pCtlContext /*const*/ PCCTL_CONTEXT, dwAddDisposition DWORD, ppStoreContext *PCCTL_CONTEXT) bool {
	ret1 := syscall6(certAddCTLContextToStore, 4,
		uintptr(hCertStore),
		uintptr(unsafe.Pointer(pCtlContext)),
		uintptr(dwAddDisposition),
		uintptr(unsafe.Pointer(ppStoreContext)),
		0,
		0)
	return ret1 != 0
}

func CertAddCTLLinkToStore(hCertStore HCERTSTORE, pCtlContext /*const*/ PCCTL_CONTEXT, dwAddDisposition DWORD, ppStoreContext *PCCTL_CONTEXT) bool {
	ret1 := syscall6(certAddCTLLinkToStore, 4,
		uintptr(hCertStore),
		uintptr(unsafe.Pointer(pCtlContext)),
		uintptr(dwAddDisposition),
		uintptr(unsafe.Pointer(ppStoreContext)),
		0,
		0)
	return ret1 != 0
}

func CertAddCertificateContextToStore(hCertStore HCERTSTORE, pCertContext /*const*/ PCCERT_CONTEXT, dwAddDisposition DWORD, ppStoreContext *PCCERT_CONTEXT) bool {
	ret1 := syscall6(certAddCertificateContextToStore, 4,
		uintptr(hCertStore),
		uintptr(unsafe.Pointer(pCertContext)),
		uintptr(dwAddDisposition),
		uintptr(unsafe.Pointer(ppStoreContext)),
		0,
		0)
	return ret1 != 0
}

func CertAddCertificateLinkToStore(hCertStore HCERTSTORE, pCertContext /*const*/ PCCERT_CONTEXT, dwAddDisposition DWORD, ppStoreContext *PCCERT_CONTEXT) bool {
	ret1 := syscall6(certAddCertificateLinkToStore, 4,
		uintptr(hCertStore),
		uintptr(unsafe.Pointer(pCertContext)),
		uintptr(dwAddDisposition),
		uintptr(unsafe.Pointer(ppStoreContext)),
		0,
		0)
	return ret1 != 0
}

func CertAddEncodedCRLToStore(hCertStore HCERTSTORE, dwCertEncodingType DWORD, pbCrlEncoded /*const*/ *byte, cbCrlEncoded DWORD, dwAddDisposition DWORD, ppCrlContext *PCCRL_CONTEXT) bool {
	ret1 := syscall6(certAddEncodedCRLToStore, 6,
		uintptr(hCertStore),
		uintptr(dwCertEncodingType),
		uintptr(unsafe.Pointer(pbCrlEncoded)),
		uintptr(cbCrlEncoded),
		uintptr(dwAddDisposition),
		uintptr(unsafe.Pointer(ppCrlContext)))
	return ret1 != 0
}

func CertAddEncodedCTLToStore(hCertStore HCERTSTORE, dwMsgAndCertEncodingType DWORD, pbCtlEncoded /*const*/ *byte, cbCtlEncoded DWORD, dwAddDisposition DWORD, ppCtlContext *PCCTL_CONTEXT) bool {
	ret1 := syscall6(certAddEncodedCTLToStore, 6,
		uintptr(hCertStore),
		uintptr(dwMsgAndCertEncodingType),
		uintptr(unsafe.Pointer(pbCtlEncoded)),
		uintptr(cbCtlEncoded),
		uintptr(dwAddDisposition),
		uintptr(unsafe.Pointer(ppCtlContext)))
	return ret1 != 0
}

func CertAddEncodedCertificateToStore(hCertStore HCERTSTORE, dwCertEncodingType DWORD, pbCertEncoded /*const*/ *byte, cbCertEncoded DWORD, dwAddDisposition DWORD, ppCertContext *PCCERT_CONTEXT) bool {
	ret1 := syscall6(certAddEncodedCertificateToStore, 6,
		uintptr(hCertStore),
		uintptr(dwCertEncodingType),
		uintptr(unsafe.Pointer(pbCertEncoded)),
		uintptr(cbCertEncoded),
		uintptr(dwAddDisposition),
		uintptr(unsafe.Pointer(ppCertContext)))
	return ret1 != 0
}

func CertAddEncodedCertificateToSystemStore(szCertStoreName string, pbCertEncoded /*const*/ *byte, cbCertEncoded DWORD) bool {
	szCertStoreNameStr := unicode16FromString(szCertStoreName)
	ret1 := syscall3(certAddEncodedCertificateToSystemStore, 3,
		uintptr(unsafe.Pointer(&szCertStoreNameStr[0])),
		uintptr(unsafe.Pointer(pbCertEncoded)),
		uintptr(cbCertEncoded))
	return ret1 != 0
}

func CertAddEnhancedKeyUsageIdentifier(pCertContext /*const*/ PCCERT_CONTEXT, pszUsageIdentifier /*const*/ LPCSTR) bool {
	ret1 := syscall3(certAddEnhancedKeyUsageIdentifier, 2,
		uintptr(unsafe.Pointer(pCertContext)),
		uintptr(unsafe.Pointer(pszUsageIdentifier)),
		0)
	return ret1 != 0
}

func CertAddSerializedElementToStore(hCertStore HCERTSTORE, pbElement /*const*/ *byte, cbElement DWORD, dwAddDisposition DWORD, dwFlags DWORD, dwContextTypeFlags DWORD, pdwContextType *uint32, ppvContext /*const*/ uintptr) bool {
	ret1 := syscall9(certAddSerializedElementToStore, 8,
		uintptr(hCertStore),
		uintptr(unsafe.Pointer(pbElement)),
		uintptr(cbElement),
		uintptr(dwAddDisposition),
		uintptr(dwFlags),
		uintptr(dwContextTypeFlags),
		uintptr(unsafe.Pointer(pdwContextType)),
		ppvContext,
		0)
	return ret1 != 0
}

func CertAddStoreToCollection(hCollectionStore HCERTSTORE, hSiblingStore HCERTSTORE, dwUpdateFlags DWORD, dwPriority DWORD) bool {
	ret1 := syscall6(certAddStoreToCollection, 4,
		uintptr(hCollectionStore),
		uintptr(hSiblingStore),
		uintptr(dwUpdateFlags),
		uintptr(dwPriority),
		0,
		0)
	return ret1 != 0
}

func CertAlgIdToOID(dwAlgId DWORD) LPCSTR {
	ret1 := syscall3(certAlgIdToOID, 1,
		uintptr(dwAlgId),
		0,
		0)
	return (LPCSTR)(unsafe.Pointer(ret1))
}

func CertCloseStore(hCertStore HCERTSTORE, dwFlags DWORD) bool {
	ret1 := syscall3(certCloseStore, 2,
		uintptr(hCertStore),
		uintptr(dwFlags),
		0)
	return ret1 != 0
}

func CertCompareCertificate(dwCertEncodingType DWORD, pCertId1 PCERT_INFO, pCertId2 PCERT_INFO) bool {
	ret1 := syscall3(certCompareCertificate, 3,
		uintptr(dwCertEncodingType),
		uintptr(unsafe.Pointer(pCertId1)),
		uintptr(unsafe.Pointer(pCertId2)))
	return ret1 != 0
}

func CertCompareCertificateName(dwCertEncodingType DWORD, pCertName1 PCERT_NAME_BLOB, pCertName2 PCERT_NAME_BLOB) bool {
	ret1 := syscall3(certCompareCertificateName, 3,
		uintptr(dwCertEncodingType),
		uintptr(unsafe.Pointer(pCertName1)),
		uintptr(unsafe.Pointer(pCertName2)))
	return ret1 != 0
}

func CertCompareIntegerBlob(pInt1 PCRYPT_INTEGER_BLOB, pInt2 PCRYPT_INTEGER_BLOB) bool {
	ret1 := syscall3(certCompareIntegerBlob, 2,
		uintptr(unsafe.Pointer(pInt1)),
		uintptr(unsafe.Pointer(pInt2)),
		0)
	return ret1 != 0
}

// TODO: Unknown type(s): PCERT_PUBLIC_KEY_INFO
// func CertComparePublicKeyInfo(dwCertEncodingType DWORD, pPublicKey1 PCERT_PUBLIC_KEY_INFO, pPublicKey2 PCERT_PUBLIC_KEY_INFO) bool

func CertControlStore(hCertStore HCERTSTORE, dwFlags DWORD, dwCtrlType DWORD, pvCtrlPara /*const*/ uintptr) bool {
	ret1 := syscall6(certControlStore, 4,
		uintptr(hCertStore),
		uintptr(dwFlags),
		uintptr(dwCtrlType),
		pvCtrlPara,
		0,
		0)
	return ret1 != 0
}

func CertCreateCRLContext(dwCertEncodingType DWORD, pbCrlEncoded /*const*/ *byte, cbCrlEncoded DWORD) PCCRL_CONTEXT {
	ret1 := syscall3(certCreateCRLContext, 3,
		uintptr(dwCertEncodingType),
		uintptr(unsafe.Pointer(pbCrlEncoded)),
		uintptr(cbCrlEncoded))
	return (PCCRL_CONTEXT)(unsafe.Pointer(ret1))
}

func CertCreateCTLContext(dwMsgAndCertEncodingType DWORD, pbCtlEncoded /*const*/ *byte, cbCtlEncoded DWORD) PCCTL_CONTEXT {
	ret1 := syscall3(certCreateCTLContext, 3,
		uintptr(dwMsgAndCertEncodingType),
		uintptr(unsafe.Pointer(pbCtlEncoded)),
		uintptr(cbCtlEncoded))
	return (PCCTL_CONTEXT)(unsafe.Pointer(ret1))
}

func CertCreateCTLEntryFromCertificateContextProperties(pCertContext /*const*/ PCCERT_CONTEXT, cOptAttr DWORD, rgOptAttr PCRYPT_ATTRIBUTE, dwFlags DWORD, pvReserved uintptr, pCtlEntry PCTL_ENTRY, pcbCtlEntry *uint32) bool {
	ret1 := syscall9(certCreateCTLEntryFromCertificateContextProperties, 7,
		uintptr(unsafe.Pointer(pCertContext)),
		uintptr(cOptAttr),
		uintptr(unsafe.Pointer(rgOptAttr)),
		uintptr(dwFlags),
		pvReserved,
		uintptr(unsafe.Pointer(pCtlEntry)),
		uintptr(unsafe.Pointer(pcbCtlEntry)),
		0,
		0)
	return ret1 != 0
}

// TODO: Unknown type(s): HCERTCHAINENGINE *, PCERT_CHAIN_ENGINE_CONFIG
// func CertCreateCertificateChainEngine(pConfig PCERT_CHAIN_ENGINE_CONFIG, phChainEngine HCERTCHAINENGINE *) bool

func CertCreateCertificateContext(dwCertEncodingType DWORD, pbCertEncoded /*const*/ *byte, cbCertEncoded DWORD) PCCERT_CONTEXT {
	ret1 := syscall3(certCreateCertificateContext, 3,
		uintptr(dwCertEncodingType),
		uintptr(unsafe.Pointer(pbCertEncoded)),
		uintptr(cbCertEncoded))
	return (PCCERT_CONTEXT)(unsafe.Pointer(ret1))
}

// TODO: Unknown type(s): HCRYPTPROV_OR_NCRYPT_KEY_HANDLE, PCERT_EXTENSIONS, PCRYPT_ALGORITHM_IDENTIFIER, PCRYPT_KEY_PROV_INFO, PSYSTEMTIME
// func CertCreateSelfSignCertificate(hCryptProvOrNCryptKey HCRYPTPROV_OR_NCRYPT_KEY_HANDLE, pSubjectIssuerBlob PCERT_NAME_BLOB, dwFlags DWORD, pKeyProvInfo PCRYPT_KEY_PROV_INFO, pSignatureAlgorithm PCRYPT_ALGORITHM_IDENTIFIER, pStartTime PSYSTEMTIME, pEndTime PSYSTEMTIME, pExtensions PCERT_EXTENSIONS) PCCERT_CONTEXT

func CertDeleteCRLFromStore(pCrlContext PCCRL_CONTEXT) bool {
	ret1 := syscall3(certDeleteCRLFromStore, 1,
		uintptr(unsafe.Pointer(pCrlContext)),
		0,
		0)
	return ret1 != 0
}

func CertDeleteCTLFromStore(pCtlContext /*const*/ PCCTL_CONTEXT) bool {
	ret1 := syscall3(certDeleteCTLFromStore, 1,
		uintptr(unsafe.Pointer(pCtlContext)),
		0,
		0)
	return ret1 != 0
}

func CertDeleteCertificateFromStore(pCertContext /*const*/ PCCERT_CONTEXT) bool {
	ret1 := syscall3(certDeleteCertificateFromStore, 1,
		uintptr(unsafe.Pointer(pCertContext)),
		0,
		0)
	return ret1 != 0
}

func CertDuplicateCRLContext(pCrlContext PCCRL_CONTEXT) PCCRL_CONTEXT {
	ret1 := syscall3(certDuplicateCRLContext, 1,
		uintptr(unsafe.Pointer(pCrlContext)),
		0,
		0)
	return (PCCRL_CONTEXT)(unsafe.Pointer(ret1))
}

func CertDuplicateCTLContext(pCtlContext /*const*/ PCCTL_CONTEXT) PCCTL_CONTEXT {
	ret1 := syscall3(certDuplicateCTLContext, 1,
		uintptr(unsafe.Pointer(pCtlContext)),
		0,
		0)
	return (PCCTL_CONTEXT)(unsafe.Pointer(ret1))
}

// TODO: Unknown type(s): PCCERT_CHAIN_CONTEXT
// func CertDuplicateCertificateChain(pChainContext PCCERT_CHAIN_CONTEXT) PCCERT_CHAIN_CONTEXT

func CertDuplicateCertificateContext(pCertContext /*const*/ PCCERT_CONTEXT) PCCERT_CONTEXT {
	ret1 := syscall3(certDuplicateCertificateContext, 1,
		uintptr(unsafe.Pointer(pCertContext)),
		0,
		0)
	return (PCCERT_CONTEXT)(unsafe.Pointer(ret1))
}

func CertDuplicateStore(hCertStore HCERTSTORE) HCERTSTORE {
	ret1 := syscall3(certDuplicateStore, 1,
		uintptr(hCertStore),
		0,
		0)
	return HCERTSTORE(ret1)
}

func CertEnumCRLContextProperties(pCrlContext PCCRL_CONTEXT, dwPropId DWORD) DWORD {
	ret1 := syscall3(certEnumCRLContextProperties, 2,
		uintptr(unsafe.Pointer(pCrlContext)),
		uintptr(dwPropId),
		0)
	return DWORD(ret1)
}

func CertEnumCRLsInStore(hCertStore HCERTSTORE, pPrevCrlContext PCCRL_CONTEXT) PCCRL_CONTEXT {
	ret1 := syscall3(certEnumCRLsInStore, 2,
		uintptr(hCertStore),
		uintptr(unsafe.Pointer(pPrevCrlContext)),
		0)
	return (PCCRL_CONTEXT)(unsafe.Pointer(ret1))
}

func CertEnumCTLContextProperties(pCtlContext /*const*/ PCCTL_CONTEXT, dwPropId DWORD) DWORD {
	ret1 := syscall3(certEnumCTLContextProperties, 2,
		uintptr(unsafe.Pointer(pCtlContext)),
		uintptr(dwPropId),
		0)
	return DWORD(ret1)
}

func CertEnumCTLsInStore(hCertStore HCERTSTORE, pPrevCtlContext /*const*/ PCCTL_CONTEXT) PCCTL_CONTEXT {
	ret1 := syscall3(certEnumCTLsInStore, 2,
		uintptr(hCertStore),
		uintptr(unsafe.Pointer(pPrevCtlContext)),
		0)
	return (PCCTL_CONTEXT)(unsafe.Pointer(ret1))
}

func CertEnumCertificateContextProperties(pCertContext /*const*/ PCCERT_CONTEXT, dwPropId DWORD) DWORD {
	ret1 := syscall3(certEnumCertificateContextProperties, 2,
		uintptr(unsafe.Pointer(pCertContext)),
		uintptr(dwPropId),
		0)
	return DWORD(ret1)
}

func CertEnumCertificatesInStore(hCertStore HCERTSTORE, pPrevCertContext /*const*/ PCCERT_CONTEXT) PCCERT_CONTEXT {
	ret1 := syscall3(certEnumCertificatesInStore, 2,
		uintptr(hCertStore),
		uintptr(unsafe.Pointer(pPrevCertContext)),
		0)
	return (PCCERT_CONTEXT)(unsafe.Pointer(ret1))
}

// TODO: Unknown type(s): PFN_CERT_ENUM_PHYSICAL_STORE
// func CertEnumPhysicalStore(pvSystemStore /*const*/ uintptr, dwFlags DWORD, pvArg uintptr, pfnEnum PFN_CERT_ENUM_PHYSICAL_STORE) bool

func CertEnumSubjectInSortedCTL(pCtlContext /*const*/ PCCTL_CONTEXT, ppvNextSubject uintptr, pSubjectIdentifier PCRYPT_DER_BLOB, pEncodedAttributes PCRYPT_DER_BLOB) bool {
	ret1 := syscall6(certEnumSubjectInSortedCTL, 4,
		uintptr(unsafe.Pointer(pCtlContext)),
		ppvNextSubject,
		uintptr(unsafe.Pointer(pSubjectIdentifier)),
		uintptr(unsafe.Pointer(pEncodedAttributes)),
		0,
		0)
	return ret1 != 0
}

// TODO: Unknown type(s): PFN_CERT_ENUM_SYSTEM_STORE
// func CertEnumSystemStore(dwFlags DWORD, pvSystemStoreLocationPara uintptr, pvArg uintptr, pfnEnum PFN_CERT_ENUM_SYSTEM_STORE) bool

// TODO: Unknown type(s): PFN_CERT_ENUM_SYSTEM_STORE_LOCATION
// func CertEnumSystemStoreLocation(dwFlags DWORD, pvArg uintptr, pfnEnum PFN_CERT_ENUM_SYSTEM_STORE_LOCATION) bool

func CertFindAttribute(pszObjId /*const*/ LPCSTR, cAttr DWORD, rgAttr *CRYPT_ATTRIBUTE) PCRYPT_ATTRIBUTE {
	ret1 := syscall3(certFindAttribute, 3,
		uintptr(unsafe.Pointer(pszObjId)),
		uintptr(cAttr),
		uintptr(unsafe.Pointer(rgAttr)))
	return (PCRYPT_ATTRIBUTE)(unsafe.Pointer(ret1))
}

func CertFindCRLInStore(hCertStore HCERTSTORE, dwCertEncodingType DWORD, dwFindFlags DWORD, dwFindType DWORD, pvFindPara /*const*/ uintptr, pPrevCrlContext PCCRL_CONTEXT) PCCRL_CONTEXT {
	ret1 := syscall6(certFindCRLInStore, 6,
		uintptr(hCertStore),
		uintptr(dwCertEncodingType),
		uintptr(dwFindFlags),
		uintptr(dwFindType),
		pvFindPara,
		uintptr(unsafe.Pointer(pPrevCrlContext)))
	return (PCCRL_CONTEXT)(unsafe.Pointer(ret1))
}

func CertFindCTLInStore(hCertStore HCERTSTORE, dwMsgAndCertEncodingType DWORD, dwFindFlags DWORD, dwFindType DWORD, pvFindPara /*const*/ uintptr, pPrevCtlContext /*const*/ PCCTL_CONTEXT) PCCTL_CONTEXT {
	ret1 := syscall6(certFindCTLInStore, 6,
		uintptr(hCertStore),
		uintptr(dwMsgAndCertEncodingType),
		uintptr(dwFindFlags),
		uintptr(dwFindType),
		pvFindPara,
		uintptr(unsafe.Pointer(pPrevCtlContext)))
	return (PCCTL_CONTEXT)(unsafe.Pointer(ret1))
}

func CertFindCertificateInCRL(pCert /*const*/ PCCERT_CONTEXT, pCrlContext PCCRL_CONTEXT, dwFlags DWORD, pvReserved uintptr, ppCrlEntry *PCRL_ENTRY) bool {
	ret1 := syscall6(certFindCertificateInCRL, 5,
		uintptr(unsafe.Pointer(pCert)),
		uintptr(unsafe.Pointer(pCrlContext)),
		uintptr(dwFlags),
		pvReserved,
		uintptr(unsafe.Pointer(ppCrlEntry)),
		0)
	return ret1 != 0
}

func CertFindCertificateInStore(hCertStore HCERTSTORE, dwCertEncodingType DWORD, dwFindFlags DWORD, dwFindType DWORD, pvFindPara /*const*/ uintptr, pPrevCertContext /*const*/ PCCERT_CONTEXT) PCCERT_CONTEXT {
	ret1 := syscall6(certFindCertificateInStore, 6,
		uintptr(hCertStore),
		uintptr(dwCertEncodingType),
		uintptr(dwFindFlags),
		uintptr(dwFindType),
		pvFindPara,
		uintptr(unsafe.Pointer(pPrevCertContext)))
	return (PCCERT_CONTEXT)(unsafe.Pointer(ret1))
}

// TODO: Unknown type(s): PCCERT_CHAIN_CONTEXT
// func CertFindChainInStore(hCertStore HCERTSTORE, dwCertEncodingType DWORD, dwFindFlags DWORD, dwFindType DWORD, pvFindPara /*const*/ uintptr, pPrevChainContext PCCERT_CHAIN_CONTEXT) PCCERT_CHAIN_CONTEXT

func CertFindExtension(pszObjId /*const*/ LPCSTR, cExtensions DWORD, rgExtensions *CERT_EXTENSION) PCERT_EXTENSION {
	ret1 := syscall3(certFindExtension, 3,
		uintptr(unsafe.Pointer(pszObjId)),
		uintptr(cExtensions),
		uintptr(unsafe.Pointer(rgExtensions)))
	return (PCERT_EXTENSION)(unsafe.Pointer(ret1))
}

// TODO: Unknown type(s): PCERT_NAME_INFO, PCERT_RDN_ATTR
// func CertFindRDNAttr(pszObjId /*const*/ LPCSTR, pName PCERT_NAME_INFO) PCERT_RDN_ATTR

func CertFindSubjectInCTL(dwEncodingType DWORD, dwSubjectType DWORD, pvSubject uintptr, pCtlContext /*const*/ PCCTL_CONTEXT, dwFlags DWORD) PCTL_ENTRY {
	ret1 := syscall6(certFindSubjectInCTL, 5,
		uintptr(dwEncodingType),
		uintptr(dwSubjectType),
		pvSubject,
		uintptr(unsafe.Pointer(pCtlContext)),
		uintptr(dwFlags),
		0)
	return (PCTL_ENTRY)(unsafe.Pointer(ret1))
}

func CertFindSubjectInSortedCTL(pSubjectIdentifier PCRYPT_DATA_BLOB, pCtlContext /*const*/ PCCTL_CONTEXT, dwFlags DWORD, pvReserved uintptr, pEncodedAttributes PCRYPT_DER_BLOB) bool {
	ret1 := syscall6(certFindSubjectInSortedCTL, 5,
		uintptr(unsafe.Pointer(pSubjectIdentifier)),
		uintptr(unsafe.Pointer(pCtlContext)),
		uintptr(dwFlags),
		pvReserved,
		uintptr(unsafe.Pointer(pEncodedAttributes)),
		0)
	return ret1 != 0
}

func CertFreeCRLContext(pCrlContext PCCRL_CONTEXT) bool {
	ret1 := syscall3(certFreeCRLContext, 1,
		uintptr(unsafe.Pointer(pCrlContext)),
		0,
		0)
	return ret1 != 0
}

func CertFreeCTLContext(pCtlContext /*const*/ PCCTL_CONTEXT) bool {
	ret1 := syscall3(certFreeCTLContext, 1,
		uintptr(unsafe.Pointer(pCtlContext)),
		0,
		0)
	return ret1 != 0
}

// TODO: Unknown type(s): PCCERT_CHAIN_CONTEXT
// func CertFreeCertificateChain(pChainContext PCCERT_CHAIN_CONTEXT)

// TODO: Unknown type(s): HCERTCHAINENGINE
// func CertFreeCertificateChainEngine(hChainEngine HCERTCHAINENGINE)

func CertFreeCertificateContext(pCertContext /*const*/ PCCERT_CONTEXT) bool {
	ret1 := syscall3(certFreeCertificateContext, 1,
		uintptr(unsafe.Pointer(pCertContext)),
		0,
		0)
	return ret1 != 0
}

func CertGetCRLContextProperty(pCrlContext PCCRL_CONTEXT, dwPropId DWORD, pvData uintptr, pcbData *uint32) bool {
	ret1 := syscall6(certGetCRLContextProperty, 4,
		uintptr(unsafe.Pointer(pCrlContext)),
		uintptr(dwPropId),
		pvData,
		uintptr(unsafe.Pointer(pcbData)),
		0,
		0)
	return ret1 != 0
}

func CertGetCRLFromStore(hCertStore HCERTSTORE, pIssuerContext /*const*/ PCCERT_CONTEXT, pPrevCrlContext PCCRL_CONTEXT, pdwFlags *uint32) PCCRL_CONTEXT {
	ret1 := syscall6(certGetCRLFromStore, 4,
		uintptr(hCertStore),
		uintptr(unsafe.Pointer(pIssuerContext)),
		uintptr(unsafe.Pointer(pPrevCrlContext)),
		uintptr(unsafe.Pointer(pdwFlags)),
		0,
		0)
	return (PCCRL_CONTEXT)(unsafe.Pointer(ret1))
}

func CertGetCTLContextProperty(pCtlContext /*const*/ PCCTL_CONTEXT, dwPropId DWORD, pvData uintptr, pcbData *uint32) bool {
	ret1 := syscall6(certGetCTLContextProperty, 4,
		uintptr(unsafe.Pointer(pCtlContext)),
		uintptr(dwPropId),
		pvData,
		uintptr(unsafe.Pointer(pcbData)),
		0,
		0)
	return ret1 != 0
}

// TODO: Unknown type(s): HCERTCHAINENGINE, PCCERT_CHAIN_CONTEXT *, PCERT_CHAIN_PARA
// func CertGetCertificateChain(hChainEngine HCERTCHAINENGINE, pCertContext /*const*/ PCCERT_CONTEXT, pTime *FILETIME, hAdditionalStore HCERTSTORE, pChainPara PCERT_CHAIN_PARA, dwFlags DWORD, pvReserved LPVOID, ppChainContext PCCERT_CHAIN_CONTEXT *) bool

func CertGetCertificateContextProperty(pCertContext /*const*/ PCCERT_CONTEXT, dwPropId DWORD, pvData uintptr, pcbData *uint32) bool {
	ret1 := syscall6(certGetCertificateContextProperty, 4,
		uintptr(unsafe.Pointer(pCertContext)),
		uintptr(dwPropId),
		pvData,
		uintptr(unsafe.Pointer(pcbData)),
		0,
		0)
	return ret1 != 0
}

// TODO: Unknown type(s): PCERT_ENHKEY_USAGE
// func CertGetEnhancedKeyUsage(pCertContext /*const*/ PCCERT_CONTEXT, dwFlags DWORD, pUsage PCERT_ENHKEY_USAGE, pcbUsage *uint32) bool

func CertGetIntendedKeyUsage(dwCertEncodingType DWORD, pCertInfo PCERT_INFO, pbKeyUsage *byte, cbKeyUsage DWORD) bool {
	ret1 := syscall6(certGetIntendedKeyUsage, 4,
		uintptr(dwCertEncodingType),
		uintptr(unsafe.Pointer(pCertInfo)),
		uintptr(unsafe.Pointer(pbKeyUsage)),
		uintptr(cbKeyUsage),
		0,
		0)
	return ret1 != 0
}

func CertGetIssuerCertificateFromStore(hCertStore HCERTSTORE, pSubjectContext /*const*/ PCCERT_CONTEXT, pPrevIssuerContext /*const*/ PCCERT_CONTEXT, pdwFlags *uint32) PCCERT_CONTEXT {
	ret1 := syscall6(certGetIssuerCertificateFromStore, 4,
		uintptr(hCertStore),
		uintptr(unsafe.Pointer(pSubjectContext)),
		uintptr(unsafe.Pointer(pPrevIssuerContext)),
		uintptr(unsafe.Pointer(pdwFlags)),
		0,
		0)
	return (PCCERT_CONTEXT)(unsafe.Pointer(ret1))
}

func CertGetNameString(pCertContext /*const*/ PCCERT_CONTEXT, dwType DWORD, dwFlags DWORD, pvTypePara uintptr, pszNameString LPWSTR, cchNameString DWORD) DWORD {
	ret1 := syscall6(certGetNameString, 6,
		uintptr(unsafe.Pointer(pCertContext)),
		uintptr(dwType),
		uintptr(dwFlags),
		pvTypePara,
		uintptr(unsafe.Pointer(pszNameString)),
		uintptr(cchNameString))
	return DWORD(ret1)
}

// TODO: Unknown type(s): PCERT_PUBLIC_KEY_INFO
// func CertGetPublicKeyLength(dwCertEncodingType DWORD, pPublicKey PCERT_PUBLIC_KEY_INFO) DWORD

func CertGetStoreProperty(hCertStore HCERTSTORE, dwPropId DWORD, pvData uintptr, pcbData *uint32) bool {
	ret1 := syscall6(certGetStoreProperty, 4,
		uintptr(hCertStore),
		uintptr(dwPropId),
		pvData,
		uintptr(unsafe.Pointer(pcbData)),
		0,
		0)
	return ret1 != 0
}

func CertGetSubjectCertificateFromStore(hCertStore HCERTSTORE, dwCertEncodingType DWORD, pCertId PCERT_INFO) PCCERT_CONTEXT {
	ret1 := syscall3(certGetSubjectCertificateFromStore, 3,
		uintptr(hCertStore),
		uintptr(dwCertEncodingType),
		uintptr(unsafe.Pointer(pCertId)))
	return (PCCERT_CONTEXT)(unsafe.Pointer(ret1))
}

func CertGetValidUsages(cCerts DWORD, rghCerts *PCCERT_CONTEXT, cNumOIDs *int, rghOIDs *LPSTR, pcbOIDs *uint32) bool {
	ret1 := syscall6(certGetValidUsages, 5,
		uintptr(cCerts),
		uintptr(unsafe.Pointer(rghCerts)),
		uintptr(unsafe.Pointer(cNumOIDs)),
		uintptr(unsafe.Pointer(rghOIDs)),
		uintptr(unsafe.Pointer(pcbOIDs)),
		0)
	return ret1 != 0
}

// TODO: Unknown type(s): PCERT_RDN
// func CertIsRDNAttrsInCertificateName(dwCertEncodingType DWORD, dwFlags DWORD, pCertName PCERT_NAME_BLOB, pRDN PCERT_RDN) bool

func CertIsValidCRLForCertificate(pCert /*const*/ PCCERT_CONTEXT, pCrl PCCRL_CONTEXT, dwFlags DWORD, pvReserved uintptr) bool {
	ret1 := syscall6(certIsValidCRLForCertificate, 4,
		uintptr(unsafe.Pointer(pCert)),
		uintptr(unsafe.Pointer(pCrl)),
		uintptr(dwFlags),
		pvReserved,
		0,
		0)
	return ret1 != 0
}

func CertNameToStr(dwCertEncodingType DWORD, pName PCERT_NAME_BLOB, dwStrType DWORD, psz LPWSTR, csz DWORD) DWORD {
	ret1 := syscall6(certNameToStr, 5,
		uintptr(dwCertEncodingType),
		uintptr(unsafe.Pointer(pName)),
		uintptr(dwStrType),
		uintptr(unsafe.Pointer(psz)),
		uintptr(csz),
		0)
	return DWORD(ret1)
}

func CertOIDToAlgId(pszObjId /*const*/ LPCSTR) DWORD {
	ret1 := syscall3(certOIDToAlgId, 1,
		uintptr(unsafe.Pointer(pszObjId)),
		0,
		0)
	return DWORD(ret1)
}

// TODO: Unknown type(s): HCRYPTPROV_LEGACY
// func CertOpenStore(lpszStoreProvider /*const*/ LPCSTR, dwEncodingType DWORD, hCryptProv HCRYPTPROV_LEGACY, dwFlags DWORD, pvPara /*const*/ uintptr) HCERTSTORE

// TODO: Unknown type(s): HCRYPTPROV_LEGACY
// func CertOpenSystemStore(hProv HCRYPTPROV_LEGACY, szSubsystemProtocol string) HCERTSTORE

func CertRDNValueToStr(dwValueType DWORD, pValue PCERT_RDN_VALUE_BLOB, psz LPWSTR, csz DWORD) DWORD {
	ret1 := syscall6(certRDNValueToStr, 4,
		uintptr(dwValueType),
		uintptr(unsafe.Pointer(pValue)),
		uintptr(unsafe.Pointer(psz)),
		uintptr(csz),
		0,
		0)
	return DWORD(ret1)
}

// TODO: Unknown type(s): PCERT_PHYSICAL_STORE_INFO
// func CertRegisterPhysicalStore(pvSystemStore /*const*/ uintptr, dwFlags DWORD, pwszStoreName string, pStoreInfo PCERT_PHYSICAL_STORE_INFO, pvReserved uintptr) bool

// TODO: Unknown type(s): PCERT_SYSTEM_STORE_INFO
// func CertRegisterSystemStore(pvSystemStore /*const*/ uintptr, dwFlags DWORD, pStoreInfo PCERT_SYSTEM_STORE_INFO, pvReserved uintptr) bool

func CertRemoveEnhancedKeyUsageIdentifier(pCertContext /*const*/ PCCERT_CONTEXT, pszUsageIdentifier /*const*/ LPCSTR) bool {
	ret1 := syscall3(certRemoveEnhancedKeyUsageIdentifier, 2,
		uintptr(unsafe.Pointer(pCertContext)),
		uintptr(unsafe.Pointer(pszUsageIdentifier)),
		0)
	return ret1 != 0
}

func CertRemoveStoreFromCollection(hCollectionStore HCERTSTORE, hSiblingStore HCERTSTORE) {
	syscall3(certRemoveStoreFromCollection, 2,
		uintptr(hCollectionStore),
		uintptr(hSiblingStore),
		0)
}

// TODO: Unknown type(s): HCERTCHAINENGINE
// func CertResyncCertificateChainEngine(hChainEngine HCERTCHAINENGINE) bool

func CertSaveStore(hCertStore HCERTSTORE, dwEncodingType DWORD, dwSaveAs DWORD, dwSaveTo DWORD, pvSaveToPara uintptr, dwFlags DWORD) bool {
	ret1 := syscall6(certSaveStore, 6,
		uintptr(hCertStore),
		uintptr(dwEncodingType),
		uintptr(dwSaveAs),
		uintptr(dwSaveTo),
		pvSaveToPara,
		uintptr(dwFlags))
	return ret1 != 0
}

func CertSerializeCRLStoreElement(pCrlContext PCCRL_CONTEXT, dwFlags DWORD, pbElement *byte, pcbElement *uint32) bool {
	ret1 := syscall6(certSerializeCRLStoreElement, 4,
		uintptr(unsafe.Pointer(pCrlContext)),
		uintptr(dwFlags),
		uintptr(unsafe.Pointer(pbElement)),
		uintptr(unsafe.Pointer(pcbElement)),
		0,
		0)
	return ret1 != 0
}

func CertSerializeCTLStoreElement(pCtlContext /*const*/ PCCTL_CONTEXT, dwFlags DWORD, pbElement *byte, pcbElement *uint32) bool {
	ret1 := syscall6(certSerializeCTLStoreElement, 4,
		uintptr(unsafe.Pointer(pCtlContext)),
		uintptr(dwFlags),
		uintptr(unsafe.Pointer(pbElement)),
		uintptr(unsafe.Pointer(pcbElement)),
		0,
		0)
	return ret1 != 0
}

func CertSerializeCertificateStoreElement(pCertContext /*const*/ PCCERT_CONTEXT, dwFlags DWORD, pbElement *byte, pcbElement *uint32) bool {
	ret1 := syscall6(certSerializeCertificateStoreElement, 4,
		uintptr(unsafe.Pointer(pCertContext)),
		uintptr(dwFlags),
		uintptr(unsafe.Pointer(pbElement)),
		uintptr(unsafe.Pointer(pcbElement)),
		0,
		0)
	return ret1 != 0
}

func CertSetCRLContextProperty(pCrlContext PCCRL_CONTEXT, dwPropId DWORD, dwFlags DWORD, pvData /*const*/ uintptr) bool {
	ret1 := syscall6(certSetCRLContextProperty, 4,
		uintptr(unsafe.Pointer(pCrlContext)),
		uintptr(dwPropId),
		uintptr(dwFlags),
		pvData,
		0,
		0)
	return ret1 != 0
}

func CertSetCTLContextProperty(pCtlContext /*const*/ PCCTL_CONTEXT, dwPropId DWORD, dwFlags DWORD, pvData /*const*/ uintptr) bool {
	ret1 := syscall6(certSetCTLContextProperty, 4,
		uintptr(unsafe.Pointer(pCtlContext)),
		uintptr(dwPropId),
		uintptr(dwFlags),
		pvData,
		0,
		0)
	return ret1 != 0
}

func CertSetCertificateContextPropertiesFromCTLEntry(pCertContext /*const*/ PCCERT_CONTEXT, pCtlEntry PCTL_ENTRY, dwFlags DWORD) bool {
	ret1 := syscall3(certSetCertificateContextPropertiesFromCTLEntry, 3,
		uintptr(unsafe.Pointer(pCertContext)),
		uintptr(unsafe.Pointer(pCtlEntry)),
		uintptr(dwFlags))
	return ret1 != 0
}

func CertSetCertificateContextProperty(pCertContext /*const*/ PCCERT_CONTEXT, dwPropId DWORD, dwFlags DWORD, pvData /*const*/ uintptr) bool {
	ret1 := syscall6(certSetCertificateContextProperty, 4,
		uintptr(unsafe.Pointer(pCertContext)),
		uintptr(dwPropId),
		uintptr(dwFlags),
		pvData,
		0,
		0)
	return ret1 != 0
}

// TODO: Unknown type(s): PCERT_ENHKEY_USAGE
// func CertSetEnhancedKeyUsage(pCertContext /*const*/ PCCERT_CONTEXT, pUsage PCERT_ENHKEY_USAGE) bool

func CertSetStoreProperty(hCertStore HCERTSTORE, dwPropId DWORD, dwFlags DWORD, pvData /*const*/ uintptr) bool {
	ret1 := syscall6(certSetStoreProperty, 4,
		uintptr(hCertStore),
		uintptr(dwPropId),
		uintptr(dwFlags),
		pvData,
		0,
		0)
	return ret1 != 0
}

func CertStrToName(dwCertEncodingType DWORD, pszX500 string, dwStrType DWORD, pvReserved uintptr, pbEncoded *byte, pcbEncoded *uint32, ppszError *LPCWSTR) bool {
	pszX500Str := unicode16FromString(pszX500)
	ret1 := syscall9(certStrToName, 7,
		uintptr(dwCertEncodingType),
		uintptr(unsafe.Pointer(&pszX500Str[0])),
		uintptr(dwStrType),
		pvReserved,
		uintptr(unsafe.Pointer(pbEncoded)),
		uintptr(unsafe.Pointer(pcbEncoded)),
		uintptr(unsafe.Pointer(ppszError)),
		0,
		0)
	return ret1 != 0
}

func CertUnregisterPhysicalStore(pvSystemStore /*const*/ uintptr, dwFlags DWORD, pwszStoreName string) bool {
	pwszStoreNameStr := unicode16FromString(pwszStoreName)
	ret1 := syscall3(certUnregisterPhysicalStore, 3,
		pvSystemStore,
		uintptr(dwFlags),
		uintptr(unsafe.Pointer(&pwszStoreNameStr[0])))
	return ret1 != 0
}

func CertUnregisterSystemStore(pvSystemStore /*const*/ uintptr, dwFlags DWORD) bool {
	ret1 := syscall3(certUnregisterSystemStore, 2,
		pvSystemStore,
		uintptr(dwFlags),
		0)
	return ret1 != 0
}

func CertVerifyCRLRevocation(dwCertEncodingType DWORD, pCertId PCERT_INFO, cCrlInfo DWORD, rgpCrlInfo *PCRL_INFO) bool {
	ret1 := syscall6(certVerifyCRLRevocation, 4,
		uintptr(dwCertEncodingType),
		uintptr(unsafe.Pointer(pCertId)),
		uintptr(cCrlInfo),
		uintptr(unsafe.Pointer(rgpCrlInfo)),
		0,
		0)
	return ret1 != 0
}

func CertVerifyCRLTimeValidity(pTimeToVerify *FILETIME, pCrlInfo PCRL_INFO) LONG {
	ret1 := syscall3(certVerifyCRLTimeValidity, 2,
		uintptr(unsafe.Pointer(pTimeToVerify)),
		uintptr(unsafe.Pointer(pCrlInfo)),
		0)
	return LONG(ret1)
}

// TODO: Unknown type(s): PCTL_USAGE, PCTL_VERIFY_USAGE_PARA, PCTL_VERIFY_USAGE_STATUS
// func CertVerifyCTLUsage(dwEncodingType DWORD, dwSubjectType DWORD, pvSubject uintptr, pSubjectUsage PCTL_USAGE, dwFlags DWORD, pVerifyUsagePara PCTL_VERIFY_USAGE_PARA, pVerifyUsageStatus PCTL_VERIFY_USAGE_STATUS) bool

// TODO: Unknown type(s): PCCERT_CHAIN_CONTEXT, PCERT_CHAIN_POLICY_PARA, PCERT_CHAIN_POLICY_STATUS
// func CertVerifyCertificateChainPolicy(pszPolicyOID /*const*/ LPCSTR, pChainContext PCCERT_CHAIN_CONTEXT, pPolicyPara PCERT_CHAIN_POLICY_PARA, pPolicyStatus PCERT_CHAIN_POLICY_STATUS) bool

// TODO: Unknown type(s): PCERT_REVOCATION_PARA, PCERT_REVOCATION_STATUS
// func CertVerifyRevocation(dwEncodingType DWORD, dwRevType DWORD, cContext DWORD, rgpvContext *PVOID, dwFlags DWORD, pRevPara PCERT_REVOCATION_PARA, pRevStatus PCERT_REVOCATION_STATUS) bool

func CertVerifySubjectCertificateContext(pSubject /*const*/ PCCERT_CONTEXT, pIssuer /*const*/ PCCERT_CONTEXT, pdwFlags *uint32) bool {
	ret1 := syscall3(certVerifySubjectCertificateContext, 3,
		uintptr(unsafe.Pointer(pSubject)),
		uintptr(unsafe.Pointer(pIssuer)),
		uintptr(unsafe.Pointer(pdwFlags)))
	return ret1 != 0
}

func CertVerifyTimeValidity(pTimeToVerify *FILETIME, pCertInfo PCERT_INFO) LONG {
	ret1 := syscall3(certVerifyTimeValidity, 2,
		uintptr(unsafe.Pointer(pTimeToVerify)),
		uintptr(unsafe.Pointer(pCertInfo)),
		0)
	return LONG(ret1)
}

func CertVerifyValidityNesting(pSubjectInfo PCERT_INFO, pIssuerInfo PCERT_INFO) bool {
	ret1 := syscall3(certVerifyValidityNesting, 2,
		uintptr(unsafe.Pointer(pSubjectInfo)),
		uintptr(unsafe.Pointer(pIssuerInfo)),
		0)
	return ret1 != 0
}

// TODO: Unknown type(s): HCRYPTPROV_OR_NCRYPT_KEY_HANDLE *
// func CryptAcquireCertificatePrivateKey(pCert /*const*/ PCCERT_CONTEXT, dwFlags DWORD, pvParameters uintptr, phCryptProvOrNCryptKey HCRYPTPROV_OR_NCRYPT_KEY_HANDLE *, pdwKeySpec *uint32, pfCallerFreeProvOrNCryptKey *BOOL) bool

func CryptBinaryToString(pbBinary /*const*/ *byte, cbBinary DWORD, dwFlags DWORD, pszString LPWSTR, pcchString *uint32) bool {
	ret1 := syscall6(cryptBinaryToString, 5,
		uintptr(unsafe.Pointer(pbBinary)),
		uintptr(cbBinary),
		uintptr(dwFlags),
		uintptr(unsafe.Pointer(pszString)),
		uintptr(unsafe.Pointer(pcchString)),
		0)
	return ret1 != 0
}

// TODO: Unknown type(s): HCRYPTASYNC
// func CryptCloseAsyncHandle(hAsync HCRYPTASYNC) bool

// TODO: Unknown type(s): PHCRYPTASYNC
// func CryptCreateAsyncHandle(dwFlags DWORD, phAsync PHCRYPTASYNC) bool

// TODO: Unknown type(s): const PUBLICKEYSTRUC *
// func CryptCreateKeyIdentifierFromCSP(dwCertEncodingType DWORD, pszPubKeyOID /*const*/ LPCSTR, pPubKeyStruc /*const*/ const PUBLICKEYSTRUC *, cbPubKeyStruc DWORD, dwFlags DWORD, pvReserved uintptr, pbHash *byte, pcbHash *uint32) bool

// TODO: Unknown type(s): PCRYPT_DECRYPT_MESSAGE_PARA, PCRYPT_VERIFY_MESSAGE_PARA
// func CryptDecodeMessage(dwMsgTypeFlags DWORD, pDecryptPara PCRYPT_DECRYPT_MESSAGE_PARA, pVerifyPara PCRYPT_VERIFY_MESSAGE_PARA, dwSignerIndex DWORD, pbEncodedBlob /*const*/ *byte, cbEncodedBlob DWORD, dwPrevInnerContentType DWORD, pdwMsgType *uint32, pdwInnerContentType *uint32, pbDecoded *byte, pcbDecoded *uint32, ppXchgCert *PCCERT_CONTEXT, ppSignerCert *PCCERT_CONTEXT) bool

func CryptDecodeObject(dwCertEncodingType DWORD, lpszStructType /*const*/ LPCSTR, pbEncoded /*const*/ *byte, cbEncoded DWORD, dwFlags DWORD, pvStructInfo uintptr, pcbStructInfo *uint32) bool {
	ret1 := syscall9(cryptDecodeObject, 7,
		uintptr(dwCertEncodingType),
		uintptr(unsafe.Pointer(lpszStructType)),
		uintptr(unsafe.Pointer(pbEncoded)),
		uintptr(cbEncoded),
		uintptr(dwFlags),
		pvStructInfo,
		uintptr(unsafe.Pointer(pcbStructInfo)),
		0,
		0)
	return ret1 != 0
}

// TODO: Unknown type(s): PCRYPT_DECODE_PARA
// func CryptDecodeObjectEx(dwCertEncodingType DWORD, lpszStructType /*const*/ LPCSTR, pbEncoded /*const*/ *byte, cbEncoded DWORD, dwFlags DWORD, pDecodePara PCRYPT_DECODE_PARA, pvStructInfo uintptr, pcbStructInfo *uint32) bool

// TODO: Unknown type(s): PCRYPT_DECRYPT_MESSAGE_PARA, PCRYPT_VERIFY_MESSAGE_PARA
// func CryptDecryptAndVerifyMessageSignature(pDecryptPara PCRYPT_DECRYPT_MESSAGE_PARA, pVerifyPara PCRYPT_VERIFY_MESSAGE_PARA, dwSignerIndex DWORD, pbEncryptedBlob /*const*/ *byte, cbEncryptedBlob DWORD, pbDecrypted *byte, pcbDecrypted *uint32, ppXchgCert *PCCERT_CONTEXT, ppSignerCert *PCCERT_CONTEXT) bool

// TODO: Unknown type(s): PCRYPT_DECRYPT_MESSAGE_PARA
// func CryptDecryptMessage(pDecryptPara PCRYPT_DECRYPT_MESSAGE_PARA, pbEncryptedBlob /*const*/ *byte, cbEncryptedBlob DWORD, pbDecrypted *byte, pcbDecrypted *uint32, ppXchgCert *PCCERT_CONTEXT) bool

func CryptEncodeObject(dwCertEncodingType DWORD, lpszStructType /*const*/ LPCSTR, pvStructInfo /*const*/ uintptr, pbEncoded *byte, pcbEncoded *uint32) bool {
	ret1 := syscall6(cryptEncodeObject, 5,
		uintptr(dwCertEncodingType),
		uintptr(unsafe.Pointer(lpszStructType)),
		pvStructInfo,
		uintptr(unsafe.Pointer(pbEncoded)),
		uintptr(unsafe.Pointer(pcbEncoded)),
		0)
	return ret1 != 0
}

// TODO: Unknown type(s): PCRYPT_ENCODE_PARA
// func CryptEncodeObjectEx(dwCertEncodingType DWORD, lpszStructType /*const*/ LPCSTR, pvStructInfo /*const*/ uintptr, dwFlags DWORD, pEncodePara PCRYPT_ENCODE_PARA, pvEncoded uintptr, pcbEncoded *uint32) bool

// TODO: Unknown type(s): PCRYPT_ENCRYPT_MESSAGE_PARA
// func CryptEncryptMessage(pEncryptPara PCRYPT_ENCRYPT_MESSAGE_PARA, cRecipientCert DWORD, rgpRecipientCert *PCCERT_CONTEXT, pbToBeEncrypted /*const*/ *byte, cbToBeEncrypted DWORD, pbEncryptedBlob *byte, pcbEncryptedBlob *uint32) bool

// TODO: Unknown type(s): PFN_CRYPT_ENUM_KEYID_PROP
// func CryptEnumKeyIdentifierProperties(pKeyIdentifier /*const*/ *CRYPT_HASH_BLOB, dwPropId DWORD, dwFlags DWORD, pwszComputerName string, pvReserved uintptr, pvArg uintptr, pfnEnum PFN_CRYPT_ENUM_KEYID_PROP) bool

// TODO: Unknown type(s): PFN_CRYPT_ENUM_OID_FUNC
// func CryptEnumOIDFunction(dwEncodingType DWORD, pszFuncName /*const*/ LPCSTR, pszOID /*const*/ LPCSTR, dwFlags DWORD, pvArg uintptr, pfnEnumOIDFunc PFN_CRYPT_ENUM_OID_FUNC) bool

// TODO: Unknown type(s): PFN_CRYPT_ENUM_OID_INFO
// func CryptEnumOIDInfo(dwGroupId DWORD, dwFlags DWORD, pvArg uintptr, pfnEnumOIDInfo PFN_CRYPT_ENUM_OID_INFO) bool

func CryptExportPKCS8(hCryptProv HCRYPTPROV, dwKeySpec DWORD, pszPrivateKeyObjId LPSTR, dwFlags DWORD, pvAuxInfo uintptr, pbPrivateKeyBlob *byte, pcbPrivateKeyBlob *uint32) bool {
	ret1 := syscall9(cryptExportPKCS8, 7,
		uintptr(hCryptProv),
		uintptr(dwKeySpec),
		uintptr(unsafe.Pointer(pszPrivateKeyObjId)),
		uintptr(dwFlags),
		pvAuxInfo,
		uintptr(unsafe.Pointer(pbPrivateKeyBlob)),
		uintptr(unsafe.Pointer(pcbPrivateKeyBlob)),
		0,
		0)
	return ret1 != 0
}

// TODO: Unknown type(s): HCRYPTPROV_OR_NCRYPT_KEY_HANDLE, PCERT_PUBLIC_KEY_INFO
// func CryptExportPublicKeyInfo(hCryptProvOrNCryptKey HCRYPTPROV_OR_NCRYPT_KEY_HANDLE, dwKeySpec DWORD, dwCertEncodingType DWORD, pInfo PCERT_PUBLIC_KEY_INFO, pcbInfo *uint32) bool

// TODO: Unknown type(s): HCRYPTPROV_OR_NCRYPT_KEY_HANDLE, PCERT_PUBLIC_KEY_INFO
// func CryptExportPublicKeyInfoEx(hCryptProvOrNCryptKey HCRYPTPROV_OR_NCRYPT_KEY_HANDLE, dwKeySpec DWORD, dwCertEncodingType DWORD, pszPublicKeyObjId LPSTR, dwFlags DWORD, pvAuxInfo uintptr, pInfo PCERT_PUBLIC_KEY_INFO, pcbInfo *uint32) bool

func CryptFindCertificateKeyProvInfo(pCert /*const*/ PCCERT_CONTEXT, dwFlags DWORD, pvReserved uintptr) bool {
	ret1 := syscall3(cryptFindCertificateKeyProvInfo, 3,
		uintptr(unsafe.Pointer(pCert)),
		uintptr(dwFlags),
		pvReserved)
	return ret1 != 0
}

func CryptFindLocalizedName(pwszCryptName string) string {
	pwszCryptNameStr := unicode16FromString(pwszCryptName)
	ret1 := syscall3(cryptFindLocalizedName, 1,
		uintptr(unsafe.Pointer(&pwszCryptNameStr[0])),
		0,
		0)
	return stringFromUnicode16((*uint16)(unsafe.Pointer(ret1)))
}

// TODO: Unknown type(s): PCCRYPT_OID_INFO
// func CryptFindOIDInfo(dwKeyType DWORD, pvKey uintptr, dwGroupId DWORD) PCCRYPT_OID_INFO

func CryptFormatObject(dwCertEncodingType DWORD, dwFormatType DWORD, dwFormatStrType DWORD, pFormatStruct uintptr, lpszStructType /*const*/ LPCSTR, pbEncoded /*const*/ *byte, cbEncoded DWORD, pbFormat uintptr, pcbFormat *uint32) bool {
	ret1 := syscall9(cryptFormatObject, 9,
		uintptr(dwCertEncodingType),
		uintptr(dwFormatType),
		uintptr(dwFormatStrType),
		pFormatStruct,
		uintptr(unsafe.Pointer(lpszStructType)),
		uintptr(unsafe.Pointer(pbEncoded)),
		uintptr(cbEncoded),
		pbFormat,
		uintptr(unsafe.Pointer(pcbFormat)))
	return ret1 != 0
}

// TODO: Unknown type(s): HCRYPTOIDFUNCADDR
// func CryptFreeOIDFunctionAddress(hFuncAddr HCRYPTOIDFUNCADDR, dwFlags DWORD) bool

// TODO: Unknown type(s): HCRYPTASYNC, PFN_CRYPT_ASYNC_PARAM_FREE_FUNC *
// func CryptGetAsyncParam(hAsync HCRYPTASYNC, pszParamOid LPSTR, ppvParam *LPVOID, ppfnFree PFN_CRYPT_ASYNC_PARAM_FREE_FUNC *) bool

// TODO: Unknown type(s): HCRYPTOIDFUNCSET
// func CryptGetDefaultOIDDllList(hFuncSet HCRYPTOIDFUNCSET, dwEncodingType DWORD, pwszDllList *WCHAR, pcchDllList *uint32) bool

// TODO: Unknown type(s): HCRYPTOIDFUNCADDR *, HCRYPTOIDFUNCSET
// func CryptGetDefaultOIDFunctionAddress(hFuncSet HCRYPTOIDFUNCSET, dwEncodingType DWORD, pwszDll string, dwFlags DWORD, ppvFuncAddr uintptr, phFuncAddr HCRYPTOIDFUNCADDR *) bool

func CryptGetKeyIdentifierProperty(pKeyIdentifier /*const*/ *CRYPT_HASH_BLOB, dwPropId DWORD, dwFlags DWORD, pwszComputerName string, pvReserved uintptr, pvData uintptr, pcbData *uint32) bool {
	pwszComputerNameStr := unicode16FromString(pwszComputerName)
	ret1 := syscall9(cryptGetKeyIdentifierProperty, 7,
		uintptr(unsafe.Pointer(pKeyIdentifier)),
		uintptr(dwPropId),
		uintptr(dwFlags),
		uintptr(unsafe.Pointer(&pwszComputerNameStr[0])),
		pvReserved,
		pvData,
		uintptr(unsafe.Pointer(pcbData)),
		0,
		0)
	return ret1 != 0
}

// TODO: Unknown type(s): HCRYPTPROV_LEGACY
// func CryptGetMessageCertificates(dwMsgAndCertEncodingType DWORD, hCryptProv HCRYPTPROV_LEGACY, dwFlags DWORD, pbSignedBlob /*const*/ *byte, cbSignedBlob DWORD) HCERTSTORE

func CryptGetMessageSignerCount(dwMsgEncodingType DWORD, pbSignedBlob /*const*/ *byte, cbSignedBlob DWORD) LONG {
	ret1 := syscall3(cryptGetMessageSignerCount, 3,
		uintptr(dwMsgEncodingType),
		uintptr(unsafe.Pointer(pbSignedBlob)),
		uintptr(cbSignedBlob))
	return LONG(ret1)
}

// TODO: Unknown type(s): HCRYPTOIDFUNCADDR *, HCRYPTOIDFUNCSET
// func CryptGetOIDFunctionAddress(hFuncSet HCRYPTOIDFUNCSET, dwEncodingType DWORD, pszOID /*const*/ LPCSTR, dwFlags DWORD, ppvFuncAddr uintptr, phFuncAddr HCRYPTOIDFUNCADDR *) bool

func CryptGetOIDFunctionValue(dwEncodingType DWORD, pszFuncName /*const*/ LPCSTR, pszOID /*const*/ LPCSTR, pwszValueName string, pdwValueType *uint32, pbValueData *byte, pcbValueData *uint32) bool {
	pwszValueNameStr := unicode16FromString(pwszValueName)
	ret1 := syscall9(cryptGetOIDFunctionValue, 7,
		uintptr(dwEncodingType),
		uintptr(unsafe.Pointer(pszFuncName)),
		uintptr(unsafe.Pointer(pszOID)),
		uintptr(unsafe.Pointer(&pwszValueNameStr[0])),
		uintptr(unsafe.Pointer(pdwValueType)),
		uintptr(unsafe.Pointer(pbValueData)),
		uintptr(unsafe.Pointer(pcbValueData)),
		0,
		0)
	return ret1 != 0
}

// TODO: Unknown type(s): HCRYPTPROV_LEGACY
// func CryptHashCertificate(hCryptProv HCRYPTPROV_LEGACY, algid ALG_ID, dwFlags DWORD, pbEncoded /*const*/ *byte, cbEncoded DWORD, pbComputedHash *byte, pcbComputedHash *uint32) bool

// TODO: Unknown type(s): PCRYPT_HASH_MESSAGE_PARA
// func CryptHashMessage(pHashPara PCRYPT_HASH_MESSAGE_PARA, fDetachedHash bool, cToBeHashed DWORD, rgpbToBeHashed /*const*/ **BYTE, rgcbToBeHashed *uint32, pbHashedBlob *byte, pcbHashedBlob *uint32, pbComputedHash *byte, pcbComputedHash *uint32) bool

// TODO: Unknown type(s): HCRYPTPROV_LEGACY, PCERT_PUBLIC_KEY_INFO
// func CryptHashPublicKeyInfo(hCryptProv HCRYPTPROV_LEGACY, algid ALG_ID, dwFlags DWORD, dwCertEncodingType DWORD, pInfo PCERT_PUBLIC_KEY_INFO, pbComputedHash *byte, pcbComputedHash *uint32) bool

// TODO: Unknown type(s): HCRYPTPROV_LEGACY
// func CryptHashToBeSigned(hCryptProv HCRYPTPROV_LEGACY, dwCertEncodingType DWORD, pbEncoded /*const*/ *byte, cbEncoded DWORD, pbComputedHash *byte, pcbComputedHash *uint32) bool

// TODO: Unknown type(s): CRYPT_PKCS8_IMPORT_PARAMS
// func CryptImportPKCS8(sPrivateKeyAndParams CRYPT_PKCS8_IMPORT_PARAMS, dwFlags DWORD, phCryptProv *HCRYPTPROV, pvAuxInfo uintptr) bool

// TODO: Unknown type(s): PCERT_PUBLIC_KEY_INFO
// func CryptImportPublicKeyInfo(hCryptProv HCRYPTPROV, dwCertEncodingType DWORD, pInfo PCERT_PUBLIC_KEY_INFO, phKey *HCRYPTKEY) bool

// TODO: Unknown type(s): PCERT_PUBLIC_KEY_INFO
// func CryptImportPublicKeyInfoEx(hCryptProv HCRYPTPROV, dwCertEncodingType DWORD, pInfo PCERT_PUBLIC_KEY_INFO, aiKeyAlg ALG_ID, dwFlags DWORD, pvAuxInfo uintptr, phKey *HCRYPTKEY) bool

// TODO: Unknown type(s): HCRYPTOIDFUNCSET
// func CryptInitOIDFunctionSet(pszFuncName /*const*/ LPCSTR, dwFlags DWORD) HCRYPTOIDFUNCSET

// TODO: Unknown type(s): HCRYPTDEFAULTCONTEXT *
// func CryptInstallDefaultContext(hCryptProv HCRYPTPROV, dwDefaultType DWORD, pvDefaultPara /*const*/ uintptr, dwFlags DWORD, pvReserved uintptr, phDefaultContext HCRYPTDEFAULTCONTEXT *) bool

// TODO: Unknown type(s): const CRYPT_OID_FUNC_ENTRY*
// func CryptInstallOIDFunctionAddress(hModule HMODULE, dwEncodingType DWORD, pszFuncName /*const*/ LPCSTR, cFuncEntry DWORD, rgFuncEntry /*const*/ const CRYPT_OID_FUNC_ENTRY*, dwFlags DWORD) bool

func CryptMemAlloc(cbSize ULONG) LPVOID {
	ret1 := syscall3(cryptMemAlloc, 1,
		uintptr(cbSize),
		0,
		0)
	return (LPVOID)(unsafe.Pointer(ret1))
}

func CryptMemFree(pv LPVOID) {
	syscall3(cryptMemFree, 1,
		uintptr(unsafe.Pointer(pv)),
		0,
		0)
}

func CryptMemRealloc(pv LPVOID, cbSize ULONG) LPVOID {
	ret1 := syscall3(cryptMemRealloc, 2,
		uintptr(unsafe.Pointer(pv)),
		uintptr(cbSize),
		0)
	return (LPVOID)(unsafe.Pointer(ret1))
}

func CryptMsgCalculateEncodedLength(dwMsgEncodingType DWORD, dwFlags DWORD, dwMsgType DWORD, pvMsgEncodeInfo /*const*/ uintptr, pszInnerContentObjID LPSTR, cbData DWORD) DWORD {
	ret1 := syscall6(cryptMsgCalculateEncodedLength, 6,
		uintptr(dwMsgEncodingType),
		uintptr(dwFlags),
		uintptr(dwMsgType),
		pvMsgEncodeInfo,
		uintptr(unsafe.Pointer(pszInnerContentObjID)),
		uintptr(cbData))
	return DWORD(ret1)
}

func CryptMsgClose(hCryptMsg HCRYPTMSG) bool {
	ret1 := syscall3(cryptMsgClose, 1,
		uintptr(unsafe.Pointer(hCryptMsg)),
		0,
		0)
	return ret1 != 0
}

func CryptMsgControl(hCryptMsg HCRYPTMSG, dwFlags DWORD, dwCtrlType DWORD, pvCtrlPara /*const*/ uintptr) bool {
	ret1 := syscall6(cryptMsgControl, 4,
		uintptr(unsafe.Pointer(hCryptMsg)),
		uintptr(dwFlags),
		uintptr(dwCtrlType),
		pvCtrlPara,
		0,
		0)
	return ret1 != 0
}

// TODO: Unknown type(s): PCMSG_SIGNER_ENCODE_INFO
// func CryptMsgCountersign(hCryptMsg HCRYPTMSG, dwIndex DWORD, cCountersigners DWORD, rgCountersigners PCMSG_SIGNER_ENCODE_INFO) bool

// TODO: Unknown type(s): PCMSG_SIGNER_ENCODE_INFO
// func CryptMsgCountersignEncoded(dwEncodingType DWORD, pbSignerInfo *byte, cbSignerInfo DWORD, cCountersigners DWORD, rgCountersigners PCMSG_SIGNER_ENCODE_INFO, pbCountersignature *byte, pcbCountersignature *DWORD) bool

func CryptMsgDuplicate(hCryptMsg HCRYPTMSG) HCRYPTMSG {
	ret1 := syscall3(cryptMsgDuplicate, 1,
		uintptr(unsafe.Pointer(hCryptMsg)),
		0,
		0)
	return (HCRYPTMSG)(unsafe.Pointer(ret1))
}

// TODO: Unknown type(s): PCMSG_SIGNED_ENCODE_INFO
// func CryptMsgEncodeAndSignCTL(dwMsgEncodingType DWORD, pCtlInfo PCTL_INFO, pSignInfo PCMSG_SIGNED_ENCODE_INFO, dwFlags DWORD, pbEncoded *byte, pcbEncoded *uint32) bool

func CryptMsgGetAndVerifySigner(hCryptMsg HCRYPTMSG, cSignerStore DWORD, rghSignerStore *HCERTSTORE, dwFlags DWORD, ppSigner *PCCERT_CONTEXT, pdwSignerIndex *uint32) bool {
	ret1 := syscall6(cryptMsgGetAndVerifySigner, 6,
		uintptr(unsafe.Pointer(hCryptMsg)),
		uintptr(cSignerStore),
		uintptr(unsafe.Pointer(rghSignerStore)),
		uintptr(dwFlags),
		uintptr(unsafe.Pointer(ppSigner)),
		uintptr(unsafe.Pointer(pdwSignerIndex)))
	return ret1 != 0
}

func CryptMsgGetParam(hCryptMsg HCRYPTMSG, dwParamType DWORD, dwIndex DWORD, pvData uintptr, pcbData *uint32) bool {
	ret1 := syscall6(cryptMsgGetParam, 5,
		uintptr(unsafe.Pointer(hCryptMsg)),
		uintptr(dwParamType),
		uintptr(dwIndex),
		pvData,
		uintptr(unsafe.Pointer(pcbData)),
		0)
	return ret1 != 0
}

// TODO: Unknown type(s): HCRYPTPROV_LEGACY, PCMSG_STREAM_INFO
// func CryptMsgOpenToDecode(dwMsgEncodingType DWORD, dwFlags DWORD, dwMsgType DWORD, hCryptProv HCRYPTPROV_LEGACY, pRecipientInfo PCERT_INFO, pStreamInfo PCMSG_STREAM_INFO) HCRYPTMSG

// TODO: Unknown type(s): PCMSG_STREAM_INFO
// func CryptMsgOpenToEncode(dwMsgEncodingType DWORD, dwFlags DWORD, dwMsgType DWORD, pvMsgEncodeInfo /*const*/ uintptr, pszInnerContentObjID LPSTR, pStreamInfo PCMSG_STREAM_INFO) HCRYPTMSG

// TODO: Unknown type(s): PCMSG_SIGNED_ENCODE_INFO
// func CryptMsgSignCTL(dwMsgEncodingType DWORD, pbCtlContent *byte, cbCtlContent DWORD, pSignInfo PCMSG_SIGNED_ENCODE_INFO, dwFlags DWORD, pbEncoded *byte, pcbEncoded *uint32) bool

func CryptMsgUpdate(hCryptMsg HCRYPTMSG, pbData /*const*/ *byte, cbData DWORD, fFinal bool) bool {
	ret1 := syscall6(cryptMsgUpdate, 4,
		uintptr(unsafe.Pointer(hCryptMsg)),
		uintptr(unsafe.Pointer(pbData)),
		uintptr(cbData),
		getUintptrFromBool(fFinal),
		0,
		0)
	return ret1 != 0
}

// TODO: Unknown type(s): HCRYPTPROV_LEGACY
// func CryptMsgVerifyCountersignatureEncoded(hCryptProv HCRYPTPROV_LEGACY, dwEncodingType DWORD, pbSignerInfo *byte, cbSignerInfo DWORD, pbSignerInfoCountersignature *byte, cbSignerInfoCountersignature DWORD, pciCountersigner PCERT_INFO) bool

// TODO: Unknown type(s): HCRYPTPROV_LEGACY
// func CryptMsgVerifyCountersignatureEncodedEx(hCryptProv HCRYPTPROV_LEGACY, dwEncodingType DWORD, pbSignerInfo *byte, cbSignerInfo DWORD, pbSignerInfoCountersignature *byte, cbSignerInfoCountersignature DWORD, dwSignerType DWORD, pvSigner uintptr, dwFlags DWORD, pvExtra uintptr) bool

// TODO: Unknown type(s): CRYPTPROTECT_PROMPTSTRUCT *
// func CryptProtectData(pDataIn *DATA_BLOB, szDataDescr string, pOptionalEntropy *DATA_BLOB, pvReserved uintptr, pPromptStruct CRYPTPROTECT_PROMPTSTRUCT *, dwFlags DWORD, pDataOut *DATA_BLOB) bool

func CryptProtectMemory(pDataIn LPVOID, cbDataIn DWORD, dwFlags DWORD) bool {
	ret1 := syscall3(cryptProtectMemory, 3,
		uintptr(unsafe.Pointer(pDataIn)),
		uintptr(cbDataIn),
		uintptr(dwFlags))
	return ret1 != 0
}

func CryptQueryObject(dwObjectType DWORD, pvObject /*const*/ uintptr, dwExpectedContentTypeFlags DWORD, dwExpectedFormatTypeFlags DWORD, dwFlags DWORD, pdwMsgAndCertEncodingType *uint32, pdwContentType *uint32, pdwFormatType *uint32, phCertStore *HCERTSTORE, phMsg *HCRYPTMSG, ppvContext /*const*/ uintptr) bool {
	ret1 := syscall12(cryptQueryObject, 11,
		uintptr(dwObjectType),
		pvObject,
		uintptr(dwExpectedContentTypeFlags),
		uintptr(dwExpectedFormatTypeFlags),
		uintptr(dwFlags),
		uintptr(unsafe.Pointer(pdwMsgAndCertEncodingType)),
		uintptr(unsafe.Pointer(pdwContentType)),
		uintptr(unsafe.Pointer(pdwFormatType)),
		uintptr(unsafe.Pointer(phCertStore)),
		uintptr(unsafe.Pointer(phMsg)),
		ppvContext,
		0)
	return ret1 != 0
}

func CryptRegisterDefaultOIDFunction(dwEncodingType DWORD, pszFuncName /*const*/ LPCSTR, dwIndex DWORD, pwszDll string) bool {
	pwszDllStr := unicode16FromString(pwszDll)
	ret1 := syscall6(cryptRegisterDefaultOIDFunction, 4,
		uintptr(dwEncodingType),
		uintptr(unsafe.Pointer(pszFuncName)),
		uintptr(dwIndex),
		uintptr(unsafe.Pointer(&pwszDllStr[0])),
		0,
		0)
	return ret1 != 0
}

func CryptRegisterOIDFunction(dwEncodingType DWORD, pszFuncName /*const*/ LPCSTR, pszOID /*const*/ LPCSTR, pwszDll string, pszOverrideFuncName /*const*/ LPCSTR) bool {
	pwszDllStr := unicode16FromString(pwszDll)
	ret1 := syscall6(cryptRegisterOIDFunction, 5,
		uintptr(dwEncodingType),
		uintptr(unsafe.Pointer(pszFuncName)),
		uintptr(unsafe.Pointer(pszOID)),
		uintptr(unsafe.Pointer(&pwszDllStr[0])),
		uintptr(unsafe.Pointer(pszOverrideFuncName)),
		0)
	return ret1 != 0
}

// TODO: Unknown type(s): PCCRYPT_OID_INFO
// func CryptRegisterOIDInfo(pInfo PCCRYPT_OID_INFO, dwFlags DWORD) bool

// TODO: Unknown type(s): SIP_ADD_NEWPROVIDER *
// func CryptSIPAddProvider(psNewProv SIP_ADD_NEWPROVIDER *) bool

// TODO: Unknown type(s): SIP_INDIRECT_DATA *, SIP_SUBJECTINFO *
// func CryptSIPCreateIndirectData(pSubjectInfo SIP_SUBJECTINFO *, pcbIndirectData *uint32, pIndirectData SIP_INDIRECT_DATA *) bool

// TODO: Unknown type(s): SIP_SUBJECTINFO *
// func CryptSIPGetSignedDataMsg(pSubjectInfo SIP_SUBJECTINFO *, pdwEncodingType *uint32, dwIndex DWORD, pcbSignedDataMsg *uint32, pbSignedDataMsg *byte) bool

// TODO: Unknown type(s): SIP_DISPATCH_INFO *
// func CryptSIPLoad(pgSubject /*const*/ *GUID, dwFlags DWORD, pSipDispatch SIP_DISPATCH_INFO *) bool

// TODO: Unknown type(s): SIP_SUBJECTINFO *
// func CryptSIPPutSignedDataMsg(pSubjectInfo SIP_SUBJECTINFO *, dwEncodingType DWORD, pdwIndex *uint32, cbSignedDataMsg DWORD, pbSignedDataMsg *byte) bool

func CryptSIPRemoveProvider(pgProv *GUID) bool {
	ret1 := syscall3(cryptSIPRemoveProvider, 1,
		uintptr(unsafe.Pointer(pgProv)),
		0,
		0)
	return ret1 != 0
}

// TODO: Unknown type(s): SIP_SUBJECTINFO *
// func CryptSIPRemoveSignedDataMsg(pSubjectInfo SIP_SUBJECTINFO *, dwIndex DWORD) bool

func CryptSIPRetrieveSubjectGuid(fileName string, hFileIn HANDLE, pgSubject *GUID) bool {
	fileNameStr := unicode16FromString(fileName)
	ret1 := syscall3(cryptSIPRetrieveSubjectGuid, 3,
		uintptr(unsafe.Pointer(&fileNameStr[0])),
		uintptr(hFileIn),
		uintptr(unsafe.Pointer(pgSubject)))
	return ret1 != 0
}

func CryptSIPRetrieveSubjectGuidForCatalogFile(fileName string, hFileIn HANDLE, pgSubject *GUID) bool {
	fileNameStr := unicode16FromString(fileName)
	ret1 := syscall3(cryptSIPRetrieveSubjectGuidForCatalogFile, 3,
		uintptr(unsafe.Pointer(&fileNameStr[0])),
		uintptr(hFileIn),
		uintptr(unsafe.Pointer(pgSubject)))
	return ret1 != 0
}

// TODO: Unknown type(s): SIP_INDIRECT_DATA *, SIP_SUBJECTINFO *
// func CryptSIPVerifyIndirectData(pSubjectInfo SIP_SUBJECTINFO *, pIndirectData SIP_INDIRECT_DATA *) bool

// TODO: Unknown type(s): HCRYPTASYNC, PFN_CRYPT_ASYNC_PARAM_FREE_FUNC
// func CryptSetAsyncParam(hAsync HCRYPTASYNC, pszParamOid LPSTR, pvParam LPVOID, pfnFree PFN_CRYPT_ASYNC_PARAM_FREE_FUNC) bool

func CryptSetKeyIdentifierProperty(pKeyIdentifier /*const*/ *CRYPT_HASH_BLOB, dwPropId DWORD, dwFlags DWORD, pwszComputerName string, pvReserved uintptr, pvData /*const*/ uintptr) bool {
	pwszComputerNameStr := unicode16FromString(pwszComputerName)
	ret1 := syscall6(cryptSetKeyIdentifierProperty, 6,
		uintptr(unsafe.Pointer(pKeyIdentifier)),
		uintptr(dwPropId),
		uintptr(dwFlags),
		uintptr(unsafe.Pointer(&pwszComputerNameStr[0])),
		pvReserved,
		pvData)
	return ret1 != 0
}

func CryptSetOIDFunctionValue(dwEncodingType DWORD, pszFuncName /*const*/ LPCSTR, pszOID /*const*/ LPCSTR, pwszValueName string, dwValueType DWORD, pbValueData /*const*/ *byte, cbValueData DWORD) bool {
	pwszValueNameStr := unicode16FromString(pwszValueName)
	ret1 := syscall9(cryptSetOIDFunctionValue, 7,
		uintptr(dwEncodingType),
		uintptr(unsafe.Pointer(pszFuncName)),
		uintptr(unsafe.Pointer(pszOID)),
		uintptr(unsafe.Pointer(&pwszValueNameStr[0])),
		uintptr(dwValueType),
		uintptr(unsafe.Pointer(pbValueData)),
		uintptr(cbValueData),
		0,
		0)
	return ret1 != 0
}

// TODO: Unknown type(s): HCRYPTPROV_OR_NCRYPT_KEY_HANDLE, PCRYPT_ALGORITHM_IDENTIFIER
// func CryptSignAndEncodeCertificate(hCryptProvOrNCryptKey HCRYPTPROV_OR_NCRYPT_KEY_HANDLE, dwKeySpec DWORD, dwCertEncodingType DWORD, lpszStructType /*const*/ LPCSTR, pvStructInfo /*const*/ uintptr, pSignatureAlgorithm PCRYPT_ALGORITHM_IDENTIFIER, pvHashAuxInfo /*const*/ uintptr, pbEncoded *byte, pcbEncoded *uint32) bool

// TODO: Unknown type(s): PCRYPT_ENCRYPT_MESSAGE_PARA, PCRYPT_SIGN_MESSAGE_PARA
// func CryptSignAndEncryptMessage(pSignPara PCRYPT_SIGN_MESSAGE_PARA, pEncryptPara PCRYPT_ENCRYPT_MESSAGE_PARA, cRecipientCert DWORD, rgpRecipientCert *PCCERT_CONTEXT, pbToBeSignedAndEncrypted /*const*/ *byte, cbToBeSignedAndEncrypted DWORD, pbSignedAndEncryptedBlob *byte, pcbSignedAndEncryptedBlob *uint32) bool

// TODO: Unknown type(s): HCRYPTPROV_OR_NCRYPT_KEY_HANDLE, PCRYPT_ALGORITHM_IDENTIFIER
// func CryptSignCertificate(hCryptProvOrNCryptKey HCRYPTPROV_OR_NCRYPT_KEY_HANDLE, dwKeySpec DWORD, dwCertEncodingType DWORD, pbEncodedToBeSigned /*const*/ *byte, cbEncodedToBeSigned DWORD, pSignatureAlgorithm PCRYPT_ALGORITHM_IDENTIFIER, pvHashAuxInfo /*const*/ uintptr, pbSignature *byte, pcbSignature *uint32) bool

// TODO: Unknown type(s): PCRYPT_SIGN_MESSAGE_PARA
// func CryptSignMessage(pSignPara PCRYPT_SIGN_MESSAGE_PARA, fDetachedSignature bool, cToBeSigned DWORD, rgpbToBeSigned /*const*/ **BYTE, rgcbToBeSigned *uint32, pbSignedBlob *byte, pcbSignedBlob *uint32) bool

// TODO: Unknown type(s): PCRYPT_KEY_SIGN_MESSAGE_PARA
// func CryptSignMessageWithKey(pSignPara PCRYPT_KEY_SIGN_MESSAGE_PARA, pbToBeSigned /*const*/ *byte, cbToBeSigned DWORD, pbSignedBlob *byte, pcbSignedBlob *uint32) bool

func CryptStringToBinary(pszString string, cchString DWORD, dwFlags DWORD, pbBinary *byte, pcbBinary *uint32, pdwSkip *uint32, pdwFlags *uint32) bool {
	pszStringStr := unicode16FromString(pszString)
	ret1 := syscall9(cryptStringToBinary, 7,
		uintptr(unsafe.Pointer(&pszStringStr[0])),
		uintptr(cchString),
		uintptr(dwFlags),
		uintptr(unsafe.Pointer(pbBinary)),
		uintptr(unsafe.Pointer(pcbBinary)),
		uintptr(unsafe.Pointer(pdwSkip)),
		uintptr(unsafe.Pointer(pdwFlags)),
		0,
		0)
	return ret1 != 0
}

// TODO: Unknown type(s): HCRYPTDEFAULTCONTEXT
// func CryptUninstallDefaultContext(hDefaultContext HCRYPTDEFAULTCONTEXT, dwFlags DWORD, pvReserved uintptr) bool

// TODO: Unknown type(s): CRYPTPROTECT_PROMPTSTRUCT *
// func CryptUnprotectData(pDataIn *DATA_BLOB, ppszDataDescr *LPWSTR, pOptionalEntropy *DATA_BLOB, pvReserved uintptr, pPromptStruct CRYPTPROTECT_PROMPTSTRUCT *, dwFlags DWORD, pDataOut *DATA_BLOB) bool

func CryptUnprotectMemory(pDataIn LPVOID, cbDataIn DWORD, dwFlags DWORD) bool {
	ret1 := syscall3(cryptUnprotectMemory, 3,
		uintptr(unsafe.Pointer(pDataIn)),
		uintptr(cbDataIn),
		uintptr(dwFlags))
	return ret1 != 0
}

func CryptUnregisterDefaultOIDFunction(dwEncodingType DWORD, pszFuncName /*const*/ LPCSTR, pwszDll string) bool {
	pwszDllStr := unicode16FromString(pwszDll)
	ret1 := syscall3(cryptUnregisterDefaultOIDFunction, 3,
		uintptr(dwEncodingType),
		uintptr(unsafe.Pointer(pszFuncName)),
		uintptr(unsafe.Pointer(&pwszDllStr[0])))
	return ret1 != 0
}

func CryptUnregisterOIDFunction(dwEncodingType DWORD, pszFuncName /*const*/ LPCSTR, pszOID /*const*/ LPCSTR) bool {
	ret1 := syscall3(cryptUnregisterOIDFunction, 3,
		uintptr(dwEncodingType),
		uintptr(unsafe.Pointer(pszFuncName)),
		uintptr(unsafe.Pointer(pszOID)))
	return ret1 != 0
}

// TODO: Unknown type(s): PCCRYPT_OID_INFO
// func CryptUnregisterOIDInfo(pInfo PCCRYPT_OID_INFO) bool

// TODO: Unknown type(s): HCRYPTPROV_LEGACY, PCERT_PUBLIC_KEY_INFO
// func CryptVerifyCertificateSignature(hCryptProv HCRYPTPROV_LEGACY, dwCertEncodingType DWORD, pbEncoded /*const*/ *byte, cbEncoded DWORD, pPublicKey PCERT_PUBLIC_KEY_INFO) bool

// TODO: Unknown type(s): HCRYPTPROV_LEGACY
// func CryptVerifyCertificateSignatureEx(hCryptProv HCRYPTPROV_LEGACY, dwCertEncodingType DWORD, dwSubjectType DWORD, pvSubject uintptr, dwIssuerType DWORD, pvIssuer uintptr, dwFlags DWORD, pvExtra uintptr) bool

// TODO: Unknown type(s): PCRYPT_HASH_MESSAGE_PARA
// func CryptVerifyDetachedMessageHash(pHashPara PCRYPT_HASH_MESSAGE_PARA, pbDetachedHashBlob *byte, cbDetachedHashBlob DWORD, cToBeHashed DWORD, rgpbToBeHashed /*const*/ **BYTE, rgcbToBeHashed *uint32, pbComputedHash *byte, pcbComputedHash *uint32) bool

// TODO: Unknown type(s): PCRYPT_VERIFY_MESSAGE_PARA
// func CryptVerifyDetachedMessageSignature(pVerifyPara PCRYPT_VERIFY_MESSAGE_PARA, dwSignerIndex DWORD, pbDetachedSignBlob /*const*/ *byte, cbDetachedSignBlob DWORD, cToBeSigned DWORD, rgpbToBeSigned /*const*/ **BYTE, rgcbToBeSigned *uint32, ppSignerCert *PCCERT_CONTEXT) bool

// TODO: Unknown type(s): PCRYPT_HASH_MESSAGE_PARA
// func CryptVerifyMessageHash(pHashPara PCRYPT_HASH_MESSAGE_PARA, pbHashedBlob *byte, cbHashedBlob DWORD, pbToBeHashed *byte, pcbToBeHashed *uint32, pbComputedHash *byte, pcbComputedHash *uint32) bool

// TODO: Unknown type(s): PCRYPT_VERIFY_MESSAGE_PARA
// func CryptVerifyMessageSignature(pVerifyPara PCRYPT_VERIFY_MESSAGE_PARA, dwSignerIndex DWORD, pbSignedBlob /*const*/ *byte, cbSignedBlob DWORD, pbDecoded *byte, pcbDecoded *uint32, ppSignerCert *PCCERT_CONTEXT) bool

// TODO: Unknown type(s): PCERT_PUBLIC_KEY_INFO, PCRYPT_KEY_VERIFY_MESSAGE_PARA
// func CryptVerifyMessageSignatureWithKey(pVerifyPara PCRYPT_KEY_VERIFY_MESSAGE_PARA, pPublicKeyInfo PCERT_PUBLIC_KEY_INFO, pbSignedBlob /*const*/ *byte, cbSignedBlob DWORD, pbDecoded *byte, pcbDecoded *uint32) bool

// TODO: Unknown type(s): ASN1decoding_t, HCRYPTASN1MODULE
// func I_CryptGetAsn1Decoder(hAsn1Module HCRYPTASN1MODULE) ASN1decoding_t

// TODO: Unknown type(s): ASN1encoding_t, HCRYPTASN1MODULE
// func I_CryptGetAsn1Encoder(hAsn1Module HCRYPTASN1MODULE) ASN1encoding_t

// TODO: Unknown type(s): ASN1module_t, HCRYPTASN1MODULE
// func I_CryptInstallAsn1Module(pMod ASN1module_t, dwFlags DWORD, pvReserved uintptr) HCRYPTASN1MODULE

// TODO: Unknown type(s): HCRYPTASN1MODULE
// func I_CryptUninstallAsn1Module(hAsn1Module HCRYPTASN1MODULE) bool

func PFXExportCertStore(hStore HCERTSTORE, pPFX *CRYPT_DATA_BLOB, szPassword string, dwFlags DWORD) bool {
	szPasswordStr := unicode16FromString(szPassword)
	ret1 := syscall6(pFXExportCertStore, 4,
		uintptr(hStore),
		uintptr(unsafe.Pointer(pPFX)),
		uintptr(unsafe.Pointer(&szPasswordStr[0])),
		uintptr(dwFlags),
		0,
		0)
	return ret1 != 0
}

func PFXExportCertStoreEx(hStore HCERTSTORE, pPFX *CRYPT_DATA_BLOB, szPassword string, pvPara uintptr, dwFlags DWORD) bool {
	szPasswordStr := unicode16FromString(szPassword)
	ret1 := syscall6(pFXExportCertStoreEx, 5,
		uintptr(hStore),
		uintptr(unsafe.Pointer(pPFX)),
		uintptr(unsafe.Pointer(&szPasswordStr[0])),
		pvPara,
		uintptr(dwFlags),
		0)
	return ret1 != 0
}

func PFXImportCertStore(pPFX *CRYPT_DATA_BLOB, szPassword string, dwFlags DWORD) HCERTSTORE {
	szPasswordStr := unicode16FromString(szPassword)
	ret1 := syscall3(pFXImportCertStore, 3,
		uintptr(unsafe.Pointer(pPFX)),
		uintptr(unsafe.Pointer(&szPasswordStr[0])),
		uintptr(dwFlags))
	return HCERTSTORE(ret1)
}

func PFXIsPFXBlob(pPFX *CRYPT_DATA_BLOB) bool {
	ret1 := syscall3(pFXIsPFXBlob, 1,
		uintptr(unsafe.Pointer(pPFX)),
		0,
		0)
	return ret1 != 0
}

func PFXVerifyPassword(pPFX *CRYPT_DATA_BLOB, szPassword string, dwFlags DWORD) bool {
	szPasswordStr := unicode16FromString(szPassword)
	ret1 := syscall3(pFXVerifyPassword, 3,
		uintptr(unsafe.Pointer(pPFX)),
		uintptr(unsafe.Pointer(&szPasswordStr[0])),
		uintptr(dwFlags))
	return ret1 != 0
}

func I_CertUpdateStore(store1 HCERTSTORE, store2 HCERTSTORE, unk0 DWORD, unk1 DWORD) bool {
	ret1 := syscall6(i_CertUpdateStore, 4,
		uintptr(store1),
		uintptr(store2),
		uintptr(unk0),
		uintptr(unk1),
		0,
		0)
	return ret1 != 0
}

func I_CryptAllocTls() DWORD {
	ret1 := syscall3(i_CryptAllocTls, 0,
		0,
		0,
		0)
	return DWORD(ret1)
}

// TODO: Unknown type(s): HLRUCACHE *
// func I_CryptCreateLruCache(unknown uintptr, out HLRUCACHE *) bool

// TODO: Unknown type(s): HLRUCACHE
// func I_CryptCreateLruEntry(h HLRUCACHE, unk0 DWORD, unk1 DWORD) bool

func I_CryptDetachTls(dwTlsIndex DWORD) LPVOID {
	ret1 := syscall3(i_CryptDetachTls, 1,
		uintptr(dwTlsIndex),
		0,
		0)
	return (LPVOID)(unsafe.Pointer(ret1))
}

func I_CryptFindLruEntry(unk0 DWORD, unk1 DWORD) bool {
	ret1 := syscall3(i_CryptFindLruEntry, 2,
		uintptr(unk0),
		uintptr(unk1),
		0)
	return ret1 != 0
}

func I_CryptFindLruEntryData(unk0 DWORD, unk1 DWORD, unk2 DWORD) bool {
	ret1 := syscall3(i_CryptFindLruEntryData, 3,
		uintptr(unk0),
		uintptr(unk1),
		uintptr(unk2))
	return ret1 != 0
}

// TODO: Unknown type(s): HLRUCACHE
// func I_CryptFlushLruCache(h HLRUCACHE, unk0 DWORD, unk1 DWORD) DWORD

// TODO: Unknown type(s): HLRUCACHE
// func I_CryptFreeLruCache(h HLRUCACHE, unk0 DWORD, unk1 DWORD) HLRUCACHE

func I_CryptFreeTls(dwTlsIndex DWORD, unknown DWORD) bool {
	ret1 := syscall3(i_CryptFreeTls, 2,
		uintptr(dwTlsIndex),
		uintptr(unknown),
		0)
	return ret1 != 0
}

func I_CryptGetDefaultCryptProv(reserved DWORD) HCRYPTPROV {
	ret1 := syscall3(i_CryptGetDefaultCryptProv, 1,
		uintptr(reserved),
		0,
		0)
	return HCRYPTPROV(ret1)
}

func I_CryptGetOssGlobal(x DWORD) bool {
	ret1 := syscall3(i_CryptGetOssGlobal, 1,
		uintptr(x),
		0,
		0)
	return ret1 != 0
}

func I_CryptGetTls(dwTlsIndex DWORD) LPVOID {
	ret1 := syscall3(i_CryptGetTls, 1,
		uintptr(dwTlsIndex),
		0,
		0)
	return (LPVOID)(unsafe.Pointer(ret1))
}

func I_CryptInstallOssGlobal(x DWORD, y DWORD, z DWORD) DWORD {
	ret1 := syscall3(i_CryptInstallOssGlobal, 3,
		uintptr(x),
		uintptr(y),
		uintptr(z))
	return DWORD(ret1)
}

func I_CryptReadTrustedPublisherDWORDValueFromRegistry(name string, value *uint32) bool {
	nameStr := unicode16FromString(name)
	ret1 := syscall3(i_CryptReadTrustedPublisherDWORDValueFromRegistry, 2,
		uintptr(unsafe.Pointer(&nameStr[0])),
		uintptr(unsafe.Pointer(value)),
		0)
	return ret1 != 0
}

func I_CryptSetTls(dwTlsIndex DWORD, lpTlsValue LPVOID) bool {
	ret1 := syscall3(i_CryptSetTls, 2,
		uintptr(dwTlsIndex),
		uintptr(unsafe.Pointer(lpTlsValue)),
		0)
	return ret1 != 0
}
