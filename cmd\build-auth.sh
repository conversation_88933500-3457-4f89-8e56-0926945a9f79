#!/bin/bash

# Lightx 认证版本编译脚本
# 支持编译正常版本和限制版本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo -e "${BLUE}Lightx 认证版本编译脚本${NC}"
    echo ""
    echo "用法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  normal              编译正常版本（无限制）"
    echo "  restricted [days]   编译限制版本（默认30天有效期）"
    echo "  test [minutes]      编译测试版本（分钟级有效期，用于测试）"
    echo "  clean              清理编译文件"
    echo "  help               显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 normal                    # 编译正常版本"
    echo "  $0 restricted               # 编译30天限制版本"
    echo "  $0 restricted 7             # 编译7天限制版本"
    echo "  $0 test 10                  # 编译10分钟测试版本"
    echo ""
}

# 清理函数
clean_build() {
    echo -e "${YELLOW}清理编译文件...${NC}"
    rm -f ../../lightx ../../lightx-restricted ../../lightx-test
    echo -e "${GREEN}清理完成${NC}"
}

# 编译正常版本
build_normal() {
    echo -e "${BLUE}编译正常版本（无限制）...${NC}"
    
    # 获取版本信息
    VERSION=$(git describe --tags --always 2>/dev/null || echo "dev")
    BUILD_TIME=$(date '+%Y-%m-%d %H:%M:%S')
    
    # 编译（添加默认优化参数）
    BUILD_CMD="go build -ldflags \"-s -w -X 'main.Version=${VERSION}' -X 'main.BuildTime=${BUILD_TIME}'\" -trimpath -o ../../lightx"
    echo -e "${YELLOW}执行编译命令:${NC}"
    echo "  ${BUILD_CMD}"
    echo ""
    
    go build -ldflags "-s -w -X 'main.Version=${VERSION}' -X 'main.BuildTime=${BUILD_TIME}'" -trimpath -o ../../lightx
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ 正常版本编译成功: ../../lightx${NC}"
        echo -e "  版本: ${VERSION}"
        echo -e "  编译时间: ${BUILD_TIME}"
    else
        echo -e "${RED}✗ 编译失败${NC}"
        exit 1
    fi
}

# 编译限制版本
build_restricted() {
    local days=${1:-30}
    
    echo -e "${BLUE}编译限制版本（${days}天有效期）...${NC}"
    
    # 获取版本信息
    VERSION=$(git describe --tags --always 2>/dev/null || echo "dev")
    BUILD_TIME=$(date '+%Y-%m-%d %H:%M:%S')
    
    # 编译时设置有效期和时间戳，添加默认优化参数
    BUILD_CMD="go build -tags auth -ldflags \"-s -w -X 'main.Version=${VERSION}' -X 'main.BuildTime=${BUILD_TIME}' -X 'Lightx/internal/common.ValidDaysStr=${days}' -X 'Lightx/internal/common.BuildTimestamp=${BUILD_TIME}'\" -trimpath -o ../../lightx-restricted"
    echo -e "${YELLOW}执行编译命令:${NC}"
    echo "  ${BUILD_CMD}"
    echo ""
    
    go build -tags auth \
        -ldflags "-s -w -X 'main.Version=${VERSION}' -X 'main.BuildTime=${BUILD_TIME}' -X 'Lightx/internal/common.ValidDaysStr=${days}' -X 'Lightx/internal/common.BuildTimestamp=${BUILD_TIME}'" \
        -trimpath \
        -o ../../lightx-restricted
    
    if [ $? -eq 0 ]; then
        # 计算过期时间
        if command -v date >/dev/null 2>&1; then
            if [[ "$OSTYPE" == "darwin"* ]]; then
                # macOS
                EXPIRE_TIME=$(date -v+${days}d '+%Y-%m-%d %H:%M:%S')
            else
                # Linux
                EXPIRE_TIME=$(date -d "+${days} days" '+%Y-%m-%d %H:%M:%S')
            fi
        else
            EXPIRE_TIME="计算失败"
        fi
        
        echo -e "${GREEN}✓ 限制版本编译成功: ../../lightx-restricted${NC}"
        echo -e "  版本: ${VERSION}"
        echo -e "  编译时间: ${BUILD_TIME}"
        echo -e "  有效期: ${days}天"
        echo -e "  过期时间: ${EXPIRE_TIME}"
        echo ""
        echo -e "${YELLOW}注意: 此版本包含时间限制和安全验证${NC}"
    else
        echo -e "${RED}✗ 编译失败${NC}"
        exit 1
    fi
}

# 编译测试版本
build_test() {
    local minutes=${1:-10}
    
    echo -e "${BLUE}编译测试版本（${minutes}分钟有效期）...${NC}"
    
    # 获取版本信息
    VERSION=$(git describe --tags --always 2>/dev/null || echo "dev-test")
    BUILD_TIME=$(date '+%Y-%m-%d %H:%M:%S')
    
    # 编译时设置有效期（分钟）和时间戳，添加默认优化参数
    BUILD_CMD="go build -tags auth -ldflags \"-s -w -X 'main.Version=${VERSION}' -X 'main.BuildTime=${BUILD_TIME}' -X 'Lightx/internal/common.ValidMinutesStr=${minutes}' -X 'Lightx/internal/common.BuildTimestamp=${BUILD_TIME}'\" -trimpath -o ../../lightx-test"
    echo -e "${YELLOW}执行编译命令:${NC}"
    echo "  ${BUILD_CMD}"
    echo ""
    
    go build -tags auth \
        -ldflags "-s -w -X 'main.Version=${VERSION}' -X 'main.BuildTime=${BUILD_TIME}' -X 'Lightx/internal/common.ValidMinutesStr=${minutes}' -X 'Lightx/internal/common.BuildTimestamp=${BUILD_TIME}'" \
        -trimpath \
        -o ../../lightx-test
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ 测试版本编译成功: ../../lightx-test${NC}"
        echo -e "  版本: ${VERSION}"
        echo -e "  编译时间: ${BUILD_TIME}"
        echo -e "  有效期: ${minutes}分钟"
        echo ""
        echo -e "${YELLOW}注意: 此版本仅用于测试，${minutes}分钟后过期${NC}"
    else
        echo -e "${RED}✗ 编译失败${NC}"
        exit 1
    fi
}

# 主逻辑
case "${1:-help}" in
    "normal")
        build_normal
        ;;
    "restricted")
        build_restricted "$2"
        ;;
    "test")
        build_test "$2"
        ;;
    "clean")
        clean_build
        ;;
    "help"|"--help"|"-h")
        show_help
        ;;
    *)
        echo -e "${RED}错误: 未知选项 '$1'${NC}"
        echo ""
        show_help
        exit 1
        ;;
esac
