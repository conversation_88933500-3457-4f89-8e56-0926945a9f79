// This file was automatically generated by https://github.com/kbinani/win/blob/generator/internal/cmd/gen/gen.go
// go run internal/cmd/gen/gen.go

// +build windows

package win

import (
	"syscall"
	"unsafe"
)

var (
	// Library
	libshell32 uintptr

	// Functions
	sHAddFromPropSheetExtArray              uintptr
	sHCreatePropSheetExtArray               uintptr
	sHDestroyPropSheetExtArray              uintptr
	sHReplaceFromPropSheetExtArray          uintptr
	cDefFolderMenu_Create2                  uintptr
	cIDLData_CreateFromIDArray              uintptr
	callCPLEntry16                          uintptr
	checkEscapes                            uintptr
	control_FillCache_RunDLL                uintptr
	control_RunDLL                          uintptr
	dAD_AutoScroll                          uintptr
	dAD_DragEnterEx                         uintptr
	dAD_DragLeave                           uintptr
	dAD_DragMove                            uintptr
	dAD_SetDragImage                        uintptr
	dAD_ShowDragImage                       uintptr
	doEnvironmentSubst                      uintptr
	dragAcceptFiles                         uintptr
	dragFinish                              uintptr
	dragQueryFile                           uintptr
	dragQueryPoint                          uintptr
	driveType                               uintptr
	duplicateIcon                           uintptr
	extractAssociatedIconEx                 uintptr
	extractAssociatedIcon                   uintptr
	extractIconEx                           uintptr
	extractIcon                             uintptr
	extractVersionResource16W               uintptr
	findExecutable                          uintptr
	freeIconList                            uintptr
	getCurrentProcessExplicitAppUserModelID uintptr
	iLAppendID                              uintptr
	iLClone                                 uintptr
	iLCloneFirst                            uintptr
	iLCombine                               uintptr
	iLCreateFromPath                        uintptr
	iLFindChild                             uintptr
	iLFindLastID                            uintptr
	iLFree                                  uintptr
	iLGetNext                               uintptr
	iLGetSize                               uintptr
	iLIsEqual                               uintptr
	iLIsParent                              uintptr
	iLLoadFromStream                        uintptr
	iLRemoveLastID                          uintptr
	iLSaveToStream                          uintptr
	initNetworkAddressControl               uintptr
	isLFNDrive                              uintptr
	isNetDrive                              uintptr
	isUserAnAdmin                           uintptr
	openAs_RunDLL                           uintptr
	pathCleanupSpec                         uintptr
	pathYetAnotherMakeUniqueName            uintptr
	pickIconDlg                             uintptr
	readCabinetState                        uintptr
	realDriveType                           uintptr
	regenerateUserEnvironment               uintptr
	restartDialog                           uintptr
	restartDialogEx                         uintptr
	sHAddToRecentDocs                       uintptr
	sHAlloc                                 uintptr
	sHAppBarMessage                         uintptr
	sHAssocEnumHandlers                     uintptr
	sHBindToParent                          uintptr
	sHBrowseForFolder                       uintptr
	sHChangeNotification_Lock               uintptr
	sHChangeNotification_Unlock             uintptr
	sHChangeNotify                          uintptr
	sHChangeNotifyDeregister                uintptr
	sHChangeNotifyRegister                  uintptr
	sHCloneSpecialIDList                    uintptr
	sHCoCreateInstance                      uintptr
	sHCreateDefaultContextMenu              uintptr
	sHCreateDirectory                       uintptr
	sHCreateDirectoryEx                     uintptr
	sHCreateFileExtractIconW                uintptr
	sHCreateItemFromIDList                  uintptr
	sHCreateItemFromParsingName             uintptr
	sHCreateQueryCancelAutoPlayMoniker      uintptr
	sHCreateShellFolderView                 uintptr
	sHCreateShellFolderViewEx               uintptr
	sHCreateShellItem                       uintptr
	sHCreateShellItemArray                  uintptr
	sHCreateShellItemArrayFromDataObject    uintptr
	sHCreateShellItemArrayFromIDLists       uintptr
	sHCreateShellItemArrayFromShellItem     uintptr
	sHCreateStdEnumFmtEtc                   uintptr
	sHDefExtractIcon                        uintptr
	sHDoDragDrop                            uintptr
	sHEmptyRecycleBin                       uintptr
	sHEnumerateUnreadMailAccountsW          uintptr
	sHFileOperation                         uintptr
	sHFindFiles                             uintptr
	sHFind_InitMenuPopup                    uintptr
	sHFlushClipboard                        uintptr
	sHFlushSFCache                          uintptr
	sHFormatDrive                           uintptr
	sHFree                                  uintptr
	sHFreeNameMappings                      uintptr
	sHGetDataFromIDList                     uintptr
	sHGetDesktopFolder                      uintptr
	sHGetFileInfo                           uintptr
	sHGetFolderLocation                     uintptr
	sHGetFolderPathAndSubDir                uintptr
	sHGetFolderPathEx                       uintptr
	sHGetFolderPath                         uintptr
	sHGetIDListFromObject                   uintptr
	sHGetIconOverlayIndex                   uintptr
	sHGetImageList                          uintptr
	sHGetInstanceExplorer                   uintptr
	sHGetItemFromDataObject                 uintptr
	sHGetItemFromObject                     uintptr
	sHGetKnownFolderIDList                  uintptr
	sHGetKnownFolderItem                    uintptr
	sHGetKnownFolderPath                    uintptr
	sHGetLocalizedName                      uintptr
	sHGetMalloc                             uintptr
	sHGetNameFromIDList                     uintptr
	sHGetNewLinkInfo                        uintptr
	sHGetPathFromIDList                     uintptr
	sHGetPropertyStoreForWindow             uintptr
	sHGetPropertyStoreFromParsingName       uintptr
	sHGetRealIDL                            uintptr
	sHGetSetSettings                        uintptr
	sHGetSettings                           uintptr
	sHGetSpecialFolderLocation              uintptr
	sHGetSpecialFolderPath                  uintptr
	sHGetStockIconInfo                      uintptr
	sHHandleUpdateImage                     uintptr
	sHHelpShortcuts_RunDLL                  uintptr
	sHIsFileAvailableOffline                uintptr
	sHLimitInputEdit                        uintptr
	sHLoadInProc                            uintptr
	sHLoadNonloadedIconOverlayIdentifiers   uintptr
	sHMapIDListToImageListIndexAsync        uintptr
	sHMapPIDLToSystemImageListIndex         uintptr
	sHObjectProperties                      uintptr
	sHOpenFolderAndSelectItems              uintptr
	sHParseDisplayName                      uintptr
	sHPathPrepareForWrite                   uintptr
	sHPropStgCreate                         uintptr
	sHPropStgReadMultiple                   uintptr
	sHPropStgWriteMultiple                  uintptr
	sHQueryRecycleBin                       uintptr
	sHQueryUserNotificationState            uintptr
	sHRemoveLocalizedName                   uintptr
	sHRestricted                            uintptr
	sHRunControlPanel                       uintptr
	sHSetInstanceExplorer                   uintptr
	sHSetLocalizedName                      uintptr
	sHSetUnreadMailCountW                   uintptr
	sHShellFolderView_Message               uintptr
	sHUpdateImage                           uintptr
	sHUpdateRecycleBinIcon                  uintptr
	sHValidateUNC                           uintptr
	setCurrentProcessExplicitAppUserModelID uintptr
	sheChangeDir                            uintptr
	sheGetDir                               uintptr
	shellAbout                              uintptr
	shellExecute                            uintptr
	shell_GetImageLists                     uintptr
	shell_MergeMenus                        uintptr
	shell_NotifyIcon                        uintptr
	signalFileOpen                          uintptr
	wOWShellExecute                         uintptr
	writeCabinetState                       uintptr
)

func init() {
	// Library
	libshell32 = doLoadLibrary("shell32.dll")

	// Functions
	sHAddFromPropSheetExtArray = doGetProcAddress(libshell32, "SHAddFromPropSheetExtArray")
	sHCreatePropSheetExtArray = doGetProcAddress(libshell32, "SHCreatePropSheetExtArray")
	sHDestroyPropSheetExtArray = doGetProcAddress(libshell32, "SHDestroyPropSheetExtArray")
	sHReplaceFromPropSheetExtArray = doGetProcAddress(libshell32, "SHReplaceFromPropSheetExtArray")
	cDefFolderMenu_Create2 = doGetProcAddress(libshell32, "CDefFolderMenu_Create2")
	cIDLData_CreateFromIDArray = doGetProcAddress(libshell32, "CIDLData_CreateFromIDArray")
	callCPLEntry16 = doGetProcAddress(libshell32, "CallCPLEntry16")
	checkEscapes = doGetProcAddress(libshell32, "CheckEscapesW")
	control_FillCache_RunDLL = doGetProcAddress(libshell32, "Control_FillCache_RunDLLW")
	control_RunDLL = doGetProcAddress(libshell32, "Control_RunDLLW")
	dAD_AutoScroll = doGetProcAddress(libshell32, "DAD_AutoScroll")
	dAD_DragEnterEx = doGetProcAddress(libshell32, "DAD_DragEnterEx")
	dAD_DragLeave = doGetProcAddress(libshell32, "DAD_DragLeave")
	dAD_DragMove = doGetProcAddress(libshell32, "DAD_DragMove")
	dAD_SetDragImage = doGetProcAddress(libshell32, "DAD_SetDragImage")
	dAD_ShowDragImage = doGetProcAddress(libshell32, "DAD_ShowDragImage")
	doEnvironmentSubst = doGetProcAddress(libshell32, "DoEnvironmentSubstW")
	dragAcceptFiles = doGetProcAddress(libshell32, "DragAcceptFiles")
	dragFinish = doGetProcAddress(libshell32, "DragFinish")
	dragQueryFile = doGetProcAddress(libshell32, "DragQueryFileW")
	dragQueryPoint = doGetProcAddress(libshell32, "DragQueryPoint")
	driveType = doGetProcAddress(libshell32, "DriveType")
	duplicateIcon = doGetProcAddress(libshell32, "DuplicateIcon")
	extractAssociatedIconEx = doGetProcAddress(libshell32, "ExtractAssociatedIconExW")
	extractAssociatedIcon = doGetProcAddress(libshell32, "ExtractAssociatedIconW")
	extractIconEx = doGetProcAddress(libshell32, "ExtractIconExW")
	extractIcon = doGetProcAddress(libshell32, "ExtractIconW")
	extractVersionResource16W = doGetProcAddress(libshell32, "ExtractVersionResource16W")
	findExecutable = doGetProcAddress(libshell32, "FindExecutableW")
	freeIconList = doGetProcAddress(libshell32, "FreeIconList")
	getCurrentProcessExplicitAppUserModelID = doGetProcAddress(libshell32, "GetCurrentProcessExplicitAppUserModelID")
	iLAppendID = doGetProcAddress(libshell32, "ILAppendID")
	iLClone = doGetProcAddress(libshell32, "ILClone")
	iLCloneFirst = doGetProcAddress(libshell32, "ILCloneFirst")
	iLCombine = doGetProcAddress(libshell32, "ILCombine")
	iLCreateFromPath = doGetProcAddress(libshell32, "ILCreateFromPathW")
	iLFindChild = doGetProcAddress(libshell32, "ILFindChild")
	iLFindLastID = doGetProcAddress(libshell32, "ILFindLastID")
	iLFree = doGetProcAddress(libshell32, "ILFree")
	iLGetNext = doGetProcAddress(libshell32, "ILGetNext")
	iLGetSize = doGetProcAddress(libshell32, "ILGetSize")
	iLIsEqual = doGetProcAddress(libshell32, "ILIsEqual")
	iLIsParent = doGetProcAddress(libshell32, "ILIsParent")
	iLLoadFromStream = doGetProcAddress(libshell32, "ILLoadFromStream")
	iLRemoveLastID = doGetProcAddress(libshell32, "ILRemoveLastID")
	iLSaveToStream = doGetProcAddress(libshell32, "ILSaveToStream")
	initNetworkAddressControl = doGetProcAddress(libshell32, "InitNetworkAddressControl")
	isLFNDrive = doGetProcAddress(libshell32, "IsLFNDriveW")
	isNetDrive = doGetProcAddress(libshell32, "IsNetDrive")
	isUserAnAdmin = doGetProcAddress(libshell32, "IsUserAnAdmin")
	openAs_RunDLL = doGetProcAddress(libshell32, "OpenAs_RunDLLW")
	pathCleanupSpec = doGetProcAddress(libshell32, "PathCleanupSpec")
	pathYetAnotherMakeUniqueName = doGetProcAddress(libshell32, "PathYetAnotherMakeUniqueName")
	pickIconDlg = doGetProcAddress(libshell32, "PickIconDlg")
	readCabinetState = doGetProcAddress(libshell32, "ReadCabinetState")
	realDriveType = doGetProcAddress(libshell32, "RealDriveType")
	regenerateUserEnvironment = doGetProcAddress(libshell32, "RegenerateUserEnvironment")
	restartDialog = doGetProcAddress(libshell32, "RestartDialog")
	restartDialogEx = doGetProcAddress(libshell32, "RestartDialogEx")
	sHAddToRecentDocs = doGetProcAddress(libshell32, "SHAddToRecentDocs")
	sHAlloc = doGetProcAddress(libshell32, "SHAlloc")
	sHAppBarMessage = doGetProcAddress(libshell32, "SHAppBarMessage")
	sHAssocEnumHandlers = doGetProcAddress(libshell32, "SHAssocEnumHandlers")
	sHBindToParent = doGetProcAddress(libshell32, "SHBindToParent")
	sHBrowseForFolder = doGetProcAddress(libshell32, "SHBrowseForFolderW")
	sHChangeNotification_Lock = doGetProcAddress(libshell32, "SHChangeNotification_Lock")
	sHChangeNotification_Unlock = doGetProcAddress(libshell32, "SHChangeNotification_Unlock")
	sHChangeNotify = doGetProcAddress(libshell32, "SHChangeNotify")
	sHChangeNotifyDeregister = doGetProcAddress(libshell32, "SHChangeNotifyDeregister")
	sHChangeNotifyRegister = doGetProcAddress(libshell32, "SHChangeNotifyRegister")
	sHCloneSpecialIDList = doGetProcAddress(libshell32, "SHCloneSpecialIDList")
	sHCoCreateInstance = doGetProcAddress(libshell32, "SHCoCreateInstance")
	sHCreateDefaultContextMenu = doGetProcAddress(libshell32, "SHCreateDefaultContextMenu")
	sHCreateDirectory = doGetProcAddress(libshell32, "SHCreateDirectory")
	sHCreateDirectoryEx = doGetProcAddress(libshell32, "SHCreateDirectoryExW")
	sHCreateFileExtractIconW = doGetProcAddress(libshell32, "SHCreateFileExtractIconW")
	sHCreateItemFromIDList = doGetProcAddress(libshell32, "SHCreateItemFromIDList")
	sHCreateItemFromParsingName = doGetProcAddress(libshell32, "SHCreateItemFromParsingName")
	sHCreateQueryCancelAutoPlayMoniker = doGetProcAddress(libshell32, "SHCreateQueryCancelAutoPlayMoniker")
	sHCreateShellFolderView = doGetProcAddress(libshell32, "SHCreateShellFolderView")
	sHCreateShellFolderViewEx = doGetProcAddress(libshell32, "SHCreateShellFolderViewEx")
	sHCreateShellItem = doGetProcAddress(libshell32, "SHCreateShellItem")
	sHCreateShellItemArray = doGetProcAddress(libshell32, "SHCreateShellItemArray")
	sHCreateShellItemArrayFromDataObject = doGetProcAddress(libshell32, "SHCreateShellItemArrayFromDataObject")
	sHCreateShellItemArrayFromIDLists = doGetProcAddress(libshell32, "SHCreateShellItemArrayFromIDLists")
	sHCreateShellItemArrayFromShellItem = doGetProcAddress(libshell32, "SHCreateShellItemArrayFromShellItem")
	sHCreateStdEnumFmtEtc = doGetProcAddress(libshell32, "SHCreateStdEnumFmtEtc")
	sHDefExtractIcon = doGetProcAddress(libshell32, "SHDefExtractIconW")
	sHDoDragDrop = doGetProcAddress(libshell32, "SHDoDragDrop")
	sHEmptyRecycleBin = doGetProcAddress(libshell32, "SHEmptyRecycleBinW")
	sHEnumerateUnreadMailAccountsW = doGetProcAddress(libshell32, "SHEnumerateUnreadMailAccountsW")
	sHFileOperation = doGetProcAddress(libshell32, "SHFileOperationW")
	sHFindFiles = doGetProcAddress(libshell32, "SHFindFiles")
	sHFind_InitMenuPopup = doGetProcAddress(libshell32, "SHFind_InitMenuPopup")
	sHFlushClipboard = doGetProcAddress(libshell32, "SHFlushClipboard")
	sHFlushSFCache = doGetProcAddress(libshell32, "SHFlushSFCache")
	sHFormatDrive = doGetProcAddress(libshell32, "SHFormatDrive")
	sHFree = doGetProcAddress(libshell32, "SHFree")
	sHFreeNameMappings = doGetProcAddress(libshell32, "SHFreeNameMappings")
	sHGetDataFromIDList = doGetProcAddress(libshell32, "SHGetDataFromIDListW")
	sHGetDesktopFolder = doGetProcAddress(libshell32, "SHGetDesktopFolder")
	sHGetFileInfo = doGetProcAddress(libshell32, "SHGetFileInfoW")
	sHGetFolderLocation = doGetProcAddress(libshell32, "SHGetFolderLocation")
	sHGetFolderPathAndSubDir = doGetProcAddress(libshell32, "SHGetFolderPathAndSubDirW")
	sHGetFolderPathEx = doGetProcAddress(libshell32, "SHGetFolderPathEx")
	sHGetFolderPath = doGetProcAddress(libshell32, "SHGetFolderPathW")
	sHGetIDListFromObject = doGetProcAddress(libshell32, "SHGetIDListFromObject")
	sHGetIconOverlayIndex = doGetProcAddress(libshell32, "SHGetIconOverlayIndexW")
	sHGetImageList = doGetProcAddress(libshell32, "SHGetImageList")
	sHGetInstanceExplorer = doGetProcAddress(libshell32, "SHGetInstanceExplorer")
	sHGetItemFromDataObject = doGetProcAddress(libshell32, "SHGetItemFromDataObject")
	sHGetItemFromObject = doGetProcAddress(libshell32, "SHGetItemFromObject")
	sHGetKnownFolderIDList = doGetProcAddress(libshell32, "SHGetKnownFolderIDList")
	sHGetKnownFolderItem = doGetProcAddress(libshell32, "SHGetKnownFolderItem")
	sHGetKnownFolderPath = doGetProcAddress(libshell32, "SHGetKnownFolderPath")
	sHGetLocalizedName = doGetProcAddress(libshell32, "SHGetLocalizedName")
	sHGetMalloc = doGetProcAddress(libshell32, "SHGetMalloc")
	sHGetNameFromIDList = doGetProcAddress(libshell32, "SHGetNameFromIDList")
	sHGetNewLinkInfo = doGetProcAddress(libshell32, "SHGetNewLinkInfoW")
	sHGetPathFromIDList = doGetProcAddress(libshell32, "SHGetPathFromIDListW")
	sHGetPropertyStoreForWindow = doGetProcAddress(libshell32, "SHGetPropertyStoreForWindow")
	sHGetPropertyStoreFromParsingName = doGetProcAddress(libshell32, "SHGetPropertyStoreFromParsingName")
	sHGetRealIDL = doGetProcAddress(libshell32, "SHGetRealIDL")
	sHGetSetSettings = doGetProcAddress(libshell32, "SHGetSetSettings")
	sHGetSettings = doGetProcAddress(libshell32, "SHGetSettings")
	sHGetSpecialFolderLocation = doGetProcAddress(libshell32, "SHGetSpecialFolderLocation")
	sHGetSpecialFolderPath = doGetProcAddress(libshell32, "SHGetSpecialFolderPathW")
	sHGetStockIconInfo = doGetProcAddress(libshell32, "SHGetStockIconInfo")
	sHHandleUpdateImage = doGetProcAddress(libshell32, "SHHandleUpdateImage")
	sHHelpShortcuts_RunDLL = doGetProcAddress(libshell32, "SHHelpShortcuts_RunDLLW")
	sHIsFileAvailableOffline = doGetProcAddress(libshell32, "SHIsFileAvailableOffline")
	sHLimitInputEdit = doGetProcAddress(libshell32, "SHLimitInputEdit")
	sHLoadInProc = doGetProcAddress(libshell32, "SHLoadInProc")
	sHLoadNonloadedIconOverlayIdentifiers = doGetProcAddress(libshell32, "SHLoadNonloadedIconOverlayIdentifiers")
	sHMapIDListToImageListIndexAsync = doGetProcAddress(libshell32, "SHMapIDListToImageListIndexAsync")
	sHMapPIDLToSystemImageListIndex = doGetProcAddress(libshell32, "SHMapPIDLToSystemImageListIndex")
	sHObjectProperties = doGetProcAddress(libshell32, "SHObjectProperties")
	sHOpenFolderAndSelectItems = doGetProcAddress(libshell32, "SHOpenFolderAndSelectItems")
	sHParseDisplayName = doGetProcAddress(libshell32, "SHParseDisplayName")
	sHPathPrepareForWrite = doGetProcAddress(libshell32, "SHPathPrepareForWriteW")
	sHPropStgCreate = doGetProcAddress(libshell32, "SHPropStgCreate")
	sHPropStgReadMultiple = doGetProcAddress(libshell32, "SHPropStgReadMultiple")
	sHPropStgWriteMultiple = doGetProcAddress(libshell32, "SHPropStgWriteMultiple")
	sHQueryRecycleBin = doGetProcAddress(libshell32, "SHQueryRecycleBinW")
	sHQueryUserNotificationState = doGetProcAddress(libshell32, "SHQueryUserNotificationState")
	sHRemoveLocalizedName = doGetProcAddress(libshell32, "SHRemoveLocalizedName")
	sHRestricted = doGetProcAddress(libshell32, "SHRestricted")
	sHRunControlPanel = doGetProcAddress(libshell32, "SHRunControlPanel")
	sHSetInstanceExplorer = doGetProcAddress(libshell32, "SHSetInstanceExplorer")
	sHSetLocalizedName = doGetProcAddress(libshell32, "SHSetLocalizedName")
	sHSetUnreadMailCountW = doGetProcAddress(libshell32, "SHSetUnreadMailCountW")
	sHShellFolderView_Message = doGetProcAddress(libshell32, "SHShellFolderView_Message")
	sHUpdateImage = doGetProcAddress(libshell32, "SHUpdateImageW")
	sHUpdateRecycleBinIcon = doGetProcAddress(libshell32, "SHUpdateRecycleBinIcon")
	sHValidateUNC = doGetProcAddress(libshell32, "SHValidateUNC")
	setCurrentProcessExplicitAppUserModelID = doGetProcAddress(libshell32, "SetCurrentProcessExplicitAppUserModelID")
	sheChangeDir = doGetProcAddress(libshell32, "SheChangeDirW")
	sheGetDir = doGetProcAddress(libshell32, "SheGetDirW")
	shellAbout = doGetProcAddress(libshell32, "ShellAboutW")
	shellExecute = doGetProcAddress(libshell32, "ShellExecuteW")
	shell_GetImageLists = doGetProcAddress(libshell32, "Shell_GetImageLists")
	shell_MergeMenus = doGetProcAddress(libshell32, "Shell_MergeMenus")
	shell_NotifyIcon = doGetProcAddress(libshell32, "Shell_NotifyIconW")
	signalFileOpen = doGetProcAddress(libshell32, "SignalFileOpen")
	wOWShellExecute = doGetProcAddress(libshell32, "WOWShellExecute")
	writeCabinetState = doGetProcAddress(libshell32, "WriteCabinetState")
}

func SHAddFromPropSheetExtArray(hpsxa HPSXA, lpfnAddPage LPFNADDPROPSHEETPAGE, lParam LPARAM) UINT {
	lpfnAddPageCallback := syscall.NewCallback(func(unnamed0RawArg HPROPSHEETPAGE, unnamed1RawArg LPARAM) uintptr {
		ret := lpfnAddPage(unnamed0RawArg, unnamed1RawArg)
		return uintptr(ret)
	})
	ret1 := syscall3(sHAddFromPropSheetExtArray, 3,
		uintptr(hpsxa),
		lpfnAddPageCallback,
		uintptr(lParam))
	return UINT(ret1)
}

func SHCreatePropSheetExtArray(hKey HKEY, pszSubKey string, max_iface UINT) HPSXA {
	pszSubKeyStr := unicode16FromString(pszSubKey)
	ret1 := syscall3(sHCreatePropSheetExtArray, 3,
		uintptr(hKey),
		uintptr(unsafe.Pointer(&pszSubKeyStr[0])),
		uintptr(max_iface))
	return HPSXA(ret1)
}

func SHDestroyPropSheetExtArray(hpsxa HPSXA) {
	syscall3(sHDestroyPropSheetExtArray, 1,
		uintptr(hpsxa),
		0,
		0)
}

func SHReplaceFromPropSheetExtArray(hpsxa HPSXA, uPageID UINT, lpfnReplaceWith LPFNADDPROPSHEETPAGE, lParam LPARAM) UINT {
	lpfnReplaceWithCallback := syscall.NewCallback(func(unnamed0RawArg HPROPSHEETPAGE, unnamed1RawArg LPARAM) uintptr {
		ret := lpfnReplaceWith(unnamed0RawArg, unnamed1RawArg)
		return uintptr(ret)
	})
	ret1 := syscall6(sHReplaceFromPropSheetExtArray, 4,
		uintptr(hpsxa),
		uintptr(uPageID),
		lpfnReplaceWithCallback,
		uintptr(lParam),
		0,
		0)
	return UINT(ret1)
}

func CDefFolderMenu_Create2(pidlFolder /*const*/ LPCITEMIDLIST, hwnd HWND, cidl UINT, apidl *LPCITEMIDLIST, psf *IShellFolder, lpfn LPFNDFMCALLBACK, nKeys UINT, ahkeys /*const*/ *HKEY, ppcm **IContextMenu) HRESULT {
	lpfnCallback := syscall.NewCallback(func(psfRawArg *IShellFolder, hwndRawArg HWND, pdtobjRawArg *IDataObject, uMsgRawArg UINT, wParamRawArg WPARAM, lParamRawArg LPARAM) uintptr {
		ret := lpfn(psfRawArg, hwndRawArg, pdtobjRawArg, uMsgRawArg, wParamRawArg, lParamRawArg)
		return uintptr(ret)
	})
	ret1 := syscall9(cDefFolderMenu_Create2, 9,
		uintptr(unsafe.Pointer(pidlFolder)),
		uintptr(hwnd),
		uintptr(cidl),
		uintptr(unsafe.Pointer(apidl)),
		uintptr(unsafe.Pointer(psf)),
		lpfnCallback,
		uintptr(nKeys),
		uintptr(unsafe.Pointer(ahkeys)),
		uintptr(unsafe.Pointer(ppcm)))
	return HRESULT(ret1)
}

func CIDLData_CreateFromIDArray(pidlFolder /*const*/ LPCITEMIDLIST, cpidlFiles DWORD, lppidlFiles *LPCITEMIDLIST, ppdataObject *LPDATAOBJECT) HRESULT {
	ret1 := syscall6(cIDLData_CreateFromIDArray, 4,
		uintptr(unsafe.Pointer(pidlFolder)),
		uintptr(cpidlFiles),
		uintptr(unsafe.Pointer(lppidlFiles)),
		uintptr(unsafe.Pointer(ppdataObject)),
		0,
		0)
	return HRESULT(ret1)
}

func CallCPLEntry16(hMod HMODULE, pFunc FARPROC, dw3 DWORD, dw4 DWORD, dw5 DWORD, dw6 DWORD) DWORD {
	pFuncCallback := syscall.NewCallback(func() uintptr {
		ret := pFunc()
		return uintptr(unsafe.Pointer(ret))
	})
	ret1 := syscall6(callCPLEntry16, 6,
		uintptr(hMod),
		pFuncCallback,
		uintptr(dw3),
		uintptr(dw4),
		uintptr(dw5),
		uintptr(dw6))
	return DWORD(ret1)
}

func CheckEscapes(string LPWSTR, aLen DWORD) DWORD {
	ret1 := syscall3(checkEscapes, 2,
		uintptr(unsafe.Pointer(string)),
		uintptr(aLen),
		0)
	return DWORD(ret1)
}

func Control_FillCache_RunDLL(hWnd HWND, hModule HANDLE, w DWORD, x DWORD) HRESULT {
	ret1 := syscall6(control_FillCache_RunDLL, 4,
		uintptr(hWnd),
		uintptr(hModule),
		uintptr(w),
		uintptr(x),
		0,
		0)
	return HRESULT(ret1)
}

func Control_RunDLL(hWnd HWND, hInst HINSTANCE, cmd string, nCmdShow DWORD) {
	cmdStr := unicode16FromString(cmd)
	syscall6(control_RunDLL, 4,
		uintptr(hWnd),
		uintptr(hInst),
		uintptr(unsafe.Pointer(&cmdStr[0])),
		uintptr(nCmdShow),
		0,
		0)
}

func DAD_AutoScroll(hwnd HWND, samples *AUTO_SCROLL_DATA, pt *POINT) bool {
	ret1 := syscall3(dAD_AutoScroll, 3,
		uintptr(hwnd),
		uintptr(unsafe.Pointer(samples)),
		uintptr(unsafe.Pointer(pt)))
	return ret1 != 0
}

func DAD_DragEnterEx(hwnd HWND, p POINT) bool {
	ret1 := syscall3(dAD_DragEnterEx, 3,
		uintptr(hwnd),
		uintptr(p.X),
		uintptr(p.Y))
	return ret1 != 0
}

func DAD_DragLeave() bool {
	ret1 := syscall3(dAD_DragLeave, 0,
		0,
		0,
		0)
	return ret1 != 0
}

func DAD_DragMove(p POINT) bool {
	ret1 := syscall3(dAD_DragMove, 2,
		uintptr(p.X),
		uintptr(p.Y),
		0)
	return ret1 != 0
}

func DAD_SetDragImage(himlTrack HIMAGELIST, lppt *POINT) bool {
	ret1 := syscall3(dAD_SetDragImage, 2,
		uintptr(himlTrack),
		uintptr(unsafe.Pointer(lppt)),
		0)
	return ret1 != 0
}

func DAD_ShowDragImage(bShow bool) bool {
	ret1 := syscall3(dAD_ShowDragImage, 1,
		getUintptrFromBool(bShow),
		0,
		0)
	return ret1 != 0
}

func DoEnvironmentSubst(pszString LPWSTR, cchString UINT) DWORD {
	ret1 := syscall3(doEnvironmentSubst, 2,
		uintptr(unsafe.Pointer(pszString)),
		uintptr(cchString),
		0)
	return DWORD(ret1)
}

func DragAcceptFiles(hWnd HWND, b bool) {
	syscall3(dragAcceptFiles, 2,
		uintptr(hWnd),
		getUintptrFromBool(b),
		0)
}

func DragFinish(h HDROP) {
	syscall3(dragFinish, 1,
		uintptr(h),
		0,
		0)
}

func DragQueryFile(hDrop HDROP, lFile UINT, lpszwFile LPWSTR, lLength UINT) UINT {
	ret1 := syscall6(dragQueryFile, 4,
		uintptr(hDrop),
		uintptr(lFile),
		uintptr(unsafe.Pointer(lpszwFile)),
		uintptr(lLength),
		0,
		0)
	return UINT(ret1)
}

func DragQueryPoint(hDrop HDROP, p *POINT) bool {
	ret1 := syscall3(dragQueryPoint, 2,
		uintptr(hDrop),
		uintptr(unsafe.Pointer(p)),
		0)
	return ret1 != 0
}

func DriveType(u int32) int32 {
	ret1 := syscall3(driveType, 1,
		uintptr(u),
		0,
		0)
	return int32(ret1)
}

func DuplicateIcon(hInstance HINSTANCE, hIcon HICON) HICON {
	ret1 := syscall3(duplicateIcon, 2,
		uintptr(hInstance),
		uintptr(hIcon),
		0)
	return HICON(ret1)
}

func ExtractAssociatedIconEx(hInst HINSTANCE, lpIconPath LPWSTR, lpiIconIdx *uint16, lpiIconId *uint16) HICON {
	ret1 := syscall6(extractAssociatedIconEx, 4,
		uintptr(hInst),
		uintptr(unsafe.Pointer(lpIconPath)),
		uintptr(unsafe.Pointer(lpiIconIdx)),
		uintptr(unsafe.Pointer(lpiIconId)),
		0,
		0)
	return HICON(ret1)
}

func ExtractAssociatedIcon(hInst HINSTANCE, lpIconPath LPWSTR, lpiIcon *uint16) HICON {
	ret1 := syscall3(extractAssociatedIcon, 3,
		uintptr(hInst),
		uintptr(unsafe.Pointer(lpIconPath)),
		uintptr(unsafe.Pointer(lpiIcon)))
	return HICON(ret1)
}

func ExtractIconEx(lpszFile string, nIconIndex INT, phiconLarge *HICON, phiconSmall *HICON, nIcons UINT) UINT {
	lpszFileStr := unicode16FromString(lpszFile)
	ret1 := syscall6(extractIconEx, 5,
		uintptr(unsafe.Pointer(&lpszFileStr[0])),
		uintptr(nIconIndex),
		uintptr(unsafe.Pointer(phiconLarge)),
		uintptr(unsafe.Pointer(phiconSmall)),
		uintptr(nIcons),
		0)
	return UINT(ret1)
}

func ExtractIcon(hInstance HINSTANCE, lpszFile string, nIconIndex UINT) HICON {
	lpszFileStr := unicode16FromString(lpszFile)
	ret1 := syscall3(extractIcon, 3,
		uintptr(hInstance),
		uintptr(unsafe.Pointer(&lpszFileStr[0])),
		uintptr(nIconIndex))
	return HICON(ret1)
}

func ExtractVersionResource16W(s LPWSTR, d DWORD) bool {
	ret1 := syscall3(extractVersionResource16W, 2,
		uintptr(unsafe.Pointer(s)),
		uintptr(d),
		0)
	return ret1 != 0
}

func FindExecutable(lpFile string, lpDirectory string, lpResult LPWSTR) HINSTANCE {
	lpFileStr := unicode16FromString(lpFile)
	lpDirectoryStr := unicode16FromString(lpDirectory)
	ret1 := syscall3(findExecutable, 3,
		uintptr(unsafe.Pointer(&lpFileStr[0])),
		uintptr(unsafe.Pointer(&lpDirectoryStr[0])),
		uintptr(unsafe.Pointer(lpResult)))
	return HINSTANCE(ret1)
}

func FreeIconList(dw DWORD) {
	syscall3(freeIconList, 1,
		uintptr(dw),
		0,
		0)
}

func GetCurrentProcessExplicitAppUserModelID(appid *PWSTR) HRESULT {
	ret1 := syscall3(getCurrentProcessExplicitAppUserModelID, 1,
		uintptr(unsafe.Pointer(appid)),
		0,
		0)
	return HRESULT(ret1)
}

func ILAppendID(pidl LPITEMIDLIST, item /*const*/ *SHITEMID, bEnd bool) LPITEMIDLIST {
	ret1 := syscall3(iLAppendID, 3,
		uintptr(unsafe.Pointer(pidl)),
		uintptr(unsafe.Pointer(item)),
		getUintptrFromBool(bEnd))
	return (LPITEMIDLIST)(unsafe.Pointer(ret1))
}

func ILClone(pidl /*const*/ LPCITEMIDLIST) LPITEMIDLIST {
	ret1 := syscall3(iLClone, 1,
		uintptr(unsafe.Pointer(pidl)),
		0,
		0)
	return (LPITEMIDLIST)(unsafe.Pointer(ret1))
}

func ILCloneFirst(pidl /*const*/ LPCITEMIDLIST) LPITEMIDLIST {
	ret1 := syscall3(iLCloneFirst, 1,
		uintptr(unsafe.Pointer(pidl)),
		0,
		0)
	return (LPITEMIDLIST)(unsafe.Pointer(ret1))
}

func ILCombine(pidl1 /*const*/ LPCITEMIDLIST, pidl2 /*const*/ LPCITEMIDLIST) LPITEMIDLIST {
	ret1 := syscall3(iLCombine, 2,
		uintptr(unsafe.Pointer(pidl1)),
		uintptr(unsafe.Pointer(pidl2)),
		0)
	return (LPITEMIDLIST)(unsafe.Pointer(ret1))
}

func ILCreateFromPath(path string) LPITEMIDLIST {
	pathStr := unicode16FromString(path)
	ret1 := syscall3(iLCreateFromPath, 1,
		uintptr(unsafe.Pointer(&pathStr[0])),
		0,
		0)
	return (LPITEMIDLIST)(unsafe.Pointer(ret1))
}

func ILFindChild(pidl1 /*const*/ LPCITEMIDLIST, pidl2 /*const*/ LPCITEMIDLIST) LPITEMIDLIST {
	ret1 := syscall3(iLFindChild, 2,
		uintptr(unsafe.Pointer(pidl1)),
		uintptr(unsafe.Pointer(pidl2)),
		0)
	return (LPITEMIDLIST)(unsafe.Pointer(ret1))
}

func ILFindLastID(pidl /*const*/ LPCITEMIDLIST) LPITEMIDLIST {
	ret1 := syscall3(iLFindLastID, 1,
		uintptr(unsafe.Pointer(pidl)),
		0,
		0)
	return (LPITEMIDLIST)(unsafe.Pointer(ret1))
}

func ILFree(pidl LPITEMIDLIST) {
	syscall3(iLFree, 1,
		uintptr(unsafe.Pointer(pidl)),
		0,
		0)
}

func ILGetNext(pidl /*const*/ LPCITEMIDLIST) LPITEMIDLIST {
	ret1 := syscall3(iLGetNext, 1,
		uintptr(unsafe.Pointer(pidl)),
		0,
		0)
	return (LPITEMIDLIST)(unsafe.Pointer(ret1))
}

func ILGetSize(pidl /*const*/ LPCITEMIDLIST) UINT {
	ret1 := syscall3(iLGetSize, 1,
		uintptr(unsafe.Pointer(pidl)),
		0,
		0)
	return UINT(ret1)
}

func ILIsEqual(pidl1 /*const*/ LPCITEMIDLIST, pidl2 /*const*/ LPCITEMIDLIST) bool {
	ret1 := syscall3(iLIsEqual, 2,
		uintptr(unsafe.Pointer(pidl1)),
		uintptr(unsafe.Pointer(pidl2)),
		0)
	return ret1 != 0
}

func ILIsParent(pidlParent /*const*/ LPCITEMIDLIST, pidlChild /*const*/ LPCITEMIDLIST, bImmediate bool) bool {
	ret1 := syscall3(iLIsParent, 3,
		uintptr(unsafe.Pointer(pidlParent)),
		uintptr(unsafe.Pointer(pidlChild)),
		getUintptrFromBool(bImmediate))
	return ret1 != 0
}

func ILLoadFromStream(pStream *IStream, ppPidl *LPITEMIDLIST) HRESULT {
	ret1 := syscall3(iLLoadFromStream, 2,
		uintptr(unsafe.Pointer(pStream)),
		uintptr(unsafe.Pointer(ppPidl)),
		0)
	return HRESULT(ret1)
}

func ILRemoveLastID(pidl LPITEMIDLIST) bool {
	ret1 := syscall3(iLRemoveLastID, 1,
		uintptr(unsafe.Pointer(pidl)),
		0,
		0)
	return ret1 != 0
}

func ILSaveToStream(pStream *IStream, pPidl /*const*/ LPCITEMIDLIST) HRESULT {
	ret1 := syscall3(iLSaveToStream, 2,
		uintptr(unsafe.Pointer(pStream)),
		uintptr(unsafe.Pointer(pPidl)),
		0)
	return HRESULT(ret1)
}

func InitNetworkAddressControl() bool {
	ret1 := syscall3(initNetworkAddressControl, 0,
		0,
		0,
		0)
	return ret1 != 0
}

func IsLFNDrive(lpszPath string) bool {
	lpszPathStr := unicode16FromString(lpszPath)
	ret1 := syscall3(isLFNDrive, 1,
		uintptr(unsafe.Pointer(&lpszPathStr[0])),
		0,
		0)
	return ret1 != 0
}

func IsNetDrive(drive int32) int32 {
	ret1 := syscall3(isNetDrive, 1,
		uintptr(drive),
		0,
		0)
	return int32(ret1)
}

func IsUserAnAdmin() bool {
	ret1 := syscall3(isUserAnAdmin, 0,
		0,
		0,
		0)
	return ret1 != 0
}

func OpenAs_RunDLL(hwnd HWND, hinst HINSTANCE, cmdline string, cmdshow int32) {
	cmdlineStr := unicode16FromString(cmdline)
	syscall6(openAs_RunDLL, 4,
		uintptr(hwnd),
		uintptr(hinst),
		uintptr(unsafe.Pointer(&cmdlineStr[0])),
		uintptr(cmdshow),
		0,
		0)
}

func PathCleanupSpec(lpszPathW string, lpszFileW LPWSTR) int32 {
	lpszPathWStr := unicode16FromString(lpszPathW)
	ret1 := syscall3(pathCleanupSpec, 2,
		uintptr(unsafe.Pointer(&lpszPathWStr[0])),
		uintptr(unsafe.Pointer(lpszFileW)),
		0)
	return int32(ret1)
}

func PathYetAnotherMakeUniqueName(buffer LPWSTR, path string, shortname string, longname string) bool {
	pathStr := unicode16FromString(path)
	shortnameStr := unicode16FromString(shortname)
	longnameStr := unicode16FromString(longname)
	ret1 := syscall6(pathYetAnotherMakeUniqueName, 4,
		uintptr(unsafe.Pointer(buffer)),
		uintptr(unsafe.Pointer(&pathStr[0])),
		uintptr(unsafe.Pointer(&shortnameStr[0])),
		uintptr(unsafe.Pointer(&longnameStr[0])),
		0,
		0)
	return ret1 != 0
}

func PickIconDlg(hwndOwner HWND, lpstrFile LPSTR, nMaxFile DWORD, lpdwIconIndex *uint32) INT {
	ret1 := syscall6(pickIconDlg, 4,
		uintptr(hwndOwner),
		uintptr(unsafe.Pointer(lpstrFile)),
		uintptr(nMaxFile),
		uintptr(unsafe.Pointer(lpdwIconIndex)),
		0,
		0)
	return INT(ret1)
}

func ReadCabinetState(cs *CABINETSTATE, length int32) bool {
	ret1 := syscall3(readCabinetState, 2,
		uintptr(unsafe.Pointer(cs)),
		uintptr(length),
		0)
	return ret1 != 0
}

func RealDriveType(drive int32, bQueryNet bool) int32 {
	ret1 := syscall3(realDriveType, 2,
		uintptr(drive),
		getUintptrFromBool(bQueryNet),
		0)
	return int32(ret1)
}

func RegenerateUserEnvironment(wunknown *WCHAR, bunknown bool) bool {
	ret1 := syscall3(regenerateUserEnvironment, 2,
		uintptr(unsafe.Pointer(wunknown)),
		getUintptrFromBool(bunknown),
		0)
	return ret1 != 0
}

func RestartDialog(hWndOwner HWND, lpstrReason string, uFlags DWORD) int32 {
	lpstrReasonStr := unicode16FromString(lpstrReason)
	ret1 := syscall3(restartDialog, 3,
		uintptr(hWndOwner),
		uintptr(unsafe.Pointer(&lpstrReasonStr[0])),
		uintptr(uFlags))
	return int32(ret1)
}

func RestartDialogEx(hWndOwner HWND, lpwstrReason string, uFlags DWORD, uReason DWORD) int32 {
	lpwstrReasonStr := unicode16FromString(lpwstrReason)
	ret1 := syscall6(restartDialogEx, 4,
		uintptr(hWndOwner),
		uintptr(unsafe.Pointer(&lpwstrReasonStr[0])),
		uintptr(uFlags),
		uintptr(uReason),
		0,
		0)
	return int32(ret1)
}

func SHAddToRecentDocs(uFlags UINT, pv /*const*/ uintptr) {
	syscall3(sHAddToRecentDocs, 2,
		uintptr(uFlags),
		pv,
		0)
}

func SHAlloc(aLen DWORD) LPVOID {
	ret1 := syscall3(sHAlloc, 1,
		uintptr(aLen),
		0,
		0)
	return (LPVOID)(unsafe.Pointer(ret1))
}

func SHAppBarMessage(msg DWORD, data PAPPBARDATA) *uint32 {
	ret1 := syscall3(sHAppBarMessage, 2,
		uintptr(msg),
		uintptr(unsafe.Pointer(data)),
		0)
	return (*uint32)(unsafe.Pointer(ret1))
}

func SHAssocEnumHandlers(extra /*const*/ *WCHAR, filter ASSOC_FILTER, enumhandlers **IEnumAssocHandlers) HRESULT {
	ret1 := syscall3(sHAssocEnumHandlers, 3,
		uintptr(unsafe.Pointer(extra)),
		uintptr(filter),
		uintptr(unsafe.Pointer(enumhandlers)))
	return HRESULT(ret1)
}

func SHBindToParent(pidl /*const*/ LPCITEMIDLIST, riid REFIID, ppv *LPVOID, ppidlLast *LPCITEMIDLIST) HRESULT {
	ret1 := syscall6(sHBindToParent, 4,
		uintptr(unsafe.Pointer(pidl)),
		uintptr(unsafe.Pointer(riid)),
		uintptr(unsafe.Pointer(ppv)),
		uintptr(unsafe.Pointer(ppidlLast)),
		0,
		0)
	return HRESULT(ret1)
}

func SHBrowseForFolder(lpbi LPBROWSEINFO) LPITEMIDLIST {
	ret1 := syscall3(sHBrowseForFolder, 1,
		uintptr(unsafe.Pointer(lpbi)),
		0,
		0)
	return (LPITEMIDLIST)(unsafe.Pointer(ret1))
}

func SHChangeNotification_Lock(hChange HANDLE, dwProcessId DWORD, lppidls **LPITEMIDLIST, lpwEventId *LONG) HANDLE {
	ret1 := syscall6(sHChangeNotification_Lock, 4,
		uintptr(hChange),
		uintptr(dwProcessId),
		uintptr(unsafe.Pointer(lppidls)),
		uintptr(unsafe.Pointer(lpwEventId)),
		0,
		0)
	return HANDLE(ret1)
}

func SHChangeNotification_Unlock(hLock HANDLE) bool {
	ret1 := syscall3(sHChangeNotification_Unlock, 1,
		uintptr(hLock),
		0,
		0)
	return ret1 != 0
}

func SHChangeNotify(wEventId LONG, uFlags UINT, dwItem1 /*const*/ uintptr, dwItem2 /*const*/ uintptr) {
	syscall6(sHChangeNotify, 4,
		uintptr(wEventId),
		uintptr(uFlags),
		dwItem1,
		dwItem2,
		0,
		0)
}

func SHChangeNotifyDeregister(hNotify ULONG) bool {
	ret1 := syscall3(sHChangeNotifyDeregister, 1,
		uintptr(hNotify),
		0,
		0)
	return ret1 != 0
}

func SHChangeNotifyRegister(hwnd HWND, fSources int32, wEventMask LONG, uMsg UINT, cItems int32, lpItems *SHChangeNotifyEntry) ULONG {
	ret1 := syscall6(sHChangeNotifyRegister, 6,
		uintptr(hwnd),
		uintptr(fSources),
		uintptr(wEventMask),
		uintptr(uMsg),
		uintptr(cItems),
		uintptr(unsafe.Pointer(lpItems)))
	return ULONG(ret1)
}

func SHCloneSpecialIDList(hwndOwner HWND, nFolder DWORD, fCreate bool) LPITEMIDLIST {
	ret1 := syscall3(sHCloneSpecialIDList, 3,
		uintptr(hwndOwner),
		uintptr(nFolder),
		getUintptrFromBool(fCreate))
	return (LPITEMIDLIST)(unsafe.Pointer(ret1))
}

func SHCoCreateInstance(aclsid string, clsid /*const*/ *CLSID, pUnkOuter LPUNKNOWN, refiid REFIID, ppv *LPVOID) HRESULT {
	aclsidStr := unicode16FromString(aclsid)
	ret1 := syscall6(sHCoCreateInstance, 5,
		uintptr(unsafe.Pointer(&aclsidStr[0])),
		uintptr(unsafe.Pointer(clsid)),
		uintptr(unsafe.Pointer(pUnkOuter)),
		uintptr(unsafe.Pointer(refiid)),
		uintptr(unsafe.Pointer(ppv)),
		0)
	return HRESULT(ret1)
}

func SHCreateDefaultContextMenu(pdcm /*const*/ *DEFCONTEXTMENU, riid REFIID, ppv uintptr) HRESULT {
	ret1 := syscall3(sHCreateDefaultContextMenu, 3,
		uintptr(unsafe.Pointer(pdcm)),
		uintptr(unsafe.Pointer(riid)),
		ppv)
	return HRESULT(ret1)
}

func SHCreateDirectory(hWnd HWND, path /*const*/ uintptr) DWORD {
	ret1 := syscall3(sHCreateDirectory, 2,
		uintptr(hWnd),
		path,
		0)
	return DWORD(ret1)
}

func SHCreateDirectoryEx(hWnd HWND, path string, sec *SECURITY_ATTRIBUTES) int32 {
	pathStr := unicode16FromString(path)
	ret1 := syscall3(sHCreateDirectoryEx, 3,
		uintptr(hWnd),
		uintptr(unsafe.Pointer(&pathStr[0])),
		uintptr(unsafe.Pointer(sec)))
	return int32(ret1)
}

func SHCreateFileExtractIconW(file string, attribs DWORD, riid REFIID, ppv uintptr) HRESULT {
	fileStr := unicode16FromString(file)
	ret1 := syscall6(sHCreateFileExtractIconW, 4,
		uintptr(unsafe.Pointer(&fileStr[0])),
		uintptr(attribs),
		uintptr(unsafe.Pointer(riid)),
		ppv,
		0,
		0)
	return HRESULT(ret1)
}

func SHCreateItemFromIDList(pidl /*const*/ PCIDLIST_ABSOLUTE, riid REFIID, ppv uintptr) HRESULT {
	ret1 := syscall3(sHCreateItemFromIDList, 3,
		uintptr(unsafe.Pointer(pidl)),
		uintptr(unsafe.Pointer(riid)),
		ppv)
	return HRESULT(ret1)
}

func SHCreateItemFromParsingName(pszPath string, pbc *IBindCtx, riid REFIID, ppv uintptr) HRESULT {
	pszPathStr := unicode16FromString(pszPath)
	ret1 := syscall6(sHCreateItemFromParsingName, 4,
		uintptr(unsafe.Pointer(&pszPathStr[0])),
		uintptr(unsafe.Pointer(pbc)),
		uintptr(unsafe.Pointer(riid)),
		ppv,
		0,
		0)
	return HRESULT(ret1)
}

func SHCreateQueryCancelAutoPlayMoniker(moniker **IMoniker) HRESULT {
	ret1 := syscall3(sHCreateQueryCancelAutoPlayMoniker, 1,
		uintptr(unsafe.Pointer(moniker)),
		0,
		0)
	return HRESULT(ret1)
}

func SHCreateShellFolderView(pcsfv /*const*/ *SFV_CREATE, ppsv **IShellView) HRESULT {
	ret1 := syscall3(sHCreateShellFolderView, 2,
		uintptr(unsafe.Pointer(pcsfv)),
		uintptr(unsafe.Pointer(ppsv)),
		0)
	return HRESULT(ret1)
}

func SHCreateShellFolderViewEx(psvcbi LPCSFV, ppv **IShellView) HRESULT {
	ret1 := syscall3(sHCreateShellFolderViewEx, 2,
		uintptr(unsafe.Pointer(psvcbi)),
		uintptr(unsafe.Pointer(ppv)),
		0)
	return HRESULT(ret1)
}

func SHCreateShellItem(pidlParent /*const*/ LPCITEMIDLIST, psfParent *IShellFolder, pidl /*const*/ LPCITEMIDLIST, ppsi **IShellItem) HRESULT {
	ret1 := syscall6(sHCreateShellItem, 4,
		uintptr(unsafe.Pointer(pidlParent)),
		uintptr(unsafe.Pointer(psfParent)),
		uintptr(unsafe.Pointer(pidl)),
		uintptr(unsafe.Pointer(ppsi)),
		0,
		0)
	return HRESULT(ret1)
}

func SHCreateShellItemArray(pidlParent /*const*/ PCIDLIST_ABSOLUTE, psf *IShellFolder, cidl UINT, ppidl PCUITEMID_CHILD_ARRAY, ppsiItemArray **IShellItemArray) HRESULT {
	ret1 := syscall6(sHCreateShellItemArray, 5,
		uintptr(unsafe.Pointer(pidlParent)),
		uintptr(unsafe.Pointer(psf)),
		uintptr(cidl),
		uintptr(unsafe.Pointer(ppidl)),
		uintptr(unsafe.Pointer(ppsiItemArray)),
		0)
	return HRESULT(ret1)
}

func SHCreateShellItemArrayFromDataObject(pdo *IDataObject, riid REFIID, ppv uintptr) HRESULT {
	ret1 := syscall3(sHCreateShellItemArrayFromDataObject, 3,
		uintptr(unsafe.Pointer(pdo)),
		uintptr(unsafe.Pointer(riid)),
		ppv)
	return HRESULT(ret1)
}

func SHCreateShellItemArrayFromIDLists(cidl UINT, pidl_array PCIDLIST_ABSOLUTE_ARRAY, psia **IShellItemArray) HRESULT {
	ret1 := syscall3(sHCreateShellItemArrayFromIDLists, 3,
		uintptr(cidl),
		uintptr(unsafe.Pointer(pidl_array)),
		uintptr(unsafe.Pointer(psia)))
	return HRESULT(ret1)
}

func SHCreateShellItemArrayFromShellItem(item *IShellItem, riid REFIID, ppv uintptr) HRESULT {
	ret1 := syscall3(sHCreateShellItemArrayFromShellItem, 3,
		uintptr(unsafe.Pointer(item)),
		uintptr(unsafe.Pointer(riid)),
		ppv)
	return HRESULT(ret1)
}

func SHCreateStdEnumFmtEtc(cFormats DWORD, lpFormats /*const*/ *FORMATETC, ppenumFormatetc *LPENUMFORMATETC) HRESULT {
	ret1 := syscall3(sHCreateStdEnumFmtEtc, 3,
		uintptr(cFormats),
		uintptr(unsafe.Pointer(lpFormats)),
		uintptr(unsafe.Pointer(ppenumFormatetc)))
	return HRESULT(ret1)
}

func SHDefExtractIcon(pszIconFile string, iIndex int32, uFlags UINT, phiconLarge *HICON, phiconSmall *HICON, nIconSize UINT) HRESULT {
	pszIconFileStr := unicode16FromString(pszIconFile)
	ret1 := syscall6(sHDefExtractIcon, 6,
		uintptr(unsafe.Pointer(&pszIconFileStr[0])),
		uintptr(iIndex),
		uintptr(uFlags),
		uintptr(unsafe.Pointer(phiconLarge)),
		uintptr(unsafe.Pointer(phiconSmall)),
		uintptr(nIconSize))
	return HRESULT(ret1)
}

func SHDoDragDrop(hWnd HWND, lpDataObject LPDATAOBJECT, lpDropSource LPDROPSOURCE, dwOKEffect DWORD, pdwEffect *uint32) HRESULT {
	ret1 := syscall6(sHDoDragDrop, 5,
		uintptr(hWnd),
		uintptr(unsafe.Pointer(lpDataObject)),
		uintptr(unsafe.Pointer(lpDropSource)),
		uintptr(dwOKEffect),
		uintptr(unsafe.Pointer(pdwEffect)),
		0)
	return HRESULT(ret1)
}

func SHEmptyRecycleBin(hwnd HWND, pszRootPath string, dwFlags DWORD) HRESULT {
	pszRootPathStr := unicode16FromString(pszRootPath)
	ret1 := syscall3(sHEmptyRecycleBin, 3,
		uintptr(hwnd),
		uintptr(unsafe.Pointer(&pszRootPathStr[0])),
		uintptr(dwFlags))
	return HRESULT(ret1)
}

func SHEnumerateUnreadMailAccountsW(user HKEY, idx DWORD, mailaddress LPWSTR, mailaddresslen INT) HRESULT {
	ret1 := syscall6(sHEnumerateUnreadMailAccountsW, 4,
		uintptr(user),
		uintptr(idx),
		uintptr(unsafe.Pointer(mailaddress)),
		uintptr(mailaddresslen),
		0,
		0)
	return HRESULT(ret1)
}

func SHFileOperation(lpFileOp LPSHFILEOPSTRUCT) int32 {
	ret1 := syscall3(sHFileOperation, 1,
		uintptr(unsafe.Pointer(lpFileOp)),
		0,
		0)
	return int32(ret1)
}

func SHFindFiles(pidlFolder /*const*/ LPCITEMIDLIST, pidlSaveFile /*const*/ LPCITEMIDLIST) bool {
	ret1 := syscall3(sHFindFiles, 2,
		uintptr(unsafe.Pointer(pidlFolder)),
		uintptr(unsafe.Pointer(pidlSaveFile)),
		0)
	return ret1 != 0
}

func SHFind_InitMenuPopup(hMenu HMENU, hWndParent HWND, w DWORD, x DWORD) LPVOID {
	ret1 := syscall6(sHFind_InitMenuPopup, 4,
		uintptr(hMenu),
		uintptr(hWndParent),
		uintptr(w),
		uintptr(x),
		0,
		0)
	return (LPVOID)(unsafe.Pointer(ret1))
}

func SHFlushClipboard() HRESULT {
	ret1 := syscall3(sHFlushClipboard, 0,
		0,
		0,
		0)
	return HRESULT(ret1)
}

func SHFlushSFCache() {
	syscall3(sHFlushSFCache, 0,
		0,
		0,
		0)
}

func SHFormatDrive(hwnd HWND, drive UINT, fmtID UINT, options UINT) DWORD {
	ret1 := syscall6(sHFormatDrive, 4,
		uintptr(hwnd),
		uintptr(drive),
		uintptr(fmtID),
		uintptr(options),
		0,
		0)
	return DWORD(ret1)
}

func SHFree(pv LPVOID) {
	syscall3(sHFree, 1,
		uintptr(unsafe.Pointer(pv)),
		0,
		0)
}

func SHFreeNameMappings(hNameMapping HANDLE) {
	syscall3(sHFreeNameMappings, 1,
		uintptr(hNameMapping),
		0,
		0)
}

func SHGetDataFromIDList(psf LPSHELLFOLDER, pidl /*const*/ LPCITEMIDLIST, nFormat int32, dest LPVOID, aLen int32) HRESULT {
	ret1 := syscall6(sHGetDataFromIDList, 5,
		uintptr(unsafe.Pointer(psf)),
		uintptr(unsafe.Pointer(pidl)),
		uintptr(nFormat),
		uintptr(unsafe.Pointer(dest)),
		uintptr(aLen),
		0)
	return HRESULT(ret1)
}

func SHGetDesktopFolder(psf **IShellFolder) HRESULT {
	ret1 := syscall3(sHGetDesktopFolder, 1,
		uintptr(unsafe.Pointer(psf)),
		0,
		0)
	return HRESULT(ret1)
}

func SHGetFileInfo(path string, dwFileAttributes DWORD, psfi *SHFILEINFO, sizeofpsfi UINT, flags UINT) *uint32 {
	pathStr := unicode16FromString(path)
	ret1 := syscall6(sHGetFileInfo, 5,
		uintptr(unsafe.Pointer(&pathStr[0])),
		uintptr(dwFileAttributes),
		uintptr(unsafe.Pointer(psfi)),
		uintptr(sizeofpsfi),
		uintptr(flags),
		0)
	return (*uint32)(unsafe.Pointer(ret1))
}

func SHGetFolderLocation(hwndOwner HWND, nFolder int32, hToken HANDLE, dwReserved DWORD, ppidl *LPITEMIDLIST) HRESULT {
	ret1 := syscall6(sHGetFolderLocation, 5,
		uintptr(hwndOwner),
		uintptr(nFolder),
		uintptr(hToken),
		uintptr(dwReserved),
		uintptr(unsafe.Pointer(ppidl)),
		0)
	return HRESULT(ret1)
}

func SHGetFolderPathAndSubDir(hwndOwner HWND, nFolder int32, hToken HANDLE, dwFlags DWORD, pszSubPath string, pszPath LPWSTR) HRESULT {
	pszSubPathStr := unicode16FromString(pszSubPath)
	ret1 := syscall6(sHGetFolderPathAndSubDir, 6,
		uintptr(hwndOwner),
		uintptr(nFolder),
		uintptr(hToken),
		uintptr(dwFlags),
		uintptr(unsafe.Pointer(&pszSubPathStr[0])),
		uintptr(unsafe.Pointer(pszPath)))
	return HRESULT(ret1)
}

func SHGetFolderPathEx(rfid REFKNOWNFOLDERID, flags DWORD, token HANDLE, path LPWSTR, aLen DWORD) HRESULT {
	ret1 := syscall6(sHGetFolderPathEx, 5,
		uintptr(unsafe.Pointer(rfid)),
		uintptr(flags),
		uintptr(token),
		uintptr(unsafe.Pointer(path)),
		uintptr(aLen),
		0)
	return HRESULT(ret1)
}

func SHGetFolderPath(hwndOwner HWND, nFolder int32, hToken HANDLE, dwFlags DWORD, pszPath LPWSTR) HRESULT {
	ret1 := syscall6(sHGetFolderPath, 5,
		uintptr(hwndOwner),
		uintptr(nFolder),
		uintptr(hToken),
		uintptr(dwFlags),
		uintptr(unsafe.Pointer(pszPath)),
		0)
	return HRESULT(ret1)
}

func SHGetIDListFromObject(punk *IUnknown, ppidl *PIDLIST_ABSOLUTE) HRESULT {
	ret1 := syscall3(sHGetIDListFromObject, 2,
		uintptr(unsafe.Pointer(punk)),
		uintptr(unsafe.Pointer(ppidl)),
		0)
	return HRESULT(ret1)
}

func SHGetIconOverlayIndex(pszIconPath string, iIconIndex INT) INT {
	pszIconPathStr := unicode16FromString(pszIconPath)
	ret1 := syscall3(sHGetIconOverlayIndex, 2,
		uintptr(unsafe.Pointer(&pszIconPathStr[0])),
		uintptr(iIconIndex),
		0)
	return INT(ret1)
}

func SHGetImageList(iImageList int32, riid REFIID, ppv uintptr) HRESULT {
	ret1 := syscall3(sHGetImageList, 3,
		uintptr(iImageList),
		uintptr(unsafe.Pointer(riid)),
		ppv)
	return HRESULT(ret1)
}

func SHGetInstanceExplorer(lpUnknown **IUnknown) HRESULT {
	ret1 := syscall3(sHGetInstanceExplorer, 1,
		uintptr(unsafe.Pointer(lpUnknown)),
		0,
		0)
	return HRESULT(ret1)
}

func SHGetItemFromDataObject(pdtobj *IDataObject, dwFlags DATAOBJ_GET_ITEM_FLAGS, riid REFIID, ppv uintptr) HRESULT {
	ret1 := syscall6(sHGetItemFromDataObject, 4,
		uintptr(unsafe.Pointer(pdtobj)),
		uintptr(dwFlags),
		uintptr(unsafe.Pointer(riid)),
		ppv,
		0,
		0)
	return HRESULT(ret1)
}

func SHGetItemFromObject(punk *IUnknown, riid REFIID, ppv uintptr) HRESULT {
	ret1 := syscall3(sHGetItemFromObject, 3,
		uintptr(unsafe.Pointer(punk)),
		uintptr(unsafe.Pointer(riid)),
		ppv)
	return HRESULT(ret1)
}

func SHGetKnownFolderIDList(rfid REFKNOWNFOLDERID, flags DWORD, token HANDLE, pidl *PIDLIST_ABSOLUTE) HRESULT {
	ret1 := syscall6(sHGetKnownFolderIDList, 4,
		uintptr(unsafe.Pointer(rfid)),
		uintptr(flags),
		uintptr(token),
		uintptr(unsafe.Pointer(pidl)),
		0,
		0)
	return HRESULT(ret1)
}

func SHGetKnownFolderItem(rfid REFKNOWNFOLDERID, flags KNOWN_FOLDER_FLAG, hToken HANDLE, riid REFIID, ppv uintptr) HRESULT {
	ret1 := syscall6(sHGetKnownFolderItem, 5,
		uintptr(unsafe.Pointer(rfid)),
		uintptr(flags),
		uintptr(hToken),
		uintptr(unsafe.Pointer(riid)),
		ppv,
		0)
	return HRESULT(ret1)
}

func SHGetKnownFolderPath(rfid REFKNOWNFOLDERID, flags DWORD, token HANDLE, path *PWSTR) HRESULT {
	ret1 := syscall6(sHGetKnownFolderPath, 4,
		uintptr(unsafe.Pointer(rfid)),
		uintptr(flags),
		uintptr(token),
		uintptr(unsafe.Pointer(path)),
		0,
		0)
	return HRESULT(ret1)
}

func SHGetLocalizedName(path string, module LPWSTR, size UINT, res *int32) HRESULT {
	pathStr := unicode16FromString(path)
	ret1 := syscall6(sHGetLocalizedName, 4,
		uintptr(unsafe.Pointer(&pathStr[0])),
		uintptr(unsafe.Pointer(module)),
		uintptr(size),
		uintptr(unsafe.Pointer(res)),
		0,
		0)
	return HRESULT(ret1)
}

func SHGetMalloc(lpmal *LPMALLOC) HRESULT {
	ret1 := syscall3(sHGetMalloc, 1,
		uintptr(unsafe.Pointer(lpmal)),
		0,
		0)
	return HRESULT(ret1)
}

func SHGetNameFromIDList(pidl /*const*/ PCIDLIST_ABSOLUTE, sigdnName SIGDN, ppszName *PWSTR) HRESULT {
	ret1 := syscall3(sHGetNameFromIDList, 3,
		uintptr(unsafe.Pointer(pidl)),
		uintptr(sigdnName),
		uintptr(unsafe.Pointer(ppszName)))
	return HRESULT(ret1)
}

func SHGetNewLinkInfo(pszLinkTo string, pszDir string, pszName LPWSTR, pfMustCopy *BOOL, uFlags UINT) bool {
	pszLinkToStr := unicode16FromString(pszLinkTo)
	pszDirStr := unicode16FromString(pszDir)
	ret1 := syscall6(sHGetNewLinkInfo, 5,
		uintptr(unsafe.Pointer(&pszLinkToStr[0])),
		uintptr(unsafe.Pointer(&pszDirStr[0])),
		uintptr(unsafe.Pointer(pszName)),
		uintptr(unsafe.Pointer(pfMustCopy)),
		uintptr(uFlags),
		0)
	return ret1 != 0
}

func SHGetPathFromIDList(pidl /*const*/ LPCITEMIDLIST, pszPath LPWSTR) bool {
	ret1 := syscall3(sHGetPathFromIDList, 2,
		uintptr(unsafe.Pointer(pidl)),
		uintptr(unsafe.Pointer(pszPath)),
		0)
	return ret1 != 0
}

func SHGetPropertyStoreForWindow(hwnd HWND, riid REFIID, ppv uintptr) HRESULT {
	ret1 := syscall3(sHGetPropertyStoreForWindow, 3,
		uintptr(hwnd),
		uintptr(unsafe.Pointer(riid)),
		ppv)
	return HRESULT(ret1)
}

func SHGetPropertyStoreFromParsingName(pszPath string, pbc *IBindCtx, flags GETPROPERTYSTOREFLAGS, riid REFIID, ppv uintptr) HRESULT {
	pszPathStr := unicode16FromString(pszPath)
	ret1 := syscall6(sHGetPropertyStoreFromParsingName, 5,
		uintptr(unsafe.Pointer(&pszPathStr[0])),
		uintptr(unsafe.Pointer(pbc)),
		uintptr(flags),
		uintptr(unsafe.Pointer(riid)),
		ppv,
		0)
	return HRESULT(ret1)
}

func SHGetRealIDL(lpsf LPSHELLFOLDER, pidlSimple /*const*/ LPCITEMIDLIST, pidlReal *LPITEMIDLIST) HRESULT {
	ret1 := syscall3(sHGetRealIDL, 3,
		uintptr(unsafe.Pointer(lpsf)),
		uintptr(unsafe.Pointer(pidlSimple)),
		uintptr(unsafe.Pointer(pidlReal)))
	return HRESULT(ret1)
}

func SHGetSetSettings(lpss LPSHELLSTATE, dwMask DWORD, bSet bool) {
	syscall3(sHGetSetSettings, 3,
		uintptr(unsafe.Pointer(lpss)),
		uintptr(dwMask),
		getUintptrFromBool(bSet))
}

func SHGetSettings(lpsfs LPSHELLFLAGSTATE, dwMask DWORD) {
	syscall3(sHGetSettings, 2,
		uintptr(unsafe.Pointer(lpsfs)),
		uintptr(dwMask),
		0)
}

func SHGetSpecialFolderLocation(hwndOwner HWND, nFolder INT, ppidl *LPITEMIDLIST) HRESULT {
	ret1 := syscall3(sHGetSpecialFolderLocation, 3,
		uintptr(hwndOwner),
		uintptr(nFolder),
		uintptr(unsafe.Pointer(ppidl)))
	return HRESULT(ret1)
}

func SHGetSpecialFolderPath(hwndOwner HWND, szPath LPWSTR, nFolder int32, bCreate bool) bool {
	ret1 := syscall6(sHGetSpecialFolderPath, 4,
		uintptr(hwndOwner),
		uintptr(unsafe.Pointer(szPath)),
		uintptr(nFolder),
		getUintptrFromBool(bCreate),
		0,
		0)
	return ret1 != 0
}

func SHGetStockIconInfo(id SHSTOCKICONID, flags UINT, sii *SHSTOCKICONINFO) HRESULT {
	ret1 := syscall3(sHGetStockIconInfo, 3,
		uintptr(id),
		uintptr(flags),
		uintptr(unsafe.Pointer(sii)))
	return HRESULT(ret1)
}

func SHHandleUpdateImage(pidlExtra /*const*/ LPCITEMIDLIST) INT {
	ret1 := syscall3(sHHandleUpdateImage, 1,
		uintptr(unsafe.Pointer(pidlExtra)),
		0,
		0)
	return INT(ret1)
}

func SHHelpShortcuts_RunDLL(dwArg1 DWORD, dwArg2 DWORD, dwArg3 DWORD, dwArg4 DWORD) DWORD {
	ret1 := syscall6(sHHelpShortcuts_RunDLL, 4,
		uintptr(dwArg1),
		uintptr(dwArg2),
		uintptr(dwArg3),
		uintptr(dwArg4),
		0,
		0)
	return DWORD(ret1)
}

func SHIsFileAvailableOffline(path string, status *uint32) HRESULT {
	pathStr := unicode16FromString(path)
	ret1 := syscall3(sHIsFileAvailableOffline, 2,
		uintptr(unsafe.Pointer(&pathStr[0])),
		uintptr(unsafe.Pointer(status)),
		0)
	return HRESULT(ret1)
}

func SHLimitInputEdit(textbox HWND, folder *IShellFolder) HRESULT {
	ret1 := syscall3(sHLimitInputEdit, 2,
		uintptr(textbox),
		uintptr(unsafe.Pointer(folder)),
		0)
	return HRESULT(ret1)
}

func SHLoadInProc(rclsid /*const*/ REFCLSID) HRESULT {
	ret1 := syscall3(sHLoadInProc, 1,
		uintptr(unsafe.Pointer(rclsid)),
		0,
		0)
	return HRESULT(ret1)
}

func SHLoadNonloadedIconOverlayIdentifiers() HRESULT {
	ret1 := syscall3(sHLoadNonloadedIconOverlayIdentifiers, 0,
		0,
		0,
		0)
	return HRESULT(ret1)
}

func SHMapIDListToImageListIndexAsync(pts *IUnknown, psf *IShellFolder, pidl /*const*/ LPCITEMIDLIST, flags UINT, pfn uintptr, pvData uintptr, pvHint uintptr, piIndex *int, piIndexSel *int) HRESULT {
	ret1 := syscall9(sHMapIDListToImageListIndexAsync, 9,
		uintptr(unsafe.Pointer(pts)),
		uintptr(unsafe.Pointer(psf)),
		uintptr(unsafe.Pointer(pidl)),
		uintptr(flags),
		pfn,
		pvData,
		pvHint,
		uintptr(unsafe.Pointer(piIndex)),
		uintptr(unsafe.Pointer(piIndexSel)))
	return HRESULT(ret1)
}

func SHMapPIDLToSystemImageListIndex(sh *IShellFolder, pidl /*const*/ LPCITEMIDLIST, pIndex *int) int32 {
	ret1 := syscall3(sHMapPIDLToSystemImageListIndex, 3,
		uintptr(unsafe.Pointer(sh)),
		uintptr(unsafe.Pointer(pidl)),
		uintptr(unsafe.Pointer(pIndex)))
	return int32(ret1)
}

func SHObjectProperties(hwnd HWND, dwType DWORD, szObject string, szPage string) bool {
	szObjectStr := unicode16FromString(szObject)
	szPageStr := unicode16FromString(szPage)
	ret1 := syscall6(sHObjectProperties, 4,
		uintptr(hwnd),
		uintptr(dwType),
		uintptr(unsafe.Pointer(&szObjectStr[0])),
		uintptr(unsafe.Pointer(&szPageStr[0])),
		0,
		0)
	return ret1 != 0
}

func SHOpenFolderAndSelectItems(pidlFolder /*const*/ PCIDLIST_ABSOLUTE, cidl UINT, apidl *PCUITEMID_CHILD_ARRAY, flags DWORD) HRESULT {
	ret1 := syscall6(sHOpenFolderAndSelectItems, 4,
		uintptr(unsafe.Pointer(pidlFolder)),
		uintptr(cidl),
		uintptr(unsafe.Pointer(apidl)),
		uintptr(flags),
		0,
		0)
	return HRESULT(ret1)
}

func SHParseDisplayName(name string, bindctx *IBindCtx, pidlist *LPITEMIDLIST, attr_in SFGAOF, attr_out *SFGAOF) HRESULT {
	nameStr := unicode16FromString(name)
	ret1 := syscall6(sHParseDisplayName, 5,
		uintptr(unsafe.Pointer(&nameStr[0])),
		uintptr(unsafe.Pointer(bindctx)),
		uintptr(unsafe.Pointer(pidlist)),
		uintptr(attr_in),
		uintptr(unsafe.Pointer(attr_out)),
		0)
	return HRESULT(ret1)
}

func SHPathPrepareForWrite(hwnd HWND, modless *IUnknown, path string, flags DWORD) HRESULT {
	pathStr := unicode16FromString(path)
	ret1 := syscall6(sHPathPrepareForWrite, 4,
		uintptr(hwnd),
		uintptr(unsafe.Pointer(modless)),
		uintptr(unsafe.Pointer(&pathStr[0])),
		uintptr(flags),
		0,
		0)
	return HRESULT(ret1)
}

func SHPropStgCreate(psstg *IPropertySetStorage, fmtid REFFMTID, pclsid /*const*/ *CLSID, grfFlags DWORD, grfMode DWORD, dwDisposition DWORD, ppstg **IPropertyStorage, puCodePage *UINT) HRESULT {
	ret1 := syscall9(sHPropStgCreate, 8,
		uintptr(unsafe.Pointer(psstg)),
		uintptr(unsafe.Pointer(fmtid)),
		uintptr(unsafe.Pointer(pclsid)),
		uintptr(grfFlags),
		uintptr(grfMode),
		uintptr(dwDisposition),
		uintptr(unsafe.Pointer(ppstg)),
		uintptr(unsafe.Pointer(puCodePage)),
		0)
	return HRESULT(ret1)
}

func SHPropStgReadMultiple(pps *IPropertyStorage, uCodePage UINT, cpspec ULONG, rgpspec /*const*/ *PROPSPEC, rgvar *PROPVARIANT) HRESULT {
	ret1 := syscall6(sHPropStgReadMultiple, 5,
		uintptr(unsafe.Pointer(pps)),
		uintptr(uCodePage),
		uintptr(cpspec),
		uintptr(unsafe.Pointer(rgpspec)),
		uintptr(unsafe.Pointer(rgvar)),
		0)
	return HRESULT(ret1)
}

func SHPropStgWriteMultiple(pps *IPropertyStorage, uCodePage *UINT, cpspec ULONG, rgpspec /*const*/ *PROPSPEC, rgvar *PROPVARIANT, propidNameFirst PROPID) HRESULT {
	ret1 := syscall6(sHPropStgWriteMultiple, 6,
		uintptr(unsafe.Pointer(pps)),
		uintptr(unsafe.Pointer(uCodePage)),
		uintptr(cpspec),
		uintptr(unsafe.Pointer(rgpspec)),
		uintptr(unsafe.Pointer(rgvar)),
		uintptr(propidNameFirst))
	return HRESULT(ret1)
}

func SHQueryRecycleBin(pszRootPath string, pSHQueryRBInfo LPSHQUERYRBINFO) HRESULT {
	pszRootPathStr := unicode16FromString(pszRootPath)
	ret1 := syscall3(sHQueryRecycleBin, 2,
		uintptr(unsafe.Pointer(&pszRootPathStr[0])),
		uintptr(unsafe.Pointer(pSHQueryRBInfo)),
		0)
	return HRESULT(ret1)
}

func SHQueryUserNotificationState(state *QUERY_USER_NOTIFICATION_STATE) HRESULT {
	ret1 := syscall3(sHQueryUserNotificationState, 1,
		uintptr(unsafe.Pointer(state)),
		0,
		0)
	return HRESULT(ret1)
}

func SHRemoveLocalizedName(path /*const*/ *WCHAR) HRESULT {
	ret1 := syscall3(sHRemoveLocalizedName, 1,
		uintptr(unsafe.Pointer(path)),
		0,
		0)
	return HRESULT(ret1)
}

func SHRestricted(policy RESTRICTIONS) DWORD {
	ret1 := syscall3(sHRestricted, 1,
		uintptr(policy),
		0,
		0)
	return DWORD(ret1)
}

func SHRunControlPanel(commandLine string, parent HWND) bool {
	commandLineStr := unicode16FromString(commandLine)
	ret1 := syscall3(sHRunControlPanel, 2,
		uintptr(unsafe.Pointer(&commandLineStr[0])),
		uintptr(parent),
		0)
	return ret1 != 0
}

func SHSetInstanceExplorer(lpUnknown LPUNKNOWN) {
	syscall3(sHSetInstanceExplorer, 1,
		uintptr(unsafe.Pointer(lpUnknown)),
		0,
		0)
}

func SHSetLocalizedName(pszPath LPWSTR, pszResModule string, idsRes int32) HRESULT {
	pszResModuleStr := unicode16FromString(pszResModule)
	ret1 := syscall3(sHSetLocalizedName, 3,
		uintptr(unsafe.Pointer(pszPath)),
		uintptr(unsafe.Pointer(&pszResModuleStr[0])),
		uintptr(idsRes))
	return HRESULT(ret1)
}

func SHSetUnreadMailCountW(mailaddress string, count DWORD, executecommand string) HRESULT {
	mailaddressStr := unicode16FromString(mailaddress)
	executecommandStr := unicode16FromString(executecommand)
	ret1 := syscall3(sHSetUnreadMailCountW, 3,
		uintptr(unsafe.Pointer(&mailaddressStr[0])),
		uintptr(count),
		uintptr(unsafe.Pointer(&executecommandStr[0])))
	return HRESULT(ret1)
}

func SHShellFolderView_Message(hwndCabinet HWND, uMessage UINT, lParam LPARAM) LRESULT {
	ret1 := syscall3(sHShellFolderView_Message, 3,
		uintptr(hwndCabinet),
		uintptr(uMessage),
		uintptr(lParam))
	return LRESULT(ret1)
}

func SHUpdateImage(pszHashItem string, iIndex int32, uFlags UINT, iImageIndex int32) {
	pszHashItemStr := unicode16FromString(pszHashItem)
	syscall6(sHUpdateImage, 4,
		uintptr(unsafe.Pointer(&pszHashItemStr[0])),
		uintptr(iIndex),
		uintptr(uFlags),
		uintptr(iImageIndex),
		0,
		0)
}

func SHUpdateRecycleBinIcon() HRESULT {
	ret1 := syscall3(sHUpdateRecycleBinIcon, 0,
		0,
		0,
		0)
	return HRESULT(ret1)
}

func SHValidateUNC(hwndOwner HWND, pszFile PWSTR, fConnect UINT) bool {
	ret1 := syscall3(sHValidateUNC, 3,
		uintptr(hwndOwner),
		uintptr(unsafe.Pointer(pszFile)),
		uintptr(fConnect))
	return ret1 != 0
}

func SetCurrentProcessExplicitAppUserModelID(appid string) HRESULT {
	appidStr := unicode16FromString(appid)
	ret1 := syscall3(setCurrentProcessExplicitAppUserModelID, 1,
		uintptr(unsafe.Pointer(&appidStr[0])),
		0,
		0)
	return HRESULT(ret1)
}

func SheChangeDir(path LPWSTR) DWORD {
	ret1 := syscall3(sheChangeDir, 1,
		uintptr(unsafe.Pointer(path)),
		0,
		0)
	return DWORD(ret1)
}

func SheGetDir(drive DWORD, buffer LPWSTR) DWORD {
	ret1 := syscall3(sheGetDir, 2,
		uintptr(drive),
		uintptr(unsafe.Pointer(buffer)),
		0)
	return DWORD(ret1)
}

func ShellAbout(hWnd HWND, szApp string, szOtherStuff string, hIcon HICON) bool {
	szAppStr := unicode16FromString(szApp)
	szOtherStuffStr := unicode16FromString(szOtherStuff)
	ret1 := syscall6(shellAbout, 4,
		uintptr(hWnd),
		uintptr(unsafe.Pointer(&szAppStr[0])),
		uintptr(unsafe.Pointer(&szOtherStuffStr[0])),
		uintptr(hIcon),
		0,
		0)
	return ret1 != 0
}

func ShellExecute(hwnd HWND, lpVerb string, lpFile string, lpParameters string, lpDirectory string, nShowCmd INT) HINSTANCE {
	lpVerbStr := unicode16FromString(lpVerb)
	lpFileStr := unicode16FromString(lpFile)
	lpParametersStr := unicode16FromString(lpParameters)
	lpDirectoryStr := unicode16FromString(lpDirectory)
	ret1 := syscall6(shellExecute, 6,
		uintptr(hwnd),
		uintptr(unsafe.Pointer(&lpVerbStr[0])),
		uintptr(unsafe.Pointer(&lpFileStr[0])),
		uintptr(unsafe.Pointer(&lpParametersStr[0])),
		uintptr(unsafe.Pointer(&lpDirectoryStr[0])),
		uintptr(nShowCmd))
	return HINSTANCE(ret1)
}

func Shell_GetImageLists(lpBigList *HIMAGELIST, lpSmallList *HIMAGELIST) bool {
	ret1 := syscall3(shell_GetImageLists, 2,
		uintptr(unsafe.Pointer(lpBigList)),
		uintptr(unsafe.Pointer(lpSmallList)),
		0)
	return ret1 != 0
}

func Shell_MergeMenus(hmDst HMENU, hmSrc HMENU, uInsert UINT, uIDAdjust UINT, uIDAdjustMax UINT, uFlags ULONG) UINT {
	ret1 := syscall6(shell_MergeMenus, 6,
		uintptr(hmDst),
		uintptr(hmSrc),
		uintptr(uInsert),
		uintptr(uIDAdjust),
		uintptr(uIDAdjustMax),
		uintptr(uFlags))
	return UINT(ret1)
}

func Shell_NotifyIcon(dwMessage DWORD, nid PNOTIFYICONDATA) bool {
	ret1 := syscall3(shell_NotifyIcon, 2,
		uintptr(dwMessage),
		uintptr(unsafe.Pointer(nid)),
		0)
	return ret1 != 0
}

func SignalFileOpen(pidl /*const*/ PCIDLIST_ABSOLUTE) bool {
	ret1 := syscall3(signalFileOpen, 1,
		uintptr(unsafe.Pointer(pidl)),
		0,
		0)
	return ret1 != 0
}

func WOWShellExecute(hWnd HWND, lpOperation /*const*/ LPCSTR, lpFile /*const*/ LPCSTR, lpParameters /*const*/ LPCSTR, lpDirectory /*const*/ LPCSTR, iShowCmd INT, callback uintptr) HINSTANCE {
	ret1 := syscall9(wOWShellExecute, 7,
		uintptr(hWnd),
		uintptr(unsafe.Pointer(lpOperation)),
		uintptr(unsafe.Pointer(lpFile)),
		uintptr(unsafe.Pointer(lpParameters)),
		uintptr(unsafe.Pointer(lpDirectory)),
		uintptr(iShowCmd),
		callback,
		0,
		0)
	return HINSTANCE(ret1)
}

func WriteCabinetState(cs *CABINETSTATE) bool {
	ret1 := syscall3(writeCabinetState, 1,
		uintptr(unsafe.Pointer(cs)),
		0,
		0)
	return ret1 != 0
}
