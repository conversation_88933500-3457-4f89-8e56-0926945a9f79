// This file was automatically generated by https://github.com/kbinani/win/blob/generator/internal/cmd/gen/gen.go
// go run internal/cmd/gen/gen.go

// +build windows

package win

import (
	"unsafe"
)

var (
	// Library
	libole32 uintptr

	// Functions
	coGetInterceptor                      uintptr
	coGetInterceptorFromTypeInfo          uintptr
	propVariantChangeType                 uintptr
	bindMoniker                           uintptr
	cLSIDFromProgIDEx                     uintptr
	cLSIDFromString                       uintptr
	coAddRefServerProcess                 uintptr
	coAllowSetForegroundWindow            uintptr
	coBuildVersion                        uintptr
	coCopyProxy                           uintptr
	coCreateFreeThreadedMarshaler         uintptr
	coCreateGuid                          uintptr
	coDisconnectObject                    uintptr
	coFileTimeNow                         uintptr
	coFreeAllLibraries                    uintptr
	coFreeLibrary                         uintptr
	coGetActivationState                  uintptr
	coGetApartmentType                    uintptr
	coGetCallContext                      uintptr
	coGetCallState                        uintptr
	coGetCallerTID                        uintptr
	coGetContextToken                     uintptr
	coGetCurrentLogicalThreadId           uintptr
	coGetCurrentProcess                   uintptr
	coGetDefaultContext                   uintptr
	coGetInstanceFromIStorage             uintptr
	coGetInterfaceAndReleaseStream        uintptr
	coGetMalloc                           uintptr
	coGetMarshalSizeMax                   uintptr
	coGetObject                           uintptr
	coGetObjectContext                    uintptr
	coGetPSClsid                          uintptr
	coGetStandardMarshal                  uintptr
	coGetState                            uintptr
	coGetTreatAsClass                     uintptr
	coImpersonateClient                   uintptr
	coInitialize                          uintptr
	coInitializeSecurity                  uintptr
	coInitializeWOW                       uintptr
	coIsHandlerConnected                  uintptr
	coIsOle1Class                         uintptr
	coLoadLibrary                         uintptr
	coLockObjectExternal                  uintptr
	coMarshalHresult                      uintptr
	coMarshalInterThreadInterfaceInStream uintptr
	coMarshalInterface                    uintptr
	coQueryClientBlanket                  uintptr
	coQueryProxyBlanket                   uintptr
	coRegisterChannelHook                 uintptr
	coRegisterClassObject                 uintptr
	coRegisterInitializeSpy               uintptr
	coRegisterMallocSpy                   uintptr
	coRegisterMessageFilter               uintptr
	coRegisterPSClsid                     uintptr
	coReleaseMarshalData                  uintptr
	coReleaseServerProcess                uintptr
	coResumeClassObjects                  uintptr
	coRevertToSelf                        uintptr
	coRevokeInitializeSpy                 uintptr
	coRevokeMallocSpy                     uintptr
	coSetProxyBlanket                     uintptr
	coSetState                            uintptr
	coSuspendClassObjects                 uintptr
	coSwitchCallContext                   uintptr
	coTaskMemAlloc                        uintptr
	coTaskMemFree                         uintptr
	coTaskMemRealloc                      uintptr
	coTreatAsClass                        uintptr
	coUnmarshalHresult                    uintptr
	coUnmarshalInterface                  uintptr
	coWaitForMultipleHandles              uintptr
	createAntiMoniker                     uintptr
	createBindCtx                         uintptr
	createClassMoniker                    uintptr
	createDataAdviseHolder                uintptr
	createDataCache                       uintptr
	createFileMoniker                     uintptr
	createGenericComposite                uintptr
	createILockBytesOnHGlobal             uintptr
	createItemMoniker                     uintptr
	createOleAdviseHolder                 uintptr
	createPointerMoniker                  uintptr
	createStreamOnHGlobal                 uintptr
	dllDebugObjectRPCHook                 uintptr
	doDragDrop                            uintptr
	fmtIdToPropStgName                    uintptr
	freePropVariantArray                  uintptr
	getClassFile                          uintptr
	getConvertStg                         uintptr
	getHGlobalFromILockBytes              uintptr
	getHGlobalFromStream                  uintptr
	getRunningObjectTable                 uintptr
	iIDFromString                         uintptr
	isAccelerator                         uintptr
	isEqualGUID                           uintptr
	isValidInterface                      uintptr
	mkParseDisplayName                    uintptr
	monikerCommonPrefixWith               uintptr
	oleBuildVersion                       uintptr
	oleConvertIStorageToOLESTREAM         uintptr
	oleConvertOLESTREAMToIStorage         uintptr
	oleCreate                             uintptr
	oleCreateDefaultHandler               uintptr
	oleCreateEmbeddingHelper              uintptr
	oleCreateFromData                     uintptr
	oleCreateFromDataEx                   uintptr
	oleCreateFromFile                     uintptr
	oleCreateFromFileEx                   uintptr
	oleCreateLink                         uintptr
	oleCreateLinkFromData                 uintptr
	oleCreateLinkToFile                   uintptr
	oleCreateMenuDescriptor               uintptr
	oleCreateStaticFromData               uintptr
	oleDestroyMenuDescriptor              uintptr
	oleDoAutoConvert                      uintptr
	oleDraw                               uintptr
	oleDuplicateData                      uintptr
	oleFlushClipboard                     uintptr
	oleGetAutoConvert                     uintptr
	oleGetClipboard                       uintptr
	oleGetIconOfClass                     uintptr
	oleInitialize                         uintptr
	oleUninitialize                       uintptr
	oleInitializeWOW                      uintptr
	oleIsCurrentClipboard                 uintptr
	oleIsRunning                          uintptr
	oleLoad                               uintptr
	oleLoadFromStream                     uintptr
	oleLockRunning                        uintptr
	oleMetafilePictFromIconAndLabel       uintptr
	oleNoteObjectVisible                  uintptr
	oleQueryCreateFromData                uintptr
	oleQueryLinkFromData                  uintptr
	oleRegEnumVerbs                       uintptr
	oleRegGetMiscStatus                   uintptr
	oleRegGetUserType                     uintptr
	oleSave                               uintptr
	oleSaveToStream                       uintptr
	oleSetAutoConvert                     uintptr
	oleSetClipboard                       uintptr
	oleSetContainedObject                 uintptr
	oleSetMenuDescriptor                  uintptr
	oleTranslateAccelerator               uintptr
	propStgNameToFmtId                    uintptr
	propSysAllocString                    uintptr
	propSysFreeString                     uintptr
	propVariantClear                      uintptr
	propVariantCopy                       uintptr
	readClassStg                          uintptr
	readClassStm                          uintptr
	readFmtUserTypeStg                    uintptr
	registerDragDrop                      uintptr
	releaseStgMedium                      uintptr
	revokeDragDrop                        uintptr
	setConvertStg                         uintptr
	stgCreateDocfile                      uintptr
	stgCreateDocfileOnILockBytes          uintptr
	stgCreatePropSetStg                   uintptr
	stgCreatePropStg                      uintptr
	stgIsStorageFile                      uintptr
	stgIsStorageILockBytes                uintptr
	stgOpenPropStg                        uintptr
	stgSetTimes                           uintptr
	stringFromCLSID                       uintptr
	stringFromGUID2                       uintptr
	wdtpInterfacePointer_UserFree         uintptr
	writeClassStg                         uintptr
	writeClassStm                         uintptr
	writeFmtUserTypeStg                   uintptr
)

func init() {
	// Library
	libole32 = doLoadLibrary("ole32.dll")

	// Functions
	coGetInterceptor = doGetProcAddress(libole32, "CoGetInterceptor")
	coGetInterceptorFromTypeInfo = doGetProcAddress(libole32, "CoGetInterceptorFromTypeInfo")
	propVariantChangeType = doGetProcAddress(libole32, "PropVariantChangeType")
	bindMoniker = doGetProcAddress(libole32, "BindMoniker")
	cLSIDFromProgIDEx = doGetProcAddress(libole32, "CLSIDFromProgIDEx")
	cLSIDFromString = doGetProcAddress(libole32, "CLSIDFromString")
	coAddRefServerProcess = doGetProcAddress(libole32, "CoAddRefServerProcess")
	coAllowSetForegroundWindow = doGetProcAddress(libole32, "CoAllowSetForegroundWindow")
	coBuildVersion = doGetProcAddress(libole32, "CoBuildVersion")
	coCopyProxy = doGetProcAddress(libole32, "CoCopyProxy")
	coCreateFreeThreadedMarshaler = doGetProcAddress(libole32, "CoCreateFreeThreadedMarshaler")
	coCreateGuid = doGetProcAddress(libole32, "CoCreateGuid")
	coDisconnectObject = doGetProcAddress(libole32, "CoDisconnectObject")
	coFileTimeNow = doGetProcAddress(libole32, "CoFileTimeNow")
	coFreeAllLibraries = doGetProcAddress(libole32, "CoFreeAllLibraries")
	coFreeLibrary = doGetProcAddress(libole32, "CoFreeLibrary")
	coGetActivationState = doGetProcAddress(libole32, "CoGetActivationState")
	coGetApartmentType = doGetProcAddress(libole32, "CoGetApartmentType")
	coGetCallContext = doGetProcAddress(libole32, "CoGetCallContext")
	coGetCallState = doGetProcAddress(libole32, "CoGetCallState")
	coGetCallerTID = doGetProcAddress(libole32, "CoGetCallerTID")
	coGetContextToken = doGetProcAddress(libole32, "CoGetContextToken")
	coGetCurrentLogicalThreadId = doGetProcAddress(libole32, "CoGetCurrentLogicalThreadId")
	coGetCurrentProcess = doGetProcAddress(libole32, "CoGetCurrentProcess")
	coGetDefaultContext = doGetProcAddress(libole32, "CoGetDefaultContext")
	coGetInstanceFromIStorage = doGetProcAddress(libole32, "CoGetInstanceFromIStorage")
	coGetInterfaceAndReleaseStream = doGetProcAddress(libole32, "CoGetInterfaceAndReleaseStream")
	coGetMalloc = doGetProcAddress(libole32, "CoGetMalloc")
	coGetMarshalSizeMax = doGetProcAddress(libole32, "CoGetMarshalSizeMax")
	coGetObject = doGetProcAddress(libole32, "CoGetObject")
	coGetObjectContext = doGetProcAddress(libole32, "CoGetObjectContext")
	coGetPSClsid = doGetProcAddress(libole32, "CoGetPSClsid")
	coGetStandardMarshal = doGetProcAddress(libole32, "CoGetStandardMarshal")
	coGetState = doGetProcAddress(libole32, "CoGetState")
	coGetTreatAsClass = doGetProcAddress(libole32, "CoGetTreatAsClass")
	coImpersonateClient = doGetProcAddress(libole32, "CoImpersonateClient")
	coInitialize = doGetProcAddress(libole32, "CoInitialize")
	coInitializeSecurity = doGetProcAddress(libole32, "CoInitializeSecurity")
	coInitializeWOW = doGetProcAddress(libole32, "CoInitializeWOW")
	coIsHandlerConnected = doGetProcAddress(libole32, "CoIsHandlerConnected")
	coIsOle1Class = doGetProcAddress(libole32, "CoIsOle1Class")
	coLoadLibrary = doGetProcAddress(libole32, "CoLoadLibrary")
	coLockObjectExternal = doGetProcAddress(libole32, "CoLockObjectExternal")
	coMarshalHresult = doGetProcAddress(libole32, "CoMarshalHresult")
	coMarshalInterThreadInterfaceInStream = doGetProcAddress(libole32, "CoMarshalInterThreadInterfaceInStream")
	coMarshalInterface = doGetProcAddress(libole32, "CoMarshalInterface")
	coQueryClientBlanket = doGetProcAddress(libole32, "CoQueryClientBlanket")
	coQueryProxyBlanket = doGetProcAddress(libole32, "CoQueryProxyBlanket")
	coRegisterChannelHook = doGetProcAddress(libole32, "CoRegisterChannelHook")
	coRegisterClassObject = doGetProcAddress(libole32, "CoRegisterClassObject")
	coRegisterInitializeSpy = doGetProcAddress(libole32, "CoRegisterInitializeSpy")
	coRegisterMallocSpy = doGetProcAddress(libole32, "CoRegisterMallocSpy")
	coRegisterMessageFilter = doGetProcAddress(libole32, "CoRegisterMessageFilter")
	coRegisterPSClsid = doGetProcAddress(libole32, "CoRegisterPSClsid")
	coReleaseMarshalData = doGetProcAddress(libole32, "CoReleaseMarshalData")
	coReleaseServerProcess = doGetProcAddress(libole32, "CoReleaseServerProcess")
	coResumeClassObjects = doGetProcAddress(libole32, "CoResumeClassObjects")
	coRevertToSelf = doGetProcAddress(libole32, "CoRevertToSelf")
	coRevokeInitializeSpy = doGetProcAddress(libole32, "CoRevokeInitializeSpy")
	coRevokeMallocSpy = doGetProcAddress(libole32, "CoRevokeMallocSpy")
	coSetProxyBlanket = doGetProcAddress(libole32, "CoSetProxyBlanket")
	coSetState = doGetProcAddress(libole32, "CoSetState")
	coSuspendClassObjects = doGetProcAddress(libole32, "CoSuspendClassObjects")
	coSwitchCallContext = doGetProcAddress(libole32, "CoSwitchCallContext")
	coTaskMemAlloc = doGetProcAddress(libole32, "CoTaskMemAlloc")
	coTaskMemFree = doGetProcAddress(libole32, "CoTaskMemFree")
	coTaskMemRealloc = doGetProcAddress(libole32, "CoTaskMemRealloc")
	coTreatAsClass = doGetProcAddress(libole32, "CoTreatAsClass")
	coUnmarshalHresult = doGetProcAddress(libole32, "CoUnmarshalHresult")
	coUnmarshalInterface = doGetProcAddress(libole32, "CoUnmarshalInterface")
	coWaitForMultipleHandles = doGetProcAddress(libole32, "CoWaitForMultipleHandles")
	createAntiMoniker = doGetProcAddress(libole32, "CreateAntiMoniker")
	createBindCtx = doGetProcAddress(libole32, "CreateBindCtx")
	createClassMoniker = doGetProcAddress(libole32, "CreateClassMoniker")
	createDataAdviseHolder = doGetProcAddress(libole32, "CreateDataAdviseHolder")
	createDataCache = doGetProcAddress(libole32, "CreateDataCache")
	createFileMoniker = doGetProcAddress(libole32, "CreateFileMoniker")
	createGenericComposite = doGetProcAddress(libole32, "CreateGenericComposite")
	createILockBytesOnHGlobal = doGetProcAddress(libole32, "CreateILockBytesOnHGlobal")
	createItemMoniker = doGetProcAddress(libole32, "CreateItemMoniker")
	createOleAdviseHolder = doGetProcAddress(libole32, "CreateOleAdviseHolder")
	createPointerMoniker = doGetProcAddress(libole32, "CreatePointerMoniker")
	createStreamOnHGlobal = doGetProcAddress(libole32, "CreateStreamOnHGlobal")
	dllDebugObjectRPCHook = doGetProcAddress(libole32, "DllDebugObjectRPCHook")
	doDragDrop = doGetProcAddress(libole32, "DoDragDrop")
	fmtIdToPropStgName = doGetProcAddress(libole32, "FmtIdToPropStgName")
	freePropVariantArray = doGetProcAddress(libole32, "FreePropVariantArray")
	getClassFile = doGetProcAddress(libole32, "GetClassFile")
	getConvertStg = doGetProcAddress(libole32, "GetConvertStg")
	getHGlobalFromILockBytes = doGetProcAddress(libole32, "GetHGlobalFromILockBytes")
	getHGlobalFromStream = doGetProcAddress(libole32, "GetHGlobalFromStream")
	getRunningObjectTable = doGetProcAddress(libole32, "GetRunningObjectTable")
	iIDFromString = doGetProcAddress(libole32, "IIDFromString")
	isAccelerator = doGetProcAddress(libole32, "IsAccelerator")
	isEqualGUID = doGetProcAddress(libole32, "IsEqualGUID")
	isValidInterface = doGetProcAddress(libole32, "IsValidInterface")
	mkParseDisplayName = doGetProcAddress(libole32, "MkParseDisplayName")
	monikerCommonPrefixWith = doGetProcAddress(libole32, "MonikerCommonPrefixWith")
	oleBuildVersion = doGetProcAddress(libole32, "OleBuildVersion")
	oleConvertIStorageToOLESTREAM = doGetProcAddress(libole32, "OleConvertIStorageToOLESTREAM")
	oleConvertOLESTREAMToIStorage = doGetProcAddress(libole32, "OleConvertOLESTREAMToIStorage")
	oleCreate = doGetProcAddress(libole32, "OleCreate")
	oleCreateDefaultHandler = doGetProcAddress(libole32, "OleCreateDefaultHandler")
	oleCreateEmbeddingHelper = doGetProcAddress(libole32, "OleCreateEmbeddingHelper")
	oleCreateFromData = doGetProcAddress(libole32, "OleCreateFromData")
	oleCreateFromDataEx = doGetProcAddress(libole32, "OleCreateFromDataEx")
	oleCreateFromFile = doGetProcAddress(libole32, "OleCreateFromFile")
	oleCreateFromFileEx = doGetProcAddress(libole32, "OleCreateFromFileEx")
	oleCreateLink = doGetProcAddress(libole32, "OleCreateLink")
	oleCreateLinkFromData = doGetProcAddress(libole32, "OleCreateLinkFromData")
	oleCreateLinkToFile = doGetProcAddress(libole32, "OleCreateLinkToFile")
	oleCreateMenuDescriptor = doGetProcAddress(libole32, "OleCreateMenuDescriptor")
	oleCreateStaticFromData = doGetProcAddress(libole32, "OleCreateStaticFromData")
	oleDestroyMenuDescriptor = doGetProcAddress(libole32, "OleDestroyMenuDescriptor")
	oleDoAutoConvert = doGetProcAddress(libole32, "OleDoAutoConvert")
	oleDraw = doGetProcAddress(libole32, "OleDraw")
	oleDuplicateData = doGetProcAddress(libole32, "OleDuplicateData")
	oleFlushClipboard = doGetProcAddress(libole32, "OleFlushClipboard")
	oleGetAutoConvert = doGetProcAddress(libole32, "OleGetAutoConvert")
	oleGetClipboard = doGetProcAddress(libole32, "OleGetClipboard")
	oleGetIconOfClass = doGetProcAddress(libole32, "OleGetIconOfClass")
	oleInitialize = doGetProcAddress(libole32, "OleInitialize")
	oleUninitialize = doGetProcAddress(libole32, "OleUninitialize")
	oleInitializeWOW = doGetProcAddress(libole32, "OleInitializeWOW")
	oleIsCurrentClipboard = doGetProcAddress(libole32, "OleIsCurrentClipboard")
	oleIsRunning = doGetProcAddress(libole32, "OleIsRunning")
	oleLoad = doGetProcAddress(libole32, "OleLoad")
	oleLoadFromStream = doGetProcAddress(libole32, "OleLoadFromStream")
	oleLockRunning = doGetProcAddress(libole32, "OleLockRunning")
	oleMetafilePictFromIconAndLabel = doGetProcAddress(libole32, "OleMetafilePictFromIconAndLabel")
	oleNoteObjectVisible = doGetProcAddress(libole32, "OleNoteObjectVisible")
	oleQueryCreateFromData = doGetProcAddress(libole32, "OleQueryCreateFromData")
	oleQueryLinkFromData = doGetProcAddress(libole32, "OleQueryLinkFromData")
	oleRegEnumVerbs = doGetProcAddress(libole32, "OleRegEnumVerbs")
	oleRegGetMiscStatus = doGetProcAddress(libole32, "OleRegGetMiscStatus")
	oleRegGetUserType = doGetProcAddress(libole32, "OleRegGetUserType")
	oleSave = doGetProcAddress(libole32, "OleSave")
	oleSaveToStream = doGetProcAddress(libole32, "OleSaveToStream")
	oleSetAutoConvert = doGetProcAddress(libole32, "OleSetAutoConvert")
	oleSetClipboard = doGetProcAddress(libole32, "OleSetClipboard")
	oleSetContainedObject = doGetProcAddress(libole32, "OleSetContainedObject")
	oleSetMenuDescriptor = doGetProcAddress(libole32, "OleSetMenuDescriptor")
	oleTranslateAccelerator = doGetProcAddress(libole32, "OleTranslateAccelerator")
	propStgNameToFmtId = doGetProcAddress(libole32, "PropStgNameToFmtId")
	propSysAllocString = doGetProcAddress(libole32, "PropSysAllocString")
	propSysFreeString = doGetProcAddress(libole32, "PropSysFreeString")
	propVariantClear = doGetProcAddress(libole32, "PropVariantClear")
	propVariantCopy = doGetProcAddress(libole32, "PropVariantCopy")
	readClassStg = doGetProcAddress(libole32, "ReadClassStg")
	readClassStm = doGetProcAddress(libole32, "ReadClassStm")
	readFmtUserTypeStg = doGetProcAddress(libole32, "ReadFmtUserTypeStg")
	registerDragDrop = doGetProcAddress(libole32, "RegisterDragDrop")
	releaseStgMedium = doGetProcAddress(libole32, "ReleaseStgMedium")
	revokeDragDrop = doGetProcAddress(libole32, "RevokeDragDrop")
	setConvertStg = doGetProcAddress(libole32, "SetConvertStg")
	stgCreateDocfile = doGetProcAddress(libole32, "StgCreateDocfile")
	stgCreateDocfileOnILockBytes = doGetProcAddress(libole32, "StgCreateDocfileOnILockBytes")
	stgCreatePropSetStg = doGetProcAddress(libole32, "StgCreatePropSetStg")
	stgCreatePropStg = doGetProcAddress(libole32, "StgCreatePropStg")
	stgIsStorageFile = doGetProcAddress(libole32, "StgIsStorageFile")
	stgIsStorageILockBytes = doGetProcAddress(libole32, "StgIsStorageILockBytes")
	stgOpenPropStg = doGetProcAddress(libole32, "StgOpenPropStg")
	stgSetTimes = doGetProcAddress(libole32, "StgSetTimes")
	stringFromCLSID = doGetProcAddress(libole32, "StringFromCLSID")
	stringFromGUID2 = doGetProcAddress(libole32, "StringFromGUID2")
	wdtpInterfacePointer_UserFree = doGetProcAddress(libole32, "WdtpInterfacePointer_UserFree")
	writeClassStg = doGetProcAddress(libole32, "WriteClassStg")
	writeClassStm = doGetProcAddress(libole32, "WriteClassStm")
	writeFmtUserTypeStg = doGetProcAddress(libole32, "WriteFmtUserTypeStg")
}

func CoGetInterceptor(iidIntercepted REFIID, punkOuter *IUnknown, iid REFIID, ppv uintptr) HRESULT {
	ret1 := syscall6(coGetInterceptor, 4,
		uintptr(unsafe.Pointer(iidIntercepted)),
		uintptr(unsafe.Pointer(punkOuter)),
		uintptr(unsafe.Pointer(iid)),
		ppv,
		0,
		0)
	return HRESULT(ret1)
}

func CoGetInterceptorFromTypeInfo(iidIntercepted REFIID, punkOuter *IUnknown, typeInfo *ITypeInfo, iid REFIID, ppv uintptr) HRESULT {
	ret1 := syscall6(coGetInterceptorFromTypeInfo, 5,
		uintptr(unsafe.Pointer(iidIntercepted)),
		uintptr(unsafe.Pointer(punkOuter)),
		uintptr(unsafe.Pointer(typeInfo)),
		uintptr(unsafe.Pointer(iid)),
		ppv,
		0)
	return HRESULT(ret1)
}

func PropVariantChangeType(ppropvarDest *PROPVARIANT, propvarSrc REFPROPVARIANT, flags PROPVAR_CHANGE_FLAGS, vt VARTYPE) HRESULT {
	ret1 := syscall6(propVariantChangeType, 4,
		uintptr(unsafe.Pointer(ppropvarDest)),
		uintptr(unsafe.Pointer(propvarSrc)),
		uintptr(flags),
		uintptr(vt),
		0,
		0)
	return HRESULT(ret1)
}

func BindMoniker(pmk LPMONIKER, grfOpt DWORD, riid REFIID, ppvResult *LPVOID) HRESULT {
	ret1 := syscall6(bindMoniker, 4,
		uintptr(unsafe.Pointer(pmk)),
		uintptr(grfOpt),
		uintptr(unsafe.Pointer(riid)),
		uintptr(unsafe.Pointer(ppvResult)),
		0,
		0)
	return HRESULT(ret1)
}

func CLSIDFromProgIDEx(progid /*const*/ LPCOLESTR, clsid *CLSID) HRESULT {
	ret1 := syscall3(cLSIDFromProgIDEx, 2,
		uintptr(unsafe.Pointer(progid)),
		uintptr(unsafe.Pointer(clsid)),
		0)
	return HRESULT(ret1)
}

func CLSIDFromString(idstr /*const*/ LPCOLESTR, id *CLSID) HRESULT {
	ret1 := syscall3(cLSIDFromString, 2,
		uintptr(unsafe.Pointer(idstr)),
		uintptr(unsafe.Pointer(id)),
		0)
	return HRESULT(ret1)
}

func CoAddRefServerProcess() ULONG {
	ret1 := syscall3(coAddRefServerProcess, 0,
		0,
		0,
		0)
	return ULONG(ret1)
}

func CoAllowSetForegroundWindow(pUnk *IUnknown, pvReserved uintptr) HRESULT {
	ret1 := syscall3(coAllowSetForegroundWindow, 2,
		uintptr(unsafe.Pointer(pUnk)),
		pvReserved,
		0)
	return HRESULT(ret1)
}

func CoBuildVersion() DWORD {
	ret1 := syscall3(coBuildVersion, 0,
		0,
		0,
		0)
	return DWORD(ret1)
}

func CoCopyProxy(pProxy *IUnknown, ppCopy **IUnknown) HRESULT {
	ret1 := syscall3(coCopyProxy, 2,
		uintptr(unsafe.Pointer(pProxy)),
		uintptr(unsafe.Pointer(ppCopy)),
		0)
	return HRESULT(ret1)
}

func CoCreateFreeThreadedMarshaler(punkOuter LPUNKNOWN, ppunkMarshal *LPUNKNOWN) HRESULT {
	ret1 := syscall3(coCreateFreeThreadedMarshaler, 2,
		uintptr(unsafe.Pointer(punkOuter)),
		uintptr(unsafe.Pointer(ppunkMarshal)),
		0)
	return HRESULT(ret1)
}

func CoCreateGuid(pguid *GUID) HRESULT {
	ret1 := syscall3(coCreateGuid, 1,
		uintptr(unsafe.Pointer(pguid)),
		0,
		0)
	return HRESULT(ret1)
}

func CoDisconnectObject(lpUnk LPUNKNOWN, reserved DWORD) HRESULT {
	ret1 := syscall3(coDisconnectObject, 2,
		uintptr(unsafe.Pointer(lpUnk)),
		uintptr(reserved),
		0)
	return HRESULT(ret1)
}

func CoFileTimeNow(lpFileTime *FILETIME) HRESULT {
	ret1 := syscall3(coFileTimeNow, 1,
		uintptr(unsafe.Pointer(lpFileTime)),
		0,
		0)
	return HRESULT(ret1)
}

func CoFreeAllLibraries() {
	syscall3(coFreeAllLibraries, 0,
		0,
		0,
		0)
}

func CoFreeLibrary(hLibrary HINSTANCE) {
	syscall3(coFreeLibrary, 1,
		uintptr(hLibrary),
		0,
		0)
}

func CoGetActivationState(guid GUID, unknown DWORD, unknown2 *uint32) HRESULT {
	ret1 := syscall6(coGetActivationState, 6,
		uintptr(guid.Data1),
		uintptr((uint32(guid.Data2)<<16)|uint32(guid.Data3)),
		uintptr((uint32(guid.Data4[0])<<24)|(uint32(guid.Data4[1]<<16))|(uint32(guid.Data4[2]<<8))|uint32(guid.Data4[3])),
		uintptr((uint32(guid.Data4[4])<<24)|(uint32(guid.Data4[5]<<16))|(uint32(guid.Data4[6]<<8))|uint32(guid.Data4[7])),
		uintptr(unknown),
		uintptr(unsafe.Pointer(unknown2)))
	return HRESULT(ret1)
}

func CoGetApartmentType(aType *APTTYPE, qualifier *APTTYPEQUALIFIER) HRESULT {
	ret1 := syscall3(coGetApartmentType, 2,
		uintptr(unsafe.Pointer(aType)),
		uintptr(unsafe.Pointer(qualifier)),
		0)
	return HRESULT(ret1)
}

func CoGetCallContext(riid REFIID, ppv uintptr) HRESULT {
	ret1 := syscall3(coGetCallContext, 2,
		uintptr(unsafe.Pointer(riid)),
		ppv,
		0)
	return HRESULT(ret1)
}

func CoGetCallState(unknown int32, unknown2 *uint32) HRESULT {
	ret1 := syscall3(coGetCallState, 2,
		uintptr(unknown),
		uintptr(unsafe.Pointer(unknown2)),
		0)
	return HRESULT(ret1)
}

func CoGetCallerTID(lpdwTID *uint32) HRESULT {
	ret1 := syscall3(coGetCallerTID, 1,
		uintptr(unsafe.Pointer(lpdwTID)),
		0,
		0)
	return HRESULT(ret1)
}

func CoGetContextToken(token *ULONG_PTR) HRESULT {
	ret1 := syscall3(coGetContextToken, 1,
		uintptr(unsafe.Pointer(token)),
		0,
		0)
	return HRESULT(ret1)
}

func CoGetCurrentLogicalThreadId(id *GUID) HRESULT {
	ret1 := syscall3(coGetCurrentLogicalThreadId, 1,
		uintptr(unsafe.Pointer(id)),
		0,
		0)
	return HRESULT(ret1)
}

func CoGetCurrentProcess() DWORD {
	ret1 := syscall3(coGetCurrentProcess, 0,
		0,
		0,
		0)
	return DWORD(ret1)
}

func CoGetDefaultContext(aType APTTYPE, riid REFIID, ppv *LPVOID) HRESULT {
	ret1 := syscall3(coGetDefaultContext, 3,
		uintptr(aType),
		uintptr(unsafe.Pointer(riid)),
		uintptr(unsafe.Pointer(ppv)))
	return HRESULT(ret1)
}

func CoGetInstanceFromIStorage(server_info *COSERVERINFO, rclsid *CLSID, outer *IUnknown, cls_context DWORD, storage *IStorage, count DWORD, results *MULTI_QI) HRESULT {
	ret1 := syscall9(coGetInstanceFromIStorage, 7,
		uintptr(unsafe.Pointer(server_info)),
		uintptr(unsafe.Pointer(rclsid)),
		uintptr(unsafe.Pointer(outer)),
		uintptr(cls_context),
		uintptr(unsafe.Pointer(storage)),
		uintptr(count),
		uintptr(unsafe.Pointer(results)),
		0,
		0)
	return HRESULT(ret1)
}

func CoGetInterfaceAndReleaseStream(pStm LPSTREAM, riid REFIID, ppv *LPVOID) HRESULT {
	ret1 := syscall3(coGetInterfaceAndReleaseStream, 3,
		uintptr(unsafe.Pointer(pStm)),
		uintptr(unsafe.Pointer(riid)),
		uintptr(unsafe.Pointer(ppv)))
	return HRESULT(ret1)
}

func CoGetMalloc(context DWORD, imalloc **IMalloc) HRESULT {
	ret1 := syscall3(coGetMalloc, 2,
		uintptr(context),
		uintptr(unsafe.Pointer(imalloc)),
		0)
	return HRESULT(ret1)
}

func CoGetMarshalSizeMax(pulSize *ULONG, riid REFIID, pUnk *IUnknown, dwDestContext DWORD, pvDestContext uintptr, mshlFlags DWORD) HRESULT {
	ret1 := syscall6(coGetMarshalSizeMax, 6,
		uintptr(unsafe.Pointer(pulSize)),
		uintptr(unsafe.Pointer(riid)),
		uintptr(unsafe.Pointer(pUnk)),
		uintptr(dwDestContext),
		pvDestContext,
		uintptr(mshlFlags))
	return HRESULT(ret1)
}

func CoGetObject(pszName string, pBindOptions *BIND_OPTS, riid REFIID, ppv uintptr) HRESULT {
	pszNameStr := unicode16FromString(pszName)
	ret1 := syscall6(coGetObject, 4,
		uintptr(unsafe.Pointer(&pszNameStr[0])),
		uintptr(unsafe.Pointer(pBindOptions)),
		uintptr(unsafe.Pointer(riid)),
		ppv,
		0,
		0)
	return HRESULT(ret1)
}

func CoGetObjectContext(riid REFIID, ppv uintptr) HRESULT {
	ret1 := syscall3(coGetObjectContext, 2,
		uintptr(unsafe.Pointer(riid)),
		ppv,
		0)
	return HRESULT(ret1)
}

func CoGetPSClsid(riid REFIID, pclsid *CLSID) HRESULT {
	ret1 := syscall3(coGetPSClsid, 2,
		uintptr(unsafe.Pointer(riid)),
		uintptr(unsafe.Pointer(pclsid)),
		0)
	return HRESULT(ret1)
}

func CoGetStandardMarshal(riid REFIID, pUnk *IUnknown, dwDestContext DWORD, pvDestContext LPVOID, mshlflags DWORD, ppMarshal *LPMARSHAL) HRESULT {
	ret1 := syscall6(coGetStandardMarshal, 6,
		uintptr(unsafe.Pointer(riid)),
		uintptr(unsafe.Pointer(pUnk)),
		uintptr(dwDestContext),
		uintptr(unsafe.Pointer(pvDestContext)),
		uintptr(mshlflags),
		uintptr(unsafe.Pointer(ppMarshal)))
	return HRESULT(ret1)
}

func CoGetState(ppv **IUnknown) HRESULT {
	ret1 := syscall3(coGetState, 1,
		uintptr(unsafe.Pointer(ppv)),
		0,
		0)
	return HRESULT(ret1)
}

func CoGetTreatAsClass(clsidOld /*const*/ REFCLSID, clsidNew *CLSID) HRESULT {
	ret1 := syscall3(coGetTreatAsClass, 2,
		uintptr(unsafe.Pointer(clsidOld)),
		uintptr(unsafe.Pointer(clsidNew)),
		0)
	return HRESULT(ret1)
}

func CoImpersonateClient() HRESULT {
	ret1 := syscall3(coImpersonateClient, 0,
		0,
		0,
		0)
	return HRESULT(ret1)
}

func CoInitialize(lpReserved LPVOID) HRESULT {
	ret1 := syscall3(coInitialize, 1,
		uintptr(unsafe.Pointer(lpReserved)),
		0,
		0)
	return HRESULT(ret1)
}

func CoInitializeSecurity(pSecDesc PSECURITY_DESCRIPTOR, cAuthSvc LONG, asAuthSvc *SOLE_AUTHENTICATION_SERVICE, pReserved1 uintptr, dwAuthnLevel DWORD, dwImpLevel DWORD, pReserved2 uintptr, dwCapabilities DWORD, pReserved3 uintptr) HRESULT {
	ret1 := syscall9(coInitializeSecurity, 9,
		uintptr(unsafe.Pointer(pSecDesc)),
		uintptr(cAuthSvc),
		uintptr(unsafe.Pointer(asAuthSvc)),
		pReserved1,
		uintptr(dwAuthnLevel),
		uintptr(dwImpLevel),
		pReserved2,
		uintptr(dwCapabilities),
		pReserved3)
	return HRESULT(ret1)
}

func CoInitializeWOW(x DWORD, y DWORD) HRESULT {
	ret1 := syscall3(coInitializeWOW, 2,
		uintptr(x),
		uintptr(y),
		0)
	return HRESULT(ret1)
}

func CoIsHandlerConnected(pUnk *IUnknown) bool {
	ret1 := syscall3(coIsHandlerConnected, 1,
		uintptr(unsafe.Pointer(pUnk)),
		0,
		0)
	return ret1 != 0
}

func CoIsOle1Class(clsid /*const*/ REFCLSID) bool {
	ret1 := syscall3(coIsOle1Class, 1,
		uintptr(unsafe.Pointer(clsid)),
		0,
		0)
	return ret1 != 0
}

func CoLoadLibrary(lpszLibName LPOLESTR, bAutoFree bool) HINSTANCE {
	ret1 := syscall3(coLoadLibrary, 2,
		uintptr(unsafe.Pointer(lpszLibName)),
		getUintptrFromBool(bAutoFree),
		0)
	return HINSTANCE(ret1)
}

func CoLockObjectExternal(pUnk LPUNKNOWN, fLock bool, fLastUnlockReleases bool) HRESULT {
	ret1 := syscall3(coLockObjectExternal, 3,
		uintptr(unsafe.Pointer(pUnk)),
		getUintptrFromBool(fLock),
		getUintptrFromBool(fLastUnlockReleases))
	return HRESULT(ret1)
}

func CoMarshalHresult(pStm LPSTREAM, hresult HRESULT) HRESULT {
	ret1 := syscall3(coMarshalHresult, 2,
		uintptr(unsafe.Pointer(pStm)),
		uintptr(hresult),
		0)
	return HRESULT(ret1)
}

func CoMarshalInterThreadInterfaceInStream(riid REFIID, pUnk LPUNKNOWN, ppStm *LPSTREAM) HRESULT {
	ret1 := syscall3(coMarshalInterThreadInterfaceInStream, 3,
		uintptr(unsafe.Pointer(riid)),
		uintptr(unsafe.Pointer(pUnk)),
		uintptr(unsafe.Pointer(ppStm)))
	return HRESULT(ret1)
}

func CoMarshalInterface(pStream *IStream, riid REFIID, pUnk *IUnknown, dwDestContext DWORD, pvDestContext uintptr, mshlFlags DWORD) HRESULT {
	ret1 := syscall6(coMarshalInterface, 6,
		uintptr(unsafe.Pointer(pStream)),
		uintptr(unsafe.Pointer(riid)),
		uintptr(unsafe.Pointer(pUnk)),
		uintptr(dwDestContext),
		pvDestContext,
		uintptr(mshlFlags))
	return HRESULT(ret1)
}

func CoQueryClientBlanket(pAuthnSvc *uint32, pAuthzSvc *uint32, pServerPrincName **OLECHAR, pAuthnLevel *uint32, pImpLevel *uint32, pPrivs *RPC_AUTHZ_HANDLE, pCapabilities *uint32) HRESULT {
	ret1 := syscall9(coQueryClientBlanket, 7,
		uintptr(unsafe.Pointer(pAuthnSvc)),
		uintptr(unsafe.Pointer(pAuthzSvc)),
		uintptr(unsafe.Pointer(pServerPrincName)),
		uintptr(unsafe.Pointer(pAuthnLevel)),
		uintptr(unsafe.Pointer(pImpLevel)),
		uintptr(unsafe.Pointer(pPrivs)),
		uintptr(unsafe.Pointer(pCapabilities)),
		0,
		0)
	return HRESULT(ret1)
}

func CoQueryProxyBlanket(pProxy *IUnknown, pAuthnSvc *uint32, pAuthzSvc *uint32, ppServerPrincName **OLECHAR, pAuthnLevel *uint32, pImpLevel *uint32, ppAuthInfo uintptr, pCapabilities *uint32) HRESULT {
	ret1 := syscall9(coQueryProxyBlanket, 8,
		uintptr(unsafe.Pointer(pProxy)),
		uintptr(unsafe.Pointer(pAuthnSvc)),
		uintptr(unsafe.Pointer(pAuthzSvc)),
		uintptr(unsafe.Pointer(ppServerPrincName)),
		uintptr(unsafe.Pointer(pAuthnLevel)),
		uintptr(unsafe.Pointer(pImpLevel)),
		ppAuthInfo,
		uintptr(unsafe.Pointer(pCapabilities)),
		0)
	return HRESULT(ret1)
}

func CoRegisterChannelHook(guidExtension REFGUID, pChannelHook *IChannelHook) HRESULT {
	ret1 := syscall3(coRegisterChannelHook, 2,
		uintptr(unsafe.Pointer(guidExtension)),
		uintptr(unsafe.Pointer(pChannelHook)),
		0)
	return HRESULT(ret1)
}

func CoRegisterClassObject(rclsid /*const*/ REFCLSID, pUnk LPUNKNOWN, dwClsContext DWORD, flags DWORD, lpdwRegister *uint32) HRESULT {
	ret1 := syscall6(coRegisterClassObject, 5,
		uintptr(unsafe.Pointer(rclsid)),
		uintptr(unsafe.Pointer(pUnk)),
		uintptr(dwClsContext),
		uintptr(flags),
		uintptr(unsafe.Pointer(lpdwRegister)),
		0)
	return HRESULT(ret1)
}

func CoRegisterInitializeSpy(spy *IInitializeSpy, cookie *ULARGE_INTEGER) HRESULT {
	ret1 := syscall3(coRegisterInitializeSpy, 2,
		uintptr(unsafe.Pointer(spy)),
		uintptr(unsafe.Pointer(cookie)),
		0)
	return HRESULT(ret1)
}

func CoRegisterMallocSpy(pMallocSpy LPMALLOCSPY) HRESULT {
	ret1 := syscall3(coRegisterMallocSpy, 1,
		uintptr(unsafe.Pointer(pMallocSpy)),
		0,
		0)
	return HRESULT(ret1)
}

func CoRegisterMessageFilter(lpMessageFilter LPMESSAGEFILTER, lplpMessageFilter *LPMESSAGEFILTER) HRESULT {
	ret1 := syscall3(coRegisterMessageFilter, 2,
		uintptr(unsafe.Pointer(lpMessageFilter)),
		uintptr(unsafe.Pointer(lplpMessageFilter)),
		0)
	return HRESULT(ret1)
}

func CoRegisterPSClsid(riid REFIID, rclsid /*const*/ REFCLSID) HRESULT {
	ret1 := syscall3(coRegisterPSClsid, 2,
		uintptr(unsafe.Pointer(riid)),
		uintptr(unsafe.Pointer(rclsid)),
		0)
	return HRESULT(ret1)
}

func CoReleaseMarshalData(pStream *IStream) HRESULT {
	ret1 := syscall3(coReleaseMarshalData, 1,
		uintptr(unsafe.Pointer(pStream)),
		0,
		0)
	return HRESULT(ret1)
}

func CoReleaseServerProcess() ULONG {
	ret1 := syscall3(coReleaseServerProcess, 0,
		0,
		0,
		0)
	return ULONG(ret1)
}

func CoResumeClassObjects() HRESULT {
	ret1 := syscall3(coResumeClassObjects, 0,
		0,
		0,
		0)
	return HRESULT(ret1)
}

func CoRevertToSelf() HRESULT {
	ret1 := syscall3(coRevertToSelf, 0,
		0,
		0,
		0)
	return HRESULT(ret1)
}

func CoRevokeInitializeSpy(cookie ULARGE_INTEGER) HRESULT {
	ret1 := syscall3(coRevokeInitializeSpy, 2,
		uintptr(*(*uint32)(unsafe.Pointer(&cookie))),
		uintptr(*(*uint32)(unsafe.Pointer(uintptr(unsafe.Pointer(&cookie)) + uintptr(4)))),
		0)
	return HRESULT(ret1)
}

func CoRevokeMallocSpy() HRESULT {
	ret1 := syscall3(coRevokeMallocSpy, 0,
		0,
		0,
		0)
	return HRESULT(ret1)
}

func CoSetProxyBlanket(pProxy *IUnknown, authnSvc DWORD, authzSvc DWORD, pServerPrincName *OLECHAR, authnLevel DWORD, impLevel DWORD, pAuthInfo uintptr, capabilities DWORD) HRESULT {
	ret1 := syscall9(coSetProxyBlanket, 8,
		uintptr(unsafe.Pointer(pProxy)),
		uintptr(authnSvc),
		uintptr(authzSvc),
		uintptr(unsafe.Pointer(pServerPrincName)),
		uintptr(authnLevel),
		uintptr(impLevel),
		pAuthInfo,
		uintptr(capabilities),
		0)
	return HRESULT(ret1)
}

func CoSetState(pv *IUnknown) HRESULT {
	ret1 := syscall3(coSetState, 1,
		uintptr(unsafe.Pointer(pv)),
		0,
		0)
	return HRESULT(ret1)
}

func CoSuspendClassObjects() HRESULT {
	ret1 := syscall3(coSuspendClassObjects, 0,
		0,
		0,
		0)
	return HRESULT(ret1)
}

func CoSwitchCallContext(pObject *IUnknown, ppOldObject **IUnknown) HRESULT {
	ret1 := syscall3(coSwitchCallContext, 2,
		uintptr(unsafe.Pointer(pObject)),
		uintptr(unsafe.Pointer(ppOldObject)),
		0)
	return HRESULT(ret1)
}

func CoTaskMemAlloc(size SIZE_T) LPVOID {
	ret1 := syscall3(coTaskMemAlloc, 1,
		uintptr(size),
		0,
		0)
	return (LPVOID)(unsafe.Pointer(ret1))
}

func CoTaskMemFree(ptr LPVOID) {
	syscall3(coTaskMemFree, 1,
		uintptr(unsafe.Pointer(ptr)),
		0,
		0)
}

func CoTaskMemRealloc(pvOld LPVOID, size SIZE_T) LPVOID {
	ret1 := syscall3(coTaskMemRealloc, 2,
		uintptr(unsafe.Pointer(pvOld)),
		uintptr(size),
		0)
	return (LPVOID)(unsafe.Pointer(ret1))
}

func CoTreatAsClass(clsidOld /*const*/ REFCLSID, clsidNew /*const*/ REFCLSID) HRESULT {
	ret1 := syscall3(coTreatAsClass, 2,
		uintptr(unsafe.Pointer(clsidOld)),
		uintptr(unsafe.Pointer(clsidNew)),
		0)
	return HRESULT(ret1)
}

func CoUnmarshalHresult(pStm LPSTREAM, phresult *HRESULT) HRESULT {
	ret1 := syscall3(coUnmarshalHresult, 2,
		uintptr(unsafe.Pointer(pStm)),
		uintptr(unsafe.Pointer(phresult)),
		0)
	return HRESULT(ret1)
}

func CoUnmarshalInterface(pStream *IStream, riid REFIID, ppv *LPVOID) HRESULT {
	ret1 := syscall3(coUnmarshalInterface, 3,
		uintptr(unsafe.Pointer(pStream)),
		uintptr(unsafe.Pointer(riid)),
		uintptr(unsafe.Pointer(ppv)))
	return HRESULT(ret1)
}

func CoWaitForMultipleHandles(dwFlags DWORD, dwTimeout DWORD, cHandles ULONG, pHandles *HANDLE, lpdwindex *uint32) HRESULT {
	ret1 := syscall6(coWaitForMultipleHandles, 5,
		uintptr(dwFlags),
		uintptr(dwTimeout),
		uintptr(cHandles),
		uintptr(unsafe.Pointer(pHandles)),
		uintptr(unsafe.Pointer(lpdwindex)),
		0)
	return HRESULT(ret1)
}

func CreateAntiMoniker(ppmk **IMoniker) HRESULT {
	ret1 := syscall3(createAntiMoniker, 1,
		uintptr(unsafe.Pointer(ppmk)),
		0,
		0)
	return HRESULT(ret1)
}

func CreateBindCtx(reserved DWORD, ppbc *LPBC) HRESULT {
	ret1 := syscall3(createBindCtx, 2,
		uintptr(reserved),
		uintptr(unsafe.Pointer(ppbc)),
		0)
	return HRESULT(ret1)
}

func CreateClassMoniker(rclsid /*const*/ REFCLSID, ppmk **IMoniker) HRESULT {
	ret1 := syscall3(createClassMoniker, 2,
		uintptr(unsafe.Pointer(rclsid)),
		uintptr(unsafe.Pointer(ppmk)),
		0)
	return HRESULT(ret1)
}

func CreateDataAdviseHolder(ppDAHolder **IDataAdviseHolder) HRESULT {
	ret1 := syscall3(createDataAdviseHolder, 1,
		uintptr(unsafe.Pointer(ppDAHolder)),
		0,
		0)
	return HRESULT(ret1)
}

func CreateDataCache(pUnkOuter LPUNKNOWN, rclsid /*const*/ REFCLSID, riid REFIID, ppvObj *LPVOID) HRESULT {
	ret1 := syscall6(createDataCache, 4,
		uintptr(unsafe.Pointer(pUnkOuter)),
		uintptr(unsafe.Pointer(rclsid)),
		uintptr(unsafe.Pointer(riid)),
		uintptr(unsafe.Pointer(ppvObj)),
		0,
		0)
	return HRESULT(ret1)
}

func CreateFileMoniker(lpszPathName /*const*/ LPCOLESTR, ppmk **IMoniker) HRESULT {
	ret1 := syscall3(createFileMoniker, 2,
		uintptr(unsafe.Pointer(lpszPathName)),
		uintptr(unsafe.Pointer(ppmk)),
		0)
	return HRESULT(ret1)
}

func CreateGenericComposite(pmkFirst *IMoniker, pmkRest *IMoniker, ppmkComposite **IMoniker) HRESULT {
	ret1 := syscall3(createGenericComposite, 3,
		uintptr(unsafe.Pointer(pmkFirst)),
		uintptr(unsafe.Pointer(pmkRest)),
		uintptr(unsafe.Pointer(ppmkComposite)))
	return HRESULT(ret1)
}

func CreateILockBytesOnHGlobal(global HGLOBAL, delete_on_release bool, ret **ILockBytes) HRESULT {
	ret1 := syscall3(createILockBytesOnHGlobal, 3,
		uintptr(global),
		getUintptrFromBool(delete_on_release),
		uintptr(unsafe.Pointer(ret)))
	return HRESULT(ret1)
}

func CreateItemMoniker(lpszDelim /*const*/ LPCOLESTR, lpszItem /*const*/ LPCOLESTR, ppmk **IMoniker) HRESULT {
	ret1 := syscall3(createItemMoniker, 3,
		uintptr(unsafe.Pointer(lpszDelim)),
		uintptr(unsafe.Pointer(lpszItem)),
		uintptr(unsafe.Pointer(ppmk)))
	return HRESULT(ret1)
}

func CreateOleAdviseHolder(ppOAHolder **IOleAdviseHolder) HRESULT {
	ret1 := syscall3(createOleAdviseHolder, 1,
		uintptr(unsafe.Pointer(ppOAHolder)),
		0,
		0)
	return HRESULT(ret1)
}

func CreatePointerMoniker(punk LPUNKNOWN, ppmk *LPMONIKER) HRESULT {
	ret1 := syscall3(createPointerMoniker, 2,
		uintptr(unsafe.Pointer(punk)),
		uintptr(unsafe.Pointer(ppmk)),
		0)
	return HRESULT(ret1)
}

func CreateStreamOnHGlobal(hGlobal HGLOBAL, fDeleteOnRelease bool, ppstm *LPSTREAM) HRESULT {
	ret1 := syscall3(createStreamOnHGlobal, 3,
		uintptr(hGlobal),
		getUintptrFromBool(fDeleteOnRelease),
		uintptr(unsafe.Pointer(ppstm)))
	return HRESULT(ret1)
}

func DllDebugObjectRPCHook(b bool, dummy uintptr) bool {
	ret1 := syscall3(dllDebugObjectRPCHook, 2,
		getUintptrFromBool(b),
		dummy,
		0)
	return ret1 != 0
}

func DoDragDrop(pDataObject *IDataObject, pDropSource *IDropSource, dwOKEffect DWORD, pdwEffect *uint32) HRESULT {
	ret1 := syscall6(doDragDrop, 4,
		uintptr(unsafe.Pointer(pDataObject)),
		uintptr(unsafe.Pointer(pDropSource)),
		uintptr(dwOKEffect),
		uintptr(unsafe.Pointer(pdwEffect)),
		0,
		0)
	return HRESULT(ret1)
}

func FmtIdToPropStgName(rfmtid /*const*/ *FMTID, str LPOLESTR) HRESULT {
	ret1 := syscall3(fmtIdToPropStgName, 2,
		uintptr(unsafe.Pointer(rfmtid)),
		uintptr(unsafe.Pointer(str)),
		0)
	return HRESULT(ret1)
}

func FreePropVariantArray(cVariants ULONG, rgvars *PROPVARIANT) HRESULT {
	ret1 := syscall3(freePropVariantArray, 2,
		uintptr(cVariants),
		uintptr(unsafe.Pointer(rgvars)),
		0)
	return HRESULT(ret1)
}

func GetClassFile(filePathName /*const*/ LPCOLESTR, pclsid *CLSID) HRESULT {
	ret1 := syscall3(getClassFile, 2,
		uintptr(unsafe.Pointer(filePathName)),
		uintptr(unsafe.Pointer(pclsid)),
		0)
	return HRESULT(ret1)
}

func GetConvertStg(stg *IStorage) HRESULT {
	ret1 := syscall3(getConvertStg, 1,
		uintptr(unsafe.Pointer(stg)),
		0,
		0)
	return HRESULT(ret1)
}

func GetHGlobalFromILockBytes(iface *ILockBytes, phglobal *HGLOBAL) HRESULT {
	ret1 := syscall3(getHGlobalFromILockBytes, 2,
		uintptr(unsafe.Pointer(iface)),
		uintptr(unsafe.Pointer(phglobal)),
		0)
	return HRESULT(ret1)
}

func GetHGlobalFromStream(pstm *IStream, phglobal *HGLOBAL) HRESULT {
	ret1 := syscall3(getHGlobalFromStream, 2,
		uintptr(unsafe.Pointer(pstm)),
		uintptr(unsafe.Pointer(phglobal)),
		0)
	return HRESULT(ret1)
}

func GetRunningObjectTable(reserved DWORD, pprot *LPRUNNINGOBJECTTABLE) HRESULT {
	ret1 := syscall3(getRunningObjectTable, 2,
		uintptr(reserved),
		uintptr(unsafe.Pointer(pprot)),
		0)
	return HRESULT(ret1)
}

func IIDFromString(s /*const*/ LPCOLESTR, iid *IID) HRESULT {
	ret1 := syscall3(iIDFromString, 2,
		uintptr(unsafe.Pointer(s)),
		uintptr(unsafe.Pointer(iid)),
		0)
	return HRESULT(ret1)
}

func IsAccelerator(hAccel HACCEL, cAccelEntries int32, lpMsg *MSG, lpwCmd *WORD) bool {
	ret1 := syscall6(isAccelerator, 4,
		uintptr(hAccel),
		uintptr(cAccelEntries),
		uintptr(unsafe.Pointer(lpMsg)),
		uintptr(unsafe.Pointer(lpwCmd)),
		0,
		0)
	return ret1 != 0
}

func IsEqualGUID(rguid1 REFGUID, rguid2 REFGUID) bool {
	ret1 := syscall3(isEqualGUID, 2,
		uintptr(unsafe.Pointer(rguid1)),
		uintptr(unsafe.Pointer(rguid2)),
		0)
	return ret1 != 0
}

func IsValidInterface(punk LPUNKNOWN) bool {
	ret1 := syscall3(isValidInterface, 1,
		uintptr(unsafe.Pointer(punk)),
		0,
		0)
	return ret1 != 0
}

func MkParseDisplayName(pbc LPBC, szDisplayName /*const*/ LPCOLESTR, pchEaten *uint32, ppmk *LPMONIKER) HRESULT {
	ret1 := syscall6(mkParseDisplayName, 4,
		uintptr(unsafe.Pointer(pbc)),
		uintptr(unsafe.Pointer(szDisplayName)),
		uintptr(unsafe.Pointer(pchEaten)),
		uintptr(unsafe.Pointer(ppmk)),
		0,
		0)
	return HRESULT(ret1)
}

func MonikerCommonPrefixWith(pmkThis *IMoniker, pmkOther *IMoniker, ppmkCommon **IMoniker) HRESULT {
	ret1 := syscall3(monikerCommonPrefixWith, 3,
		uintptr(unsafe.Pointer(pmkThis)),
		uintptr(unsafe.Pointer(pmkOther)),
		uintptr(unsafe.Pointer(ppmkCommon)))
	return HRESULT(ret1)
}

func OleBuildVersion() DWORD {
	ret1 := syscall3(oleBuildVersion, 0,
		0,
		0,
		0)
	return DWORD(ret1)
}

func OleConvertIStorageToOLESTREAM(pstg LPSTORAGE, pOleStream LPOLESTREAM) HRESULT {
	ret1 := syscall3(oleConvertIStorageToOLESTREAM, 2,
		uintptr(unsafe.Pointer(pstg)),
		uintptr(unsafe.Pointer(pOleStream)),
		0)
	return HRESULT(ret1)
}

func OleConvertOLESTREAMToIStorage(pOleStream LPOLESTREAM, pstg LPSTORAGE, ptd /*const*/ *DVTARGETDEVICE) HRESULT {
	ret1 := syscall3(oleConvertOLESTREAMToIStorage, 3,
		uintptr(unsafe.Pointer(pOleStream)),
		uintptr(unsafe.Pointer(pstg)),
		uintptr(unsafe.Pointer(ptd)))
	return HRESULT(ret1)
}

func OleCreate(rclsid /*const*/ REFCLSID, riid REFIID, renderopt DWORD, pFormatEtc *FORMATETC, pClientSite LPOLECLIENTSITE, pStg LPSTORAGE, ppvObj *LPVOID) HRESULT {
	ret1 := syscall9(oleCreate, 7,
		uintptr(unsafe.Pointer(rclsid)),
		uintptr(unsafe.Pointer(riid)),
		uintptr(renderopt),
		uintptr(unsafe.Pointer(pFormatEtc)),
		uintptr(unsafe.Pointer(pClientSite)),
		uintptr(unsafe.Pointer(pStg)),
		uintptr(unsafe.Pointer(ppvObj)),
		0,
		0)
	return HRESULT(ret1)
}

func OleCreateDefaultHandler(clsid /*const*/ REFCLSID, pUnkOuter LPUNKNOWN, riid REFIID, ppvObj *LPVOID) HRESULT {
	ret1 := syscall6(oleCreateDefaultHandler, 4,
		uintptr(unsafe.Pointer(clsid)),
		uintptr(unsafe.Pointer(pUnkOuter)),
		uintptr(unsafe.Pointer(riid)),
		uintptr(unsafe.Pointer(ppvObj)),
		0,
		0)
	return HRESULT(ret1)
}

func OleCreateEmbeddingHelper(clsid /*const*/ REFCLSID, pUnkOuter LPUNKNOWN, flags DWORD, pCF *IClassFactory, riid REFIID, ppvObj *LPVOID) HRESULT {
	ret1 := syscall6(oleCreateEmbeddingHelper, 6,
		uintptr(unsafe.Pointer(clsid)),
		uintptr(unsafe.Pointer(pUnkOuter)),
		uintptr(flags),
		uintptr(unsafe.Pointer(pCF)),
		uintptr(unsafe.Pointer(riid)),
		uintptr(unsafe.Pointer(ppvObj)))
	return HRESULT(ret1)
}

func OleCreateFromData(data LPDATAOBJECT, iid REFIID, renderopt DWORD, fmt *FORMATETC, client_site LPOLECLIENTSITE, stg LPSTORAGE, obj *LPVOID) HRESULT {
	ret1 := syscall9(oleCreateFromData, 7,
		uintptr(unsafe.Pointer(data)),
		uintptr(unsafe.Pointer(iid)),
		uintptr(renderopt),
		uintptr(unsafe.Pointer(fmt)),
		uintptr(unsafe.Pointer(client_site)),
		uintptr(unsafe.Pointer(stg)),
		uintptr(unsafe.Pointer(obj)),
		0,
		0)
	return HRESULT(ret1)
}

func OleCreateFromDataEx(data *IDataObject, iid REFIID, flags DWORD, renderopt DWORD, num_cache_fmts ULONG, adv_flags *uint32, cache_fmts *FORMATETC, sink *IAdviseSink, conns *uint32, client_site *IOleClientSite, stg *IStorage, obj uintptr) HRESULT {
	ret1 := syscall12(oleCreateFromDataEx, 12,
		uintptr(unsafe.Pointer(data)),
		uintptr(unsafe.Pointer(iid)),
		uintptr(flags),
		uintptr(renderopt),
		uintptr(num_cache_fmts),
		uintptr(unsafe.Pointer(adv_flags)),
		uintptr(unsafe.Pointer(cache_fmts)),
		uintptr(unsafe.Pointer(sink)),
		uintptr(unsafe.Pointer(conns)),
		uintptr(unsafe.Pointer(client_site)),
		uintptr(unsafe.Pointer(stg)),
		obj)
	return HRESULT(ret1)
}

func OleCreateFromFile(clsid /*const*/ REFCLSID, filename /*const*/ *OLECHAR, iid REFIID, renderopt DWORD, fmt *FORMATETC, client_site *IOleClientSite, storage *IStorage, obj uintptr) HRESULT {
	ret1 := syscall9(oleCreateFromFile, 8,
		uintptr(unsafe.Pointer(clsid)),
		uintptr(unsafe.Pointer(filename)),
		uintptr(unsafe.Pointer(iid)),
		uintptr(renderopt),
		uintptr(unsafe.Pointer(fmt)),
		uintptr(unsafe.Pointer(client_site)),
		uintptr(unsafe.Pointer(storage)),
		obj,
		0)
	return HRESULT(ret1)
}

func OleCreateFromFileEx(clsid /*const*/ REFCLSID, filename /*const*/ *OLECHAR, iid REFIID, flags DWORD, renderopt DWORD, num_fmts ULONG, adv_flags *uint32, fmts *FORMATETC, sink *IAdviseSink, conns *uint32, client_site *IOleClientSite, stg *IStorage, obj uintptr) HRESULT {
	ret1 := syscall15(oleCreateFromFileEx, 13,
		uintptr(unsafe.Pointer(clsid)),
		uintptr(unsafe.Pointer(filename)),
		uintptr(unsafe.Pointer(iid)),
		uintptr(flags),
		uintptr(renderopt),
		uintptr(num_fmts),
		uintptr(unsafe.Pointer(adv_flags)),
		uintptr(unsafe.Pointer(fmts)),
		uintptr(unsafe.Pointer(sink)),
		uintptr(unsafe.Pointer(conns)),
		uintptr(unsafe.Pointer(client_site)),
		uintptr(unsafe.Pointer(stg)),
		obj,
		0,
		0)
	return HRESULT(ret1)
}

func OleCreateLink(pmkLinkSrc LPMONIKER, riid REFIID, renderopt DWORD, lpFormatEtc *FORMATETC, pClientSite LPOLECLIENTSITE, pStg LPSTORAGE, ppvObj *LPVOID) HRESULT {
	ret1 := syscall9(oleCreateLink, 7,
		uintptr(unsafe.Pointer(pmkLinkSrc)),
		uintptr(unsafe.Pointer(riid)),
		uintptr(renderopt),
		uintptr(unsafe.Pointer(lpFormatEtc)),
		uintptr(unsafe.Pointer(pClientSite)),
		uintptr(unsafe.Pointer(pStg)),
		uintptr(unsafe.Pointer(ppvObj)),
		0,
		0)
	return HRESULT(ret1)
}

func OleCreateLinkFromData(data *IDataObject, iid REFIID, renderopt DWORD, fmt *FORMATETC, client_site *IOleClientSite, stg *IStorage, obj uintptr) HRESULT {
	ret1 := syscall9(oleCreateLinkFromData, 7,
		uintptr(unsafe.Pointer(data)),
		uintptr(unsafe.Pointer(iid)),
		uintptr(renderopt),
		uintptr(unsafe.Pointer(fmt)),
		uintptr(unsafe.Pointer(client_site)),
		uintptr(unsafe.Pointer(stg)),
		obj,
		0,
		0)
	return HRESULT(ret1)
}

func OleCreateLinkToFile(lpszFileName /*const*/ LPCOLESTR, riid REFIID, renderopt DWORD, lpFormatEtc *FORMATETC, pClientSite LPOLECLIENTSITE, pStg LPSTORAGE, ppvObj *LPVOID) HRESULT {
	ret1 := syscall9(oleCreateLinkToFile, 7,
		uintptr(unsafe.Pointer(lpszFileName)),
		uintptr(unsafe.Pointer(riid)),
		uintptr(renderopt),
		uintptr(unsafe.Pointer(lpFormatEtc)),
		uintptr(unsafe.Pointer(pClientSite)),
		uintptr(unsafe.Pointer(pStg)),
		uintptr(unsafe.Pointer(ppvObj)),
		0,
		0)
	return HRESULT(ret1)
}

func OleCreateMenuDescriptor(hmenuCombined HMENU, lpMenuWidths LPOLEMENUGROUPWIDTHS) HOLEMENU {
	ret1 := syscall3(oleCreateMenuDescriptor, 2,
		uintptr(hmenuCombined),
		uintptr(unsafe.Pointer(lpMenuWidths)),
		0)
	return HOLEMENU(ret1)
}

func OleCreateStaticFromData(data *IDataObject, iid REFIID, renderopt DWORD, fmt *FORMATETC, client_site *IOleClientSite, stg *IStorage, obj uintptr) HRESULT {
	ret1 := syscall9(oleCreateStaticFromData, 7,
		uintptr(unsafe.Pointer(data)),
		uintptr(unsafe.Pointer(iid)),
		uintptr(renderopt),
		uintptr(unsafe.Pointer(fmt)),
		uintptr(unsafe.Pointer(client_site)),
		uintptr(unsafe.Pointer(stg)),
		obj,
		0,
		0)
	return HRESULT(ret1)
}

func OleDestroyMenuDescriptor(hmenuDescriptor HOLEMENU) HRESULT {
	ret1 := syscall3(oleDestroyMenuDescriptor, 1,
		uintptr(hmenuDescriptor),
		0,
		0)
	return HRESULT(ret1)
}

func OleDoAutoConvert(pStg LPSTORAGE, pClsidNew *CLSID) HRESULT {
	ret1 := syscall3(oleDoAutoConvert, 2,
		uintptr(unsafe.Pointer(pStg)),
		uintptr(unsafe.Pointer(pClsidNew)),
		0)
	return HRESULT(ret1)
}

func OleDraw(pUnk *IUnknown, dwAspect DWORD, hdcDraw HDC, rect /*const*/ *RECT) HRESULT {
	ret1 := syscall6(oleDraw, 4,
		uintptr(unsafe.Pointer(pUnk)),
		uintptr(dwAspect),
		uintptr(hdcDraw),
		uintptr(unsafe.Pointer(rect)),
		0,
		0)
	return HRESULT(ret1)
}

func OleDuplicateData(hSrc HANDLE, cfFormat CLIPFORMAT, uiFlags UINT) HANDLE {
	ret1 := syscall3(oleDuplicateData, 3,
		uintptr(hSrc),
		uintptr(cfFormat),
		uintptr(uiFlags))
	return HANDLE(ret1)
}

func OleFlushClipboard() HRESULT {
	ret1 := syscall3(oleFlushClipboard, 0,
		0,
		0,
		0)
	return HRESULT(ret1)
}

func OleGetAutoConvert(clsidOld /*const*/ REFCLSID, pClsidNew *CLSID) HRESULT {
	ret1 := syscall3(oleGetAutoConvert, 2,
		uintptr(unsafe.Pointer(clsidOld)),
		uintptr(unsafe.Pointer(pClsidNew)),
		0)
	return HRESULT(ret1)
}

func OleGetClipboard(obj **IDataObject) HRESULT {
	ret1 := syscall3(oleGetClipboard, 1,
		uintptr(unsafe.Pointer(obj)),
		0,
		0)
	return HRESULT(ret1)
}

func OleGetIconOfClass(rclsid /*const*/ REFCLSID, lpszLabel LPOLESTR, fUseTypeAsLabel bool) HGLOBAL {
	ret1 := syscall3(oleGetIconOfClass, 3,
		uintptr(unsafe.Pointer(rclsid)),
		uintptr(unsafe.Pointer(lpszLabel)),
		getUintptrFromBool(fUseTypeAsLabel))
	return HGLOBAL(ret1)
}

func OleInitialize(x DWORD) HRESULT {
	ret1 := syscall3(oleInitialize, 1,
		uintptr(x),
		0,
		0)
	return HRESULT(ret1)
}
func OleUninitialize() HRESULT {
	ret1 := syscall3(oleUninitialize, 1,
		0,
		0,
		0)
	return HRESULT(ret1)
}
func OleInitializeWOW(x DWORD, y DWORD) HRESULT {
	ret1 := syscall3(oleInitializeWOW, 2,
		uintptr(x),
		uintptr(y),
		0)
	return HRESULT(ret1)
}
func OleIsCurrentClipboard(data *IDataObject) HRESULT {
	ret1 := syscall3(oleIsCurrentClipboard, 1,
		uintptr(unsafe.Pointer(data)),
		0,
		0)
	return HRESULT(ret1)
}

func OleIsRunning(object LPOLEOBJECT) bool {
	ret1 := syscall3(oleIsRunning, 1,
		uintptr(unsafe.Pointer(object)),
		0,
		0)
	return ret1 != 0
}

func OleLoad(pStg LPSTORAGE, riid REFIID, pClientSite LPOLECLIENTSITE, ppvObj *LPVOID) HRESULT {
	ret1 := syscall6(oleLoad, 4,
		uintptr(unsafe.Pointer(pStg)),
		uintptr(unsafe.Pointer(riid)),
		uintptr(unsafe.Pointer(pClientSite)),
		uintptr(unsafe.Pointer(ppvObj)),
		0,
		0)
	return HRESULT(ret1)
}

func OleLoadFromStream(pStm *IStream, iidInterface REFIID, ppvObj uintptr) HRESULT {
	ret1 := syscall3(oleLoadFromStream, 3,
		uintptr(unsafe.Pointer(pStm)),
		uintptr(unsafe.Pointer(iidInterface)),
		ppvObj)
	return HRESULT(ret1)
}

func OleLockRunning(pUnknown LPUNKNOWN, fLock bool, fLastUnlockCloses bool) HRESULT {
	ret1 := syscall3(oleLockRunning, 3,
		uintptr(unsafe.Pointer(pUnknown)),
		getUintptrFromBool(fLock),
		getUintptrFromBool(fLastUnlockCloses))
	return HRESULT(ret1)
}

func OleMetafilePictFromIconAndLabel(hIcon HICON, lpszLabel LPOLESTR, lpszSourceFile LPOLESTR, iIconIndex UINT) HGLOBAL {
	ret1 := syscall6(oleMetafilePictFromIconAndLabel, 4,
		uintptr(hIcon),
		uintptr(unsafe.Pointer(lpszLabel)),
		uintptr(unsafe.Pointer(lpszSourceFile)),
		uintptr(iIconIndex),
		0,
		0)
	return HGLOBAL(ret1)
}

func OleNoteObjectVisible(pUnknown LPUNKNOWN, bVisible bool) HRESULT {
	ret1 := syscall3(oleNoteObjectVisible, 2,
		uintptr(unsafe.Pointer(pUnknown)),
		getUintptrFromBool(bVisible),
		0)
	return HRESULT(ret1)
}

func OleQueryCreateFromData(data *IDataObject) HRESULT {
	ret1 := syscall3(oleQueryCreateFromData, 1,
		uintptr(unsafe.Pointer(data)),
		0,
		0)
	return HRESULT(ret1)
}

func OleQueryLinkFromData(pSrcDataObject *IDataObject) HRESULT {
	ret1 := syscall3(oleQueryLinkFromData, 1,
		uintptr(unsafe.Pointer(pSrcDataObject)),
		0,
		0)
	return HRESULT(ret1)
}

func OleRegEnumVerbs(clsid /*const*/ REFCLSID, ppenum *LPENUMOLEVERB) HRESULT {
	ret1 := syscall3(oleRegEnumVerbs, 2,
		uintptr(unsafe.Pointer(clsid)),
		uintptr(unsafe.Pointer(ppenum)),
		0)
	return HRESULT(ret1)
}

func OleRegGetMiscStatus(clsid /*const*/ REFCLSID, dwAspect DWORD, pdwStatus *uint32) HRESULT {
	ret1 := syscall3(oleRegGetMiscStatus, 3,
		uintptr(unsafe.Pointer(clsid)),
		uintptr(dwAspect),
		uintptr(unsafe.Pointer(pdwStatus)))
	return HRESULT(ret1)
}

func OleRegGetUserType(clsid /*const*/ REFCLSID, form DWORD, usertype *LPOLESTR) HRESULT {
	ret1 := syscall3(oleRegGetUserType, 3,
		uintptr(unsafe.Pointer(clsid)),
		uintptr(form),
		uintptr(unsafe.Pointer(usertype)))
	return HRESULT(ret1)
}

func OleSave(pPS LPPERSISTSTORAGE, pStg LPSTORAGE, fSameAsLoad bool) HRESULT {
	ret1 := syscall3(oleSave, 3,
		uintptr(unsafe.Pointer(pPS)),
		uintptr(unsafe.Pointer(pStg)),
		getUintptrFromBool(fSameAsLoad))
	return HRESULT(ret1)
}

func OleSaveToStream(pPStm *IPersistStream, pStm *IStream) HRESULT {
	ret1 := syscall3(oleSaveToStream, 2,
		uintptr(unsafe.Pointer(pPStm)),
		uintptr(unsafe.Pointer(pStm)),
		0)
	return HRESULT(ret1)
}

func OleSetAutoConvert(clsidOld /*const*/ REFCLSID, clsidNew /*const*/ REFCLSID) HRESULT {
	ret1 := syscall3(oleSetAutoConvert, 2,
		uintptr(unsafe.Pointer(clsidOld)),
		uintptr(unsafe.Pointer(clsidNew)),
		0)
	return HRESULT(ret1)
}

func OleSetClipboard(data *IDataObject) UINT {
	ret1 := syscall3(oleSetClipboard, 1,
		uintptr(unsafe.Pointer(data)),
		0,
		0)

	return UINT(ret1)
}

func OleSetContainedObject(pUnknown LPUNKNOWN, fContained bool) HRESULT {
	ret1 := syscall3(oleSetContainedObject, 2,
		uintptr(unsafe.Pointer(pUnknown)),
		getUintptrFromBool(fContained),
		0)
	return HRESULT(ret1)
}

func OleSetMenuDescriptor(hOleMenu HOLEMENU, hwndFrame HWND, hwndActiveObject HWND, lpFrame LPOLEINPLACEFRAME, lpActiveObject LPOLEINPLACEACTIVEOBJECT) HRESULT {
	ret1 := syscall6(oleSetMenuDescriptor, 5,
		uintptr(hOleMenu),
		uintptr(hwndFrame),
		uintptr(hwndActiveObject),
		uintptr(unsafe.Pointer(lpFrame)),
		uintptr(unsafe.Pointer(lpActiveObject)),
		0)
	return HRESULT(ret1)
}

func OleTranslateAccelerator(lpFrame LPOLEINPLACEFRAME, lpFrameInfo LPOLEINPLACEFRAMEINFO, lpmsg *MSG) HRESULT {
	ret1 := syscall3(oleTranslateAccelerator, 3,
		uintptr(unsafe.Pointer(lpFrame)),
		uintptr(unsafe.Pointer(lpFrameInfo)),
		uintptr(unsafe.Pointer(lpmsg)))
	return HRESULT(ret1)
}

func PropStgNameToFmtId(str /*const*/ LPOLESTR, rfmtid *FMTID) HRESULT {
	ret1 := syscall3(propStgNameToFmtId, 2,
		uintptr(unsafe.Pointer(str)),
		uintptr(unsafe.Pointer(rfmtid)),
		0)
	return HRESULT(ret1)
}

func PropSysAllocString(str /*const*/ LPCOLESTR) BSTR {
	ret1 := syscall3(propSysAllocString, 1,
		uintptr(unsafe.Pointer(str)),
		0,
		0)
	return (BSTR)(unsafe.Pointer(ret1))
}

func PropSysFreeString(str LPOLESTR) {
	syscall3(propSysFreeString, 1,
		uintptr(unsafe.Pointer(str)),
		0,
		0)
}

func PropVariantClear(pvar *PROPVARIANT) HRESULT {
	ret1 := syscall3(propVariantClear, 1,
		uintptr(unsafe.Pointer(pvar)),
		0,
		0)
	return HRESULT(ret1)
}

func PropVariantCopy(pvarDest *PROPVARIANT, pvarSrc /*const*/ *PROPVARIANT) HRESULT {
	ret1 := syscall3(propVariantCopy, 2,
		uintptr(unsafe.Pointer(pvarDest)),
		uintptr(unsafe.Pointer(pvarSrc)),
		0)
	return HRESULT(ret1)
}

func ReadClassStg(pstg *IStorage, pclsid *CLSID) HRESULT {
	ret1 := syscall3(readClassStg, 2,
		uintptr(unsafe.Pointer(pstg)),
		uintptr(unsafe.Pointer(pclsid)),
		0)
	return HRESULT(ret1)
}

func ReadClassStm(pStm *IStream, pclsid *CLSID) HRESULT {
	ret1 := syscall3(readClassStm, 2,
		uintptr(unsafe.Pointer(pStm)),
		uintptr(unsafe.Pointer(pclsid)),
		0)
	return HRESULT(ret1)
}

func ReadFmtUserTypeStg(pstg LPSTORAGE, pcf *CLIPFORMAT, lplpszUserType *LPOLESTR) HRESULT {
	ret1 := syscall3(readFmtUserTypeStg, 3,
		uintptr(unsafe.Pointer(pstg)),
		uintptr(unsafe.Pointer(pcf)),
		uintptr(unsafe.Pointer(lplpszUserType)))
	return HRESULT(ret1)
}

func RegisterDragDrop(hwnd HWND, pDropTarget LPDROPTARGET) HRESULT {
	ret1 := syscall3(registerDragDrop, 2,
		uintptr(hwnd),
		uintptr(unsafe.Pointer(pDropTarget)),
		0)
	return HRESULT(ret1)
}

func ReleaseStgMedium(pmedium *STGMEDIUM) {
	syscall3(releaseStgMedium, 1,
		uintptr(unsafe.Pointer(pmedium)),
		0,
		0)
}

func RevokeDragDrop(hwnd HWND) HRESULT {
	ret1 := syscall3(revokeDragDrop, 1,
		uintptr(hwnd),
		0,
		0)
	return HRESULT(ret1)
}

func SetConvertStg(storage *IStorage, convert bool) HRESULT {
	ret1 := syscall3(setConvertStg, 2,
		uintptr(unsafe.Pointer(storage)),
		getUintptrFromBool(convert),
		0)
	return HRESULT(ret1)
}

// TODO: Unknown type(s): const SERIALIZEDPROPERTYVALUE *
// func StgConvertPropertyToVariant(prop /*const*/ const SERIALIZEDPROPERTYVALUE *, codePage USHORT, pvar *PROPVARIANT, pma uintptr) BOOLEAN

func StgCreateDocfile(pwcsName /*const*/ LPCOLESTR, grfMode DWORD, reserved DWORD, ppstgOpen **IStorage) HRESULT {
	ret1 := syscall6(stgCreateDocfile, 4,
		uintptr(unsafe.Pointer(pwcsName)),
		uintptr(grfMode),
		uintptr(reserved),
		uintptr(unsafe.Pointer(ppstgOpen)),
		0,
		0)
	return HRESULT(ret1)
}

func StgCreateDocfileOnILockBytes(plkbyt *ILockBytes, grfMode DWORD, reserved DWORD, ppstgOpen **IStorage) HRESULT {
	ret1 := syscall6(stgCreateDocfileOnILockBytes, 4,
		uintptr(unsafe.Pointer(plkbyt)),
		uintptr(grfMode),
		uintptr(reserved),
		uintptr(unsafe.Pointer(ppstgOpen)),
		0,
		0)
	return HRESULT(ret1)
}

func StgCreatePropSetStg(pstg *IStorage, reserved DWORD, propset **IPropertySetStorage) HRESULT {
	ret1 := syscall3(stgCreatePropSetStg, 3,
		uintptr(unsafe.Pointer(pstg)),
		uintptr(reserved),
		uintptr(unsafe.Pointer(propset)))
	return HRESULT(ret1)
}

func StgCreatePropStg(unk *IUnknown, fmt REFFMTID, clsid /*const*/ *CLSID, flags DWORD, reserved DWORD, prop_stg **IPropertyStorage) HRESULT {
	ret1 := syscall6(stgCreatePropStg, 6,
		uintptr(unsafe.Pointer(unk)),
		uintptr(unsafe.Pointer(fmt)),
		uintptr(unsafe.Pointer(clsid)),
		uintptr(flags),
		uintptr(reserved),
		uintptr(unsafe.Pointer(prop_stg)))
	return HRESULT(ret1)
}

// TODO: Unknown type(s): STGOPTIONS *
// func StgCreateStorageEx(pwcsName /*const*/ *WCHAR, grfMode DWORD, stgfmt DWORD, grfAttrs DWORD, pStgOptions STGOPTIONS *, reserved uintptr, riid REFIID, ppObjectOpen uintptr) HRESULT

func StgIsStorageFile(fn /*const*/ LPCOLESTR) HRESULT {
	ret1 := syscall3(stgIsStorageFile, 1,
		uintptr(unsafe.Pointer(fn)),
		0,
		0)
	return HRESULT(ret1)
}

func StgIsStorageILockBytes(plkbyt *ILockBytes) HRESULT {
	ret1 := syscall3(stgIsStorageILockBytes, 1,
		uintptr(unsafe.Pointer(plkbyt)),
		0,
		0)
	return HRESULT(ret1)
}

func StgOpenPropStg(unk *IUnknown, fmt REFFMTID, flags DWORD, reserved DWORD, prop_stg **IPropertyStorage) HRESULT {
	ret1 := syscall6(stgOpenPropStg, 5,
		uintptr(unsafe.Pointer(unk)),
		uintptr(unsafe.Pointer(fmt)),
		uintptr(flags),
		uintptr(reserved),
		uintptr(unsafe.Pointer(prop_stg)),
		0)
	return HRESULT(ret1)
}

// TODO: Unknown type(s): SNB
// func StgOpenStorage(pwcsName /*const*/ *OLECHAR, pstgPriority *IStorage, grfMode DWORD, snbExclude SNB, reserved DWORD, ppstgOpen **IStorage) HRESULT

// TODO: Unknown type(s): STGOPTIONS *
// func StgOpenStorageEx(pwcsName /*const*/ *WCHAR, grfMode DWORD, stgfmt DWORD, grfAttrs DWORD, pStgOptions STGOPTIONS *, reserved uintptr, riid REFIID, ppObjectOpen uintptr) HRESULT

// TODO: Unknown type(s): SNB
// func StgOpenStorageOnILockBytes(plkbyt *ILockBytes, pstgPriority *IStorage, grfMode DWORD, snbExclude SNB, reserved DWORD, ppstgOpen **IStorage) HRESULT

func StgSetTimes(str /*const*/ *OLECHAR, pctime /*const*/ *FILETIME, patime /*const*/ *FILETIME, pmtime /*const*/ *FILETIME) HRESULT {
	ret1 := syscall6(stgSetTimes, 4,
		uintptr(unsafe.Pointer(str)),
		uintptr(unsafe.Pointer(pctime)),
		uintptr(unsafe.Pointer(patime)),
		uintptr(unsafe.Pointer(pmtime)),
		0,
		0)
	return HRESULT(ret1)
}

func StringFromCLSID(id /*const*/ REFCLSID, idstr *LPOLESTR) HRESULT {
	ret1 := syscall3(stringFromCLSID, 2,
		uintptr(unsafe.Pointer(id)),
		uintptr(unsafe.Pointer(idstr)),
		0)
	return HRESULT(ret1)
}

func StringFromGUID2(id REFGUID, str LPOLESTR, cmax INT) INT {
	ret1 := syscall3(stringFromGUID2, 3,
		uintptr(unsafe.Pointer(id)),
		uintptr(unsafe.Pointer(str)),
		uintptr(cmax))
	return INT(ret1)
}

func WdtpInterfacePointer_UserFree(punk *IUnknown) {
	syscall3(wdtpInterfacePointer_UserFree, 1,
		uintptr(unsafe.Pointer(punk)),
		0,
		0)
}

func WriteClassStg(pStg *IStorage, rclsid /*const*/ REFCLSID) HRESULT {
	ret1 := syscall3(writeClassStg, 2,
		uintptr(unsafe.Pointer(pStg)),
		uintptr(unsafe.Pointer(rclsid)),
		0)
	return HRESULT(ret1)
}

func WriteClassStm(pStm *IStream, rclsid /*const*/ REFCLSID) HRESULT {
	ret1 := syscall3(writeClassStm, 2,
		uintptr(unsafe.Pointer(pStm)),
		uintptr(unsafe.Pointer(rclsid)),
		0)
	return HRESULT(ret1)
}

func WriteFmtUserTypeStg(pstg LPSTORAGE, cf CLIPFORMAT, lpszUserType LPOLESTR) HRESULT {
	ret1 := syscall3(writeFmtUserTypeStg, 3,
		uintptr(unsafe.Pointer(pstg)),
		uintptr(cf),
		uintptr(unsafe.Pointer(lpszUserType)))
	return HRESULT(ret1)
}
