package netutil

import (
	"encoding/binary"
	"fmt"
	"net"
	"strconv"
	"strings"
)

// IsIPv4 检查字符串是否为有效的IPv4地址
func IsIPv4(ip string) bool {
	parsedIP := net.ParseIP(ip)
	return parsedIP != nil && parsedIP.To4() != nil
}

// IsIPv6 检查字符串是否为有效的IPv6地址
func IsIPv6(ip string) bool {
	// 处理带接口标识符的IPv6地址（如 fe80::1%eth0）
	if strings.Contains(ip, "%") {
		parts := strings.Split(ip, "%")
		ip = parts[0] // 只取IP部分
	}
	parsedIP := net.ParseIP(ip)
	return parsedIP != nil && parsedIP.To4() == nil && parsedIP.To16() != nil
}

// IsIP 检查字符串是否为有效的IP地址（IPv4或IPv6）
func IsIP(ip string) bool {
	return IsIPv4(ip) || IsIPv6(ip)
}

// GetIPVersion 返回IP地址的版本（4或6），如果无效返回0
func GetIPVersion(ip string) int {
	if IsIPv4(ip) {
		return 4
	}
	if IsIPv6(ip) {
		return 6
	}
	return 0
}

// CleanIPv6 清理IPv6地址，移除接口标识符
func CleanIPv6(ip string) string {
	if strings.Contains(ip, "%") {
		parts := strings.Split(ip, "%")
		return parts[0] // 只返回IP部分
	}
	return ip
}

// FormatAddress 格式化IP地址和端口为正确的网络地址格式
// IPv4: ***********:8080
// IPv6: [2001:db8::1]:8080
func FormatAddress(ip string, port int) string {
	if strings.Contains(ip, ":") && !strings.HasPrefix(ip, "[") {
		// IPv6地址需要用方括号包围
		return fmt.Sprintf("[%s]:%d", ip, port)
	}
	// IPv4地址或已经格式化的IPv6地址
	return fmt.Sprintf("%s:%d", ip, port)
}

// IsIPv4Range 检查字符串是否为有效的IPv4段（CIDR或IP范围）
func IsIPv4Range(ipRange string) bool {
	// 检查是否为CIDR格式 (***********/24)
	if strings.Contains(ipRange, "/") {
		_, _, err := net.ParseCIDR(ipRange)
		return err == nil
	}

	// 检查是否为IP范围格式 (***********-*************)
	if strings.Contains(ipRange, "-") {
		ips := strings.Split(ipRange, "-")
		if len(ips) != 2 {
			return false
		}

		// 验证开始和结束IP是否为有效的IPv4
		if !IsIPv4(ips[0]) || !IsIPv4(ips[1]) {
			return false
		}

		startIP := net.ParseIP(ips[0]).To4()
		endIP := net.ParseIP(ips[1]).To4()

		// 检查开始IP是否小于或等于结束IP
		startInt := binary.BigEndian.Uint32(startIP)
		endInt := binary.BigEndian.Uint32(endIP)

		return startInt <= endInt
	}

	return false
}

// IsIPv6Range 检查字符串是否为有效的IPv6段（CIDR格式）
func IsIPv6Range(ipRange string) bool {
	// 检查是否为CIDR格式 (2001:db8::/32)
	if strings.Contains(ipRange, "/") {
		_, _, err := net.ParseCIDR(ipRange)
		if err != nil {
			return false
		}
		// 验证是否为IPv6 CIDR
		ip, _, _ := net.ParseCIDR(ipRange)
		return IsIPv6(ip.String())
	}
	return false
}

// IsIPRange 检查字符串是否为有效的IP段（支持IPv4和IPv6）
func IsIPRange(ipRange string) bool {
	return IsIPv4Range(ipRange) || IsIPv6Range(ipRange)
}

// ParseIPRange 解析IP段并返回IP列表（支持IPv4和IPv6）
func ParseIPRange(ipRange string) ([]string, error) {
	var ips []string

	// 处理CIDR格式 (***********/24 或 2001:db8::/32)
	if strings.Contains(ipRange, "/") {
		ip, ipNet, err := net.ParseCIDR(ipRange)
		if err != nil {
			return nil, err
		}

		// 检查是IPv4还是IPv6
		if IsIPv4(ip.String()) {
			// IPv4处理
			for ip := ip.Mask(ipNet.Mask); ipNet.Contains(ip); inc(ip) {
				ips = append(ips, ip.String())
			}

			// 移除网络地址和广播地址
			if len(ips) > 2 {
				ips = ips[1 : len(ips)-1]
			}
		} else if IsIPv6(ip.String()) {
			// IPv6处理 - 由于IPv6地址空间巨大，我们限制生成的IP数量
			ones, bits := ipNet.Mask.Size()
			if bits-ones > 16 { // 如果主机位超过16位，限制生成数量
				return nil, fmt.Errorf("IPv6网段太大，主机位不能超过16位: %s", ipRange)
			}

			for ip := ip.Mask(ipNet.Mask); ipNet.Contains(ip); inc(ip) {
				ips = append(ips, ip.String())
				// 限制IPv6地址生成数量，避免内存溢出
				if len(ips) >= 65536 {
					break
				}
			}
		}

		return ips, nil
	}

	// 处理IPv4范围格式 (***********-*************)
	if strings.Contains(ipRange, "-") {
		ipParts := strings.Split(ipRange, "-")
		if len(ipParts) != 2 {
			return nil, fmt.Errorf("无效的IP范围格式: %s", ipRange)
		}

		startIP := net.ParseIP(strings.TrimSpace(ipParts[0]))
		endIP := net.ParseIP(strings.TrimSpace(ipParts[1]))

		if startIP == nil || endIP == nil {
			return nil, fmt.Errorf("无效的IP地址: %s 或 %s", ipParts[0], ipParts[1])
		}

		// 检查是否都是IPv4地址
		if startIP.To4() != nil && endIP.To4() != nil {
			startIPv4 := startIP.To4()
			endIPv4 := endIP.To4()

			// 将IP地址转换为整数
			startInt := binary.BigEndian.Uint32(startIPv4)
			endInt := binary.BigEndian.Uint32(endIPv4)

			// 检查范围有效性
			if startInt > endInt {
				return nil, fmt.Errorf("无效的IP范围: 开始IP大于结束IP")
			}

			// 生成IP范围内的所有IP
			for i := startInt; i <= endInt; i++ {
				ip := make(net.IP, 4)
				binary.BigEndian.PutUint32(ip, i)
				ips = append(ips, ip.String())
			}

			return ips, nil
		} else {
			return nil, fmt.Errorf("IPv6范围格式不支持，请使用CIDR格式: %s", ipRange)
		}
	}

	return nil, fmt.Errorf("不支持的IP范围格式: %s", ipRange)
}

// inc 增加IP地址
func inc(ip net.IP) {
	for j := len(ip) - 1; j >= 0; j-- {
		ip[j]++
		if ip[j] > 0 {
			break
		}
	}
}

// IsValidPort 检查端口号是否有效
func IsValidPort(port string) bool {
	portNum, err := strconv.Atoi(port)
	if err != nil {
		return false
	}
	return portNum > 0 && portNum < 65536
}

// ParseMultipleTargets 解析多种目标格式，支持逗号分隔和IP范围
func ParseMultipleTargets(target string) ([]string, error) {
	var targets []string

	// 检查是否包含逗号分隔
	if strings.Contains(target, ",") {
		// 按逗号分割
		parts := strings.Split(target, ",")
		for _, part := range parts {
			part = strings.TrimSpace(part)
			if part == "" {
				continue
			}

			// 递归解析每个部分
			subTargets, err := ParseMultipleTargets(part)
			if err != nil {
				return nil, fmt.Errorf("解析目标 '%s' 失败: %v", part, err)
			}
			targets = append(targets, subTargets...)
		}
		return targets, nil
	}

	// 检查是否为IP范围格式 (***********-3 或 ***********-***********)
	if IsIPRangeFormat(target) {
		ips, err := ParseIPRangeFormat(target)
		if err != nil {
			return nil, err
		}
		return ips, nil
	}

	// 单个目标
	return []string{target}, nil
}

// IsIPRangeFormat 检查是否为IP范围格式
func IsIPRangeFormat(target string) bool {
	// 检查是否包含 - 符号
	if !strings.Contains(target, "-") {
		return false
	}

	parts := strings.Split(target, "-")
	if len(parts) != 2 {
		return false
	}

	left := strings.TrimSpace(parts[0])
	right := strings.TrimSpace(parts[1])

	// 情况1: ***********-3 (简化格式)
	if IsIP(left) && isNumeric(right) {
		return true
	}

	// 情况2: ***********-*********** (完整格式)
	if IsIP(left) && IsIP(right) {
		return true
	}

	return false
}

// ParseIPRangeFormat 解析IP范围格式
func ParseIPRangeFormat(target string) ([]string, error) {
	parts := strings.Split(target, "-")
	if len(parts) != 2 {
		return nil, fmt.Errorf("无效的IP范围格式: %s", target)
	}

	left := strings.TrimSpace(parts[0])
	right := strings.TrimSpace(parts[1])

	// 检查左边是否为有效IP
	if !IsIP(left) {
		return nil, fmt.Errorf("无效的起始IP地址: %s", left)
	}

	var ips []string

	// 情况1: ***********-3 (简化格式)
	if isNumeric(right) {
		endNum, err := strconv.Atoi(right)
		if err != nil {
			return nil, fmt.Errorf("无效的结束数字: %s", right)
		}

		// 解析起始IP
		startIP := net.ParseIP(left)
		if startIP == nil {
			return nil, fmt.Errorf("无效的起始IP地址: %s", left)
		}

		// 只支持IPv4的简化格式
		if startIP.To4() == nil {
			return nil, fmt.Errorf("简化IP范围格式只支持IPv4地址: %s", left)
		}

		// 获取最后一个八位组的值
		ipParts := strings.Split(left, ".")
		if len(ipParts) != 4 {
			return nil, fmt.Errorf("无效的IPv4地址格式: %s", left)
		}

		startLastOctet, err := strconv.Atoi(ipParts[3])
		if err != nil {
			return nil, fmt.Errorf("无效的IPv4地址: %s", left)
		}

		// 检查范围有效性
		if endNum < startLastOctet || endNum > 255 {
			return nil, fmt.Errorf("无效的IP范围: 结束值 %d 必须在 %d-255 之间", endNum, startLastOctet)
		}

		// 生成IP范围
		baseIP := strings.Join(ipParts[:3], ".")
		for i := startLastOctet; i <= endNum; i++ {
			ips = append(ips, fmt.Sprintf("%s.%d", baseIP, i))
		}

		return ips, nil
	}

	// 情况2: ***********-*********** (完整格式)
	if IsIP(right) {
		return ParseIPRange(fmt.Sprintf("%s-%s", left, right))
	}

	return nil, fmt.Errorf("无效的IP范围格式: %s", target)
}

// isNumeric 检查字符串是否为数字
func isNumeric(s string) bool {
	_, err := strconv.Atoi(s)
	return err == nil
}
