package report

import (
	"Lightx/lib/structs"
	"Lightx/pkg/utils"
	"fmt"
	"io"
	"os"
	"regexp"
	"strconv"
	"strings"

	"github.com/projectdiscovery/gologger"
)

// BruteforceResult 定义爆破成功的结果
type BruteforceResult struct {
	// 插件名称
	PluginName string
	// 目标IP
	IP string
	// 目标端口
	Port int
	// 服务类型
	Service string
	// 用户名
	Username string
	// 密码
	Password string
	// 其他信息（可选）
	Extra string
}

func InitReport(reportPath string, fingerprintsResults []structs.InfoResult) {
	_, err := os.Stat(reportPath)
	fileExists := !os.IsNotExist(err)

	file, err := os.OpenFile(reportPath, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return
	}
	defer file.Close()

	// 如果是首次写入，生成完整的HTML内容
	if !fileExists {
		fullHtmlContent := defaultHeader()
		if _, err := file.WriteString(fullHtmlContent); err != nil {
			return
		}

		// 更新指纹信息
		if len(fingerprintsResults) > 0 {
			UpdateFingerprints(reportPath, fingerprintsResults)
		}
	}
}

// 更新指纹信息
func UpdateFingerprints(reportPath string, fingerprintsResults []structs.InfoResult) {
	// 打开文件
	file, err := os.OpenFile(reportPath, os.O_RDWR, 0644)
	if err != nil {
		gologger.Error().Msg(fmt.Sprintf("打开报告文件失败: %v", err))
		return
	}
	defer file.Close()

	// 读取文件内容
	content, err := io.ReadAll(file)
	if err != nil {
		gologger.Error().Msg(fmt.Sprintf("读取报告文件失败: %v", err))
		return
	}
	contentStr := string(content)

	// 找到插入位置
	insertPosition := strings.Index(contentStr, `<!-- 指纹信息将在这里动态插入 -->`)
	if insertPosition == -1 {
		gologger.Error().Msg("在报告文件中找不到指纹信息容器")
		return
	}

	// 生成指纹信息 HTML 内容
	fingerprintsContent := GenerateFingerReport(fingerprintsResults)

	// 插入指纹信息
	newContent := contentStr[:insertPosition] + fingerprintsContent + contentStr[insertPosition:]

	// 清空文件并写入新内容
	if err := file.Truncate(0); err != nil {
		gologger.Error().Msg(fmt.Sprintf("截断报告文件失败: %v", err))
		return
	}
	if _, err := file.Seek(0, 0); err != nil {
		gologger.Error().Msg(fmt.Sprintf("定位报告文件失败: %v", err))
		return
	}
	if _, err := file.WriteString(newContent); err != nil {
		gologger.Error().Msg(fmt.Sprintf("写入报告文件失败: %v", err))
		return
	}
}

func AppendVulnerabilityReport(reportPath string, pocsResults []structs.VulnerabilityInfo) {
	// 如果 pocsResults 为空，直接返回
	if len(pocsResults) == 0 {
		return
	}

	// 打开文件
	file, err := os.OpenFile(reportPath, os.O_RDWR, 0644)
	if err != nil {
		gologger.Error().Msg(fmt.Sprintf("打开报告文件失败: %v", err))
		return
	}
	defer file.Close()

	// 读取文件内容
	content, err := io.ReadAll(file)
	if err != nil {
		gologger.Error().Msg(fmt.Sprintf("读取报告文件失败: %v", err))
		return
	}
	contentStr := string(content)

	// 找到插入位置（在漏洞信息容器的起始位置）
	insertPosition := strings.Index(contentStr, `<!-- 漏洞信息将在这里动态插入 -->`)
	if insertPosition == -1 {
		gologger.Error().Msg("在报告文件中找不到漏洞信息容器")
		return
	}

	// 检查是否已有漏洞数据
	existingVulnCount := countExistingVulnerabilities(contentStr)

	// 生成漏洞信息 HTML 内容，传递现有漏洞数量作为起始索引
	pocsHtmlContent := GenerateVulnerabilityReport(pocsResults, existingVulnCount)

	// 插入漏洞信息
	newContent := contentStr[:insertPosition] + pocsHtmlContent + contentStr[insertPosition:]

	// 获取所有漏洞信息
	allVulns := getAllVulnerabilities(newContent)

	// 更新漏洞统计数据
	newContent = updateVulnerabilityStats(newContent, allVulns)

	// 清空文件并写入新内容
	if err := file.Truncate(0); err != nil {
		gologger.Error().Msg(fmt.Sprintf("截断报告文件失败: %v", err))
		return
	}
	if _, err := file.Seek(0, 0); err != nil {
		gologger.Error().Msg(fmt.Sprintf("定位报告文件失败: %v", err))
		return
	}
	if _, err := file.WriteString(newContent); err != nil {
		gologger.Error().Msg(fmt.Sprintf("写入报告文件失败: %v", err))
		return
	}
}

// 计算现有漏洞数量
func countExistingVulnerabilities(content string) int {
	count := 0
	// 使用简单的字符串匹配来计算现有漏洞项的数量
	vulnItemPattern := `<li class="vulnerability-item">`
	index := 0
	for {
		pos := strings.Index(content[index:], vulnItemPattern)
		if pos == -1 {
			break
		}
		count++
		index += pos + len(vulnItemPattern)
	}
	return count
}

// 从HTML内容中统计所有漏洞信息
func getAllVulnerabilities(content string) map[string]int {
	// 创建漏洞统计映射
	vulnStats := map[string]int{
		"critical": 0,
		"high":     0,
		"medium":   0,
		"low":      0,
		"info":     0,
		"total":    0,
	}

	// 使用正则表达式查找所有漏洞严重级别
	severityPattern := regexp.MustCompile(`<span class="vulnerability-severity severity-([a-z]+)">([A-Z]+)</span>`)
	matches := severityPattern.FindAllStringSubmatch(content, -1)

	// 统计各严重级别的数量
	for _, match := range matches {
		if len(match) >= 2 {
			severity := strings.ToLower(match[1])
			vulnStats["total"]++

			switch severity {
			case "critical":
				vulnStats["critical"]++
			case "high":
				vulnStats["high"]++
			case "medium":
				vulnStats["medium"]++
			case "low":
				vulnStats["low"]++
			case "info":
				vulnStats["info"]++
			}
		}
	}

	return vulnStats
}

// 更新漏洞统计数据
func updateVulnerabilityStats(content string, vulnStats map[string]int) string {
	// 更新漏洞总数
	content = updateCounterInHTML(content, `<span id="vuln-count">(\d+)</span>`, vulnStats["total"])
	content = updateCounterInHTML(content, `<span id="vuln-badge">(\d+)</span>`, vulnStats["total"])
	content = updateCounterInHTML(content, `<span id="nav-vuln-count">(\d+)</span>`, vulnStats["total"])

	// 更新各个严重级别的漏洞数量
	content = updateCounterInHTML(content, `<div class="summary-card-value" id="critical-count">(\d+)</div>`, vulnStats["critical"])
	content = updateCounterInHTML(content, `<div class="summary-card-value" id="high-count">(\d+)</div>`, vulnStats["high"])
	content = updateCounterInHTML(content, `<div class="summary-card-value" id="medium-count">(\d+)</div>`, vulnStats["medium"])
	content = updateCounterInHTML(content, `<div class="summary-card-value" id="low-count">(\d+)</div>`, vulnStats["low"])

	return content
}

// 使用正则表达式更新HTML中的计数器
func updateCounterInHTML(content, pattern string, count int) string {
	re := regexp.MustCompile(pattern)
	return re.ReplaceAllString(content, strings.Replace(pattern, "(\\d+)", strconv.Itoa(count), 1))
}

func GenerateVulnerabilityReport(POCs []structs.VulnerabilityInfo, startIndex int) string {
	var allContent string

	for index, poc := range POCs {
		// 生成唯一ID，考虑已有的漏洞数量
		vulnID := fmt.Sprintf("vuln-%d", startIndex+index)

		// 漏洞项开始
		vulnItem := fmt.Sprintf(`
		<li class="vulnerability-item">
			<div class="vulnerability-header" onclick="toggleVulnerability('%s-body')">
				<div class="vulnerability-title">
					<span class="vulnerability-id">%d. %s</span>
					<span class="vulnerability-severity severity-%s">%s</span>
				</div>
				<div class="vulnerability-url">%s</div>
			</div>
			<div class="vulnerability-body" id="%s-body">
				<div class="vulnerability-details">
		`, vulnID, startIndex+index+1, xssfilter(poc.ID), strings.ToLower(poc.Severity), poc.Severity, xssfilter(utils.GetBasicURL(poc.URL)), vulnID)

		// 漏洞详情
		vulnItem += fmt.Sprintf(`
					<div class="vulnerability-info-item">
						<span class="vulnerability-info-label">名称:</span>
						<span>%s</span>
					</div>
		`, xssfilter(poc.Name))

		if len(poc.Extract) > 0 {
			vulnItem += fmt.Sprintf(`
					<div class="vulnerability-info-item">
						<span class="vulnerability-info-label">提取:</span>
						<span>%s</span>
					</div>
			`, xssfilter(poc.Extract))
		}

		if len(poc.Description) > 0 {
			vulnItem += fmt.Sprintf(`
					<div class="vulnerability-info-item">
						<span class="vulnerability-info-label">描述:</span>
						<span>%s</span>
					</div>
			`, xssfilter(poc.Description))
		}

		// 参考链接
		if len(poc.Reference) > 0 {
			vulnItem += `
					<div class="vulnerability-info-item vulnerability-reference">
						<span class="vulnerability-info-label">参考:</span>
			`

			if strings.Contains(poc.Reference, ",") {
				for _, rv := range strings.Split(poc.Reference, ",") {
					// 清理URL中的空格
					rv = strings.TrimSpace(rv)
					if rv != "" {
						vulnItem += fmt.Sprintf(`
						<a href="%s" target="_blank">%s</a>`, rv, rv)
					}
				}
			} else {
				vulnItem += fmt.Sprintf(`
						<a href="%s" target="_blank">%s</a>`, poc.Reference, poc.Reference)
			}

			vulnItem += `
					</div>
			`
		}

		vulnItem += `
				</div>
		`

		// 请求和响应部分
		fullurl := xssfilter(poc.URL)
		// JavaScript转义URL，防止单引号等特殊字符破坏JavaScript代码
		jsEscapedURL := strings.ReplaceAll(strings.ReplaceAll(fullurl, "'", "\\'"), "\"", "\\\"")
		reqraw := xssfilter(poc.Request)
		respraw := xssfilter(poc.Response)

		// 限制请求和响应体长度，避免过长的内容破坏报告显示
		const maxContentLength = 10000 // 最大10KB
		if len(reqraw) > maxContentLength {
			reqraw = reqraw[:maxContentLength] + "\n\n... [请求体过长，已截断。完整请求体长度: " +
				fmt.Sprintf("%d", len(poc.Request)) + " 字节]"
		}
		if len(respraw) > maxContentLength {
			respraw = respraw[:maxContentLength] + "\n\n... [响应体过长，已截断。完整响应体长度: " +
				fmt.Sprintf("%d", len(poc.Response)) + " 字节]"
		}

		vulnItem += fmt.Sprintf(`
				<div class="vulnerability-request-response">
					<div class="request-panel">
						<div class="panel-header">
							<div class="panel-title"><i class="fas fa-arrow-right"></i> 请求</div>
							<div class="panel-actions">
								<button class="btn" onclick="copyToClipboard('%s', this)"><i class="fas fa-copy"></i> 复制URL</button>
								<button class="btn" onclick="copyToClipboard(document.querySelector('#req-%s').textContent, this)"><i class="fas fa-copy"></i> 复制请求</button>
							</div>
						</div>
						<pre class="code-block" id="req-%s">%s</pre>
					</div>
					<div class="response-panel">
						<div class="panel-header">
							<div class="panel-title"><i class="fas fa-arrow-left"></i> 响应<span class="response-time">%s</span></div>
							<div class="panel-actions">
								<button class="btn" onclick="copyToClipboard(document.querySelector('#resp-%s').textContent, this)"><i class="fas fa-copy"></i> 复制响应</button>
							</div>
						</div>
						<pre class="code-block" id="resp-%s">%s</pre>
					</div>
				</div>
			</div>
		</li>
		`, jsEscapedURL, vulnID, vulnID, reqraw, poc.ResponseTime, vulnID, vulnID, respraw)

		allContent += vulnItem
	}

	return allContent
}

func GenerateFingerReport(Fingerprints []structs.InfoResult) string {
	if len(Fingerprints) == 0 {
		return ""
	}

	// 添加表头作为第一个列表项
	var fingerprintsContent = `
		<li class="fingerprint-table-header">
			<div class="fingerprint-header-item url">URL</div>
			<div class="fingerprint-header-item status">状态码</div>
			<div class="fingerprint-header-item length">大小</div>
			<div class="fingerprint-header-item tech">指纹</div>
			<div class="fingerprint-header-item title">标题</div>
		</li>
	`

	for _, fingerprint := range Fingerprints {
		// WAF标签
		wafTag := ""
		if fingerprint.IsWAF {
			wafTag = fmt.Sprintf(`<span class="fingerprint-tag tag-waf">WAF: %s</span>`, fingerprint.WAF)
		}

		// 对URL和标题进行HTML转义，防止XSS
		safeURL := xssfilter(fingerprint.URL)
		safeTitle := xssfilter(fingerprint.Title)

		fingerprintsContent += fmt.Sprintf(`
		<li class="fingerprint-item">
			<div class="fingerprint-data url"><a href="%s" target="_blank">%s</a></div>
			<div class="fingerprint-data status"><span class="fingerprint-tag tag-status">%d</span></div>
			<div class="fingerprint-data length"><span class="fingerprint-tag tag-length">%d字节</span></div>
			<div class="fingerprint-data tech">%s %s</div>
			<div class="fingerprint-data title">%s</div>
		</li>
		`, safeURL, safeURL, fingerprint.StatusCode, fingerprint.Length,
			strings.Join(fingerprint.Fingerprints, ", "), wafTag, safeTitle)
	}

	// 更新指纹数量的脚本
	fingerprintCount := fmt.Sprintf(`
	<script>
		document.getElementById('fingerprint-count').textContent = '%d';
		document.getElementById('nav-fingerprint-count').textContent = '%d';
		document.getElementById('target-count').textContent = '%d';
	</script>
	`, len(Fingerprints), len(Fingerprints), len(Fingerprints))

	return fingerprintsContent + fingerprintCount
}

func showWafInfo(isWaf bool, waf string) string {
	if isWaf {
		return fmt.Sprintf("<span style=\"color:#DCA550; font-weight:bold;\">[WAF: %s]</span>", waf)
	} else {
		return ""
	}
}

func getSeverityColor(severity string) string {
	switch strings.ToLower(severity) {
	case "critical":
		return "#E74C3C"
	case "high":
		return "#E67E22"
	case "medium":
		return "#F1C40F"
	case "low":
		return "#2ECC71"
	default:
		return "#BDC3C7"
	}
}

// UpdatePortScanResults 更新端口扫描结果到报告中
func UpdatePortScanResults(reportPath string, portResults []structs.PortScanResult) {
	if len(portResults) == 0 {
		return
	}

	// 打开文件
	file, err := os.OpenFile(reportPath, os.O_RDWR, 0644)
	if err != nil {
		gologger.Error().Msg(fmt.Sprintf("打开报告文件失败: %v", err))
		return
	}
	defer file.Close()

	// 读取文件内容
	content, err := io.ReadAll(file)
	if err != nil {
		gologger.Error().Msg(fmt.Sprintf("读取报告文件失败: %v", err))
		return
	}
	contentStr := string(content)

	// 找到插入位置
	insertPosition := strings.Index(contentStr, `<!-- 端口扫描结果将在这里动态插入 -->`)
	if insertPosition == -1 {
		// 如果没有找到端口扫描容器，则尝试在指纹信息容器前插入端口扫描卡片
		insertPosition = strings.Index(contentStr, `<div class="card" id="fingerprint">`)
		if insertPosition == -1 {
			gologger.Error().Msg("在报告文件中找不到合适的插入位置")
			return
		}

		// 创建端口扫描卡片
		portScanCard := `
		<div class="card" id="portscan">
			<div class="card-header" onclick="toggleCard('portscan-body')">
				<h2><i class="fas fa-network-wired"></i> 端口扫描</h2>
				<span class="badge" id="portscan-badge">0</span>
			</div>
			<div class="card-body" id="portscan-body">
				<ul class="portscan-list" id="portscan-list">
					<!-- 端口扫描结果将在这里动态插入 -->
				</ul>
			</div>
		</div>

		`
		// 在指纹信息卡片前插入端口扫描卡片
		newContent := contentStr[:insertPosition] + portScanCard + contentStr[insertPosition:]

		// 更新内容字符串
		contentStr = newContent

		// 重新查找插入位置
		insertPosition = strings.Index(contentStr, `<!-- 端口扫描结果将在这里动态插入 -->`)
		if insertPosition == -1 {
			gologger.Error().Msg("创建端口扫描容器后仍找不到插入位置")
			return
		}

		// 同时更新导航栏，添加端口扫描导航项
		navInsertPosition := strings.Index(contentStr, `<li class="nav-item">
                <a href="#fingerprint" class="nav-link">`)
		if navInsertPosition != -1 {
			navItem := `<li class="nav-item">
                <a href="#portscan" class="nav-link">
                    <i class="fas fa-network-wired"></i><span class="icon-fallback">[端口]</span>
                    <span>端口扫描</span>
                    <span class="nav-badge" id="nav-portscan-count">0</span>
                </a>
            </li>
            `
			contentStr = contentStr[:navInsertPosition] + navItem + contentStr[navInsertPosition:]
			// 由于插入了导航项，需要重新计算插入位置
			insertPosition = strings.Index(contentStr, `<!-- 端口扫描结果将在这里动态插入 -->`)
		}
	}

	// 生成端口扫描结果 HTML 内容
	portScanContent := GeneratePortScanReport(portResults)

	// 插入端口扫描结果
	newContent := contentStr[:insertPosition] + portScanContent + contentStr[insertPosition:]

	// 清空文件并写入新内容
	if err := file.Truncate(0); err != nil {
		gologger.Error().Msg(fmt.Sprintf("截断报告文件失败: %v", err))
		return
	}
	if _, err := file.Seek(0, 0); err != nil {
		gologger.Error().Msg(fmt.Sprintf("定位报告文件失败: %v", err))
		return
	}
	if _, err := file.WriteString(newContent); err != nil {
		gologger.Error().Msg(fmt.Sprintf("写入报告文件失败: %v", err))
		return
	}
}

// GeneratePortScanReport 生成端口扫描报告的HTML内容
func GeneratePortScanReport(portResults []structs.PortScanResult) string {
	if len(portResults) == 0 {
		return ""
	}

	// 按IP分组
	ipGroups := make(map[string][]structs.PortScanResult)
	for _, result := range portResults {
		ipGroups[result.IP] = append(ipGroups[result.IP], result)
	}

	// 计算IP数量和端口总数
	ipCount := len(ipGroups)
	portCount := len(portResults)

	// 生成HTML内容
	var portScanContent strings.Builder

	// 为每个IP创建一个可折叠的区域
	for ip, results := range ipGroups {
		// 创建IP分组头部
		portScanContent.WriteString(fmt.Sprintf(`
		<div class="ip-group">
			<div class="ip-header" onclick="toggleIPGroup(this)">
				<div class="ip-title"><i class="fas fa-server"></i> %s</div>
				<div class="ip-port-count">%d 个开放端口</div>
				<div class="ip-toggle"><i class="fas fa-chevron-down"></i></div>
			</div>
			<div class="ip-content">
				<div class="portscan-table-header">
					<div class="portscan-header-item port">端口</div>
					<div class="portscan-header-item service">服务</div>
					<div class="portscan-header-item banner">Banner</div>
					<div class="portscan-header-item product">产品</div>
					<div class="portscan-header-item version">版本</div>
				</div>
		`, ip, len(results)))

		// 添加该IP的所有端口
		for _, result := range results {
			// 处理可能为空的字段并进行HTML转义
			banner := result.Banner
			if banner == "" {
				banner = "-"
			} else {
				banner = xssfilter(banner)
			}

			product := result.Product
			if product == "" {
				product = "-"
			} else {
				product = xssfilter(product)
			}

			version := result.Version
			if version == "" {
				version = "-"
			} else {
				version = xssfilter(version)
			}

			// 为HTTP服务添加标题信息
			httpInfo := ""
			if result.HttpTitle != "" && result.HttpTitle != "-" {
				httpInfo = fmt.Sprintf(`<span class="portscan-tag tag-http">%s</span>`, xssfilter(result.HttpTitle))
			}

			portScanContent.WriteString(fmt.Sprintf(`
			<li class="portscan-item">
				<div class="portscan-data port"><span class="portscan-tag tag-port">%d</span></div>
				<div class="portscan-data service"><span class="portscan-tag tag-service">%s</span> %s</div>
				<div class="portscan-data banner">%s</div>
				<div class="portscan-data product">%s</div>
				<div class="portscan-data version">%s</div>
			</li>
			`, result.Port, xssfilter(result.Server), httpInfo, banner, product, version))
		}

		// 关闭IP分组
		portScanContent.WriteString(`
			</div>
		</div>
		`)
	}

	// 更新端口扫描数量的脚本
	portScanCount := fmt.Sprintf(`
	<script>
		document.getElementById('portscan-badge').textContent = '%d';
		document.getElementById('nav-portscan-count').textContent = '%d';
		document.getElementById('ip-count').textContent = '%d';
		document.getElementById('port-count').textContent = '%d';
	</script>
	`, portCount, portCount, ipCount, portCount)

	return portScanContent.String() + portScanCount
}

// UpdateBruteforceResults 更新爆破成功结果到报告中
func UpdateBruteforceResults(reportPath string, result BruteforceResult) {
	// 打开文件
	file, err := os.OpenFile(reportPath, os.O_RDWR, 0644)
	if err != nil {
		gologger.Error().Msg(fmt.Sprintf("打开报告文件失败: %v", err))
		return
	}
	defer file.Close()

	// 读取文件内容
	content, err := io.ReadAll(file)
	if err != nil {
		gologger.Error().Msg(fmt.Sprintf("读取报告文件失败: %v", err))
		return
	}
	contentStr := string(content)

	// 检查是否有重复条目，防止同一个凭证被多次添加
	existingPattern := fmt.Sprintf(
		`<span class="bruteforce-service[^>]*>%s</span>[^<]*<span class="bruteforce-target">%s:%d</span>.*?<span class="credential-value">%s</span>.*?<span class="credential-value">%s</span>`,
		regexp.QuoteMeta(result.Service),
		regexp.QuoteMeta(result.IP),
		result.Port,
		regexp.QuoteMeta(result.Username),
		regexp.QuoteMeta(result.Password),
	)

	r, _ := regexp.Compile(existingPattern)
	if r.MatchString(contentStr) {
		gologger.Debug().Msg(fmt.Sprintf("相同的爆破结果已存在: %s@%s:%d (%s/%s)",
			result.Service, result.IP, result.Port, result.Username, result.Password))
		return // 不再添加重复的爆破结果
	}

	// 检查报告中是否已存在bruteforce卡片
	hasBruteforceCard := strings.Contains(contentStr, `<div class="card" id="bruteforce">`)
	if !hasBruteforceCard {
		// 尝试多种可能的插入位置模式
		// 1. 在指纹识别卡片后插入
		fingerprintCardEnd := strings.Index(contentStr, `</div>
            </div>

            <div class="card" id="portscan">`)

		// 2. 如果第一种模式不匹配，尝试其他可能的模式
		if fingerprintCardEnd == -1 {
			fingerprintCardEnd = strings.Index(contentStr, `<div class="card" id="portscan">`)
			if fingerprintCardEnd != -1 {
				// 向前查找最近的闭合div标签
				tempStr := contentStr[:fingerprintCardEnd]
				lastDivEnd := strings.LastIndex(tempStr, "</div>")
				if lastDivEnd != -1 {
					fingerprintCardEnd = lastDivEnd
				}
			}
		}

		// 3. 如果仍然找不到，尝试在漏洞卡片后插入
		if fingerprintCardEnd == -1 {
			fingerprintCardEnd = strings.Index(contentStr, `<div class="card" id="fingerprint">`)
			if fingerprintCardEnd != -1 {
				// 向前查找最近的闭合div标签
				tempStr := contentStr[:fingerprintCardEnd]
				lastDivEnd := strings.LastIndex(tempStr, "</div>")
				if lastDivEnd != -1 {
					fingerprintCardEnd = lastDivEnd
				}
			}
		}

		if fingerprintCardEnd == -1 {
			// 记录错误但不返回，尝试在其他位置插入
			gologger.Warning().Msg("无法找到理想的爆破卡片插入位置，将尝试在报告末尾插入")

			// 尝试在报告主体内容的末尾插入
			bodyEndPos := strings.Index(contentStr, `<div class="footer">`)
			if bodyEndPos != -1 {
				fingerprintCardEnd = bodyEndPos
			} else {
				// 如果连footer都找不到，就在body标签结束前插入
				bodyEndPos = strings.Index(contentStr, `</body>`)
				if bodyEndPos != -1 {
					fingerprintCardEnd = bodyEndPos
				} else {
					gologger.Error().Msg("无法定位爆破卡片插入位置")
					return
				}
			}
		}

		bruteforceCard := `
            <div class="card" id="bruteforce">
                <div class="card-header" onclick="toggleCard('bruteforce-body')">
                    <h2><i class="fas fa-key"></i> 爆破成功</h2>
                    <span class="badge" id="bruteforce-badge">0</span>
                </div>
                <div class="card-body" id="bruteforce-body">
                    <ul class="bruteforce-list" id="bruteforce-list">
                        <!-- 爆破结果将在这里动态插入 -->
                    </ul>
                </div>
            </div>
        `

		// 插入爆破成功卡片
		contentStr = contentStr[:fingerprintCardEnd] + bruteforceCard + contentStr[fingerprintCardEnd:]

		// 如果侧边栏没有爆破导航，也需要添加
		if !strings.Contains(contentStr, `<a href="#bruteforce" class="nav-link">`) {
			navInsertPos := strings.Index(contentStr, `<li class="nav-item">
                <a href="#portscan" class="nav-link">`)

			if navInsertPos != -1 {
				navItem := `<li class="nav-item">
                <a href="#bruteforce" class="nav-link">
                    <i class="fas fa-key"></i><span class="icon-fallback">[爆破]</span>
                    <span>爆破成功</span>
                    <span class="nav-badge" id="nav-bruteforce-count">0</span>
                </a>
            </li>
            `
				contentStr = contentStr[:navInsertPos] + navItem + contentStr[navInsertPos:]
			}

			// 在头部信息区域添加爆破成功计数
			if !strings.Contains(contentStr, `<i class="fas fa-key"></i> 爆破成功:`) {
				headerPos := strings.Index(contentStr, `<div class="header-info-item">
                            <i class="fas fa-bug"></i> 发现漏洞: <span id="vuln-count">0</span>
                        </div>`)

				if headerPos != -1 {
					headerEnd := strings.Index(contentStr[headerPos:], `</div>`) + headerPos
					if headerEnd != -1 {
						headerItem := `</div>
                        <div class="header-info-item">
                            <i class="fas fa-key"></i> 爆破成功: <span id="bruteforce-count">0</span>`

						contentStr = contentStr[:headerEnd] + headerItem + contentStr[headerEnd:]
					}
				}
			}
		}
	}

	// 现在尝试查找爆破结果容器
	// 先使用注释标记定位
	insertPosition := strings.Index(contentStr, `<!-- 爆破结果将在这里动态插入 -->`)

	if insertPosition == -1 {
		// 如果找不到注释标记，尝试找到爆破列表开始位置
		listStartPos := strings.Index(contentStr, `<ul class="bruteforce-list" id="bruteforce-list">`)
		if listStartPos != -1 {
			// 找到列表结束标签位置
			listEndPos := strings.Index(contentStr[listStartPos:], `</ul>`) + listStartPos
			if listEndPos != -1 {
				// 在列表结束标签前插入注释标记
				contentStr = contentStr[:listEndPos] + `<!-- 爆破结果将在这里动态插入 -->` + contentStr[listEndPos:]
				insertPosition = listEndPos
			} else {
				// 如果找不到结束标签，就在列表开始标签后插入
				insertPosition = listStartPos + len(`<ul class="bruteforce-list" id="bruteforce-list">`)
			}
		}
	}

	// 如果仍然无法找到插入位置，这是不正常的情况，尝试重新定位爆破列表
	if insertPosition == -1 {
		// 再次检查是否存在bruteforce卡片
		bruteforceCardPos := strings.Index(contentStr, `<div class="card" id="bruteforce">`)
		if bruteforceCardPos != -1 {
			// 在卡片内查找列表位置
			listPos := strings.Index(contentStr[bruteforceCardPos:], `<ul class="bruteforce-list"`) + bruteforceCardPos
			if listPos != -1 {
				// 找到列表的开始标签后位置
				listStartPos := strings.Index(contentStr[listPos:], `>`) + listPos + 1
				if listStartPos != -1 {
					insertPosition = listStartPos
				}
			}
		}

		// 如果仍然找不到，尝试创建一个新的爆破列表
		if insertPosition == -1 {
			gologger.Warning().Msg("无法定位爆破结果插入点，将尝试创建新的爆破列表")

			// 查找bruteforce卡片的body部分
			bruteforceBodyPos := strings.Index(contentStr, `<div class="card-body" id="bruteforce-body">`)
			if bruteforceBodyPos != -1 {
				// 在body内创建新列表
				bodyEndPos := strings.Index(contentStr[bruteforceBodyPos:], `</div>`) + bruteforceBodyPos
				if bodyEndPos != -1 {
					newList := `
                    <ul class="bruteforce-list" id="bruteforce-list">
                        <!-- 爆破结果将在这里动态插入 -->
                    </ul>
                    `
					contentStr = contentStr[:bodyEndPos] + newList + contentStr[bodyEndPos:]
					insertPosition = strings.Index(contentStr, `<!-- 爆破结果将在这里动态插入 -->`)
				}
			}

			// 如果仍然无法找到或创建列表，放弃此次更新
			if insertPosition == -1 {
				gologger.Error().Msg("无法定位或创建爆破结果插入点，放弃此次更新")
				return
			}
		}
	}

	// 生成爆破结果HTML内容
	bruteforceContent := GenerateBruteforceReport(result)

	// 插入爆破结果
	newContent := contentStr[:insertPosition] + bruteforceContent + contentStr[insertPosition:]

	// 更新爆破成功计数，但不添加+1，而是使用准确的计数
	newContent = updateBruteforceCount(newContent, 0) // 0只是占位符，实际会在函数内重新计算

	// 清空文件并写入新内容
	if err := file.Truncate(0); err != nil {
		gologger.Error().Msg(fmt.Sprintf("截断报告文件失败: %v", err))
		return
	}
	if _, err := file.Seek(0, 0); err != nil {
		gologger.Error().Msg(fmt.Sprintf("定位报告文件失败: %v", err))
		return
	}
	if _, err := file.WriteString(newContent); err != nil {
		gologger.Error().Msg(fmt.Sprintf("写入报告文件失败: %v", err))
		return
	}

	// 提示成功
	gologger.Debug().Msg(fmt.Sprintf("成功添加爆破结果: %s@%s:%d (%s/%s)",
		result.Service, result.IP, result.Port, result.Username, result.Password))
}

// 计算现有爆破结果数量
func countExistingBruteforceResults(content string) int {
	// 使用正则表达式更准确地匹配爆破结果项
	pattern := `<li class="bruteforce-item">`
	matches := regexp.MustCompile(pattern).FindAllStringIndex(content, -1)
	return len(matches)
}

// 更新爆破成功计数
func updateBruteforceCount(content string, count int) string {
	// 先准确计算当前爆破成功的数量，而不是简单+1
	actualCount := countExistingBruteforceResults(content)

	// 更新爆破成功总数（使用实际计数而不是传入的count值）
	content = updateCounterInHTML(content, `<span id="bruteforce-count">(\d+)</span>`, actualCount)
	content = updateCounterInHTML(content, `<span id="bruteforce-badge">(\d+)</span>`, actualCount)
	content = updateCounterInHTML(content, `<span id="nav-bruteforce-count">(\d+)</span>`, actualCount)

	// 在更新完成后添加一个前端脚本，强制更新数字显示
	if !strings.Contains(content, "updateBruteforceCounter") {
		scriptPos := strings.Index(content, `</body>`)
		if scriptPos != -1 {
			updateScript := fmt.Sprintf(`
			<script>
				// 强制更新爆破成功计数
				function updateBruteforceCounter() {
					const count = document.querySelectorAll('.bruteforce-item').length;
					document.getElementById('bruteforce-count').textContent = count;
					document.getElementById('bruteforce-badge').textContent = count;
					document.getElementById('nav-bruteforce-count').textContent = count;
				}
				// 页面加载完毕后执行一次
				window.addEventListener('DOMContentLoaded', updateBruteforceCounter);
			</script>
			`)
			content = content[:scriptPos] + updateScript + content[scriptPos:]
		}
	}

	return content
}

// GenerateBruteforceReport 生成爆破结果报告HTML - 表格样式
func GenerateBruteforceReport(result BruteforceResult) string {
	// 处理用户名或密码为空的情况
	username := result.Username
	if username == "" {
		username = "<span class='empty-value'>(空)</span>"
	}

	password := result.Password
	if password == "" {
		password = "<span class='empty-value'>(空)</span>"
	}

	// 处理其他信息
	extra := result.Extra
	if extra == "" {
		extra = "-"
	}

	// 为不同服务设置不同图标和样式
	serviceIcon := getServiceIcon(result.Service)
	serviceClass := "service-" + strings.ToLower(result.Service)

	// 目标地址，格式为IP:Port
	target := fmt.Sprintf("%s:%d", result.IP, result.Port)

	// JavaScript转义，防止特殊字符破坏JavaScript代码
	jsEscapedTarget := strings.ReplaceAll(strings.ReplaceAll(target, "'", "\\'"), "\"", "\\\"")
	jsEscapedUsername := strings.ReplaceAll(strings.ReplaceAll(username, "'", "\\'"), "\"", "\\\"")
	jsEscapedPassword := strings.ReplaceAll(strings.ReplaceAll(password, "'", "\\'"), "\"", "\\\"")
	jsEscapedExtra := strings.ReplaceAll(strings.ReplaceAll(extra, "'", "\\'"), "\"", "\\\"")

	// HTML转义显示内容
	htmlEscapedTarget := xssfilter(target)
	htmlEscapedUsername := xssfilter(username)
	htmlEscapedPassword := xssfilter(password)
	htmlEscapedExtra := xssfilter(extra)

	// 生成爆破结果HTML，使用表格样式和颜色标识
	bruteforceItem := fmt.Sprintf(`
	<li class="bruteforce-item">
		<div class="bruteforce-data service">
			<span class="bruteforce-service-tag %s">
				<i class="%s"></i> %s
			</span>
		</div>
		<div class="bruteforce-data target">
			<span class="bruteforce-value target" onclick="copyTargetToClipboard(this, '%s')" title="点击复制目标地址">
				%s
			</span>
		</div>
		<div class="bruteforce-data username">
			<span class="bruteforce-value %s" onclick="copyCredentialToClipboard(this, '%s')" title="点击复制用户名">
				%s
			</span>
		</div>
		<div class="bruteforce-data password">
			<span class="bruteforce-value %s" onclick="copyCredentialToClipboard(this, '%s')" title="点击复制密码">
				%s
			</span>
		</div>
		<div class="bruteforce-data extra">
			<span class="bruteforce-value %s" onclick="copyCredentialToClipboard(this, '%s')" title="点击复制其他信息">
				%s
			</span>
		</div>
		<div class="bruteforce-data plugin">
			<span class="bruteforce-plugin-tag">%s</span>
		</div>
	</li>
	`, serviceClass, serviceIcon, result.Service, jsEscapedTarget, htmlEscapedTarget,
		getUsernameClass(result.Username), jsEscapedUsername, htmlEscapedUsername,
		getPasswordClass(result.Password), jsEscapedPassword, htmlEscapedPassword,
		getExtraClass(result.Extra), jsEscapedExtra, htmlEscapedExtra, result.PluginName)

	return bruteforceItem
}

// getServiceIcon 根据服务类型返回对应的图标
func getServiceIcon(service string) string {
	switch strings.ToLower(service) {
	case "mysql":
		return "fas fa-database"
	case "redis":
		return "fas fa-memory"
	case "ssh":
		return "fas fa-terminal"
	case "ftp":
		return "fas fa-folder-open"
	case "postgres", "postgresql":
		return "fas fa-database"
	case "mongodb", "mongo":
		return "fas fa-leaf"
	case "mssql", "sqlserver":
		return "fas fa-database"
	case "telnet":
		return "fas fa-terminal"
	case "vnc":
		return "fas fa-desktop"
	case "rdp":
		return "fas fa-desktop"
	case "smb":
		return "fas fa-share-alt"
	case "oracle":
		return "fas fa-database"
	case "ldap":
		return "fas fa-users"
	case "snmp":
		return "fas fa-network-wired"
	default:
		return "fas fa-server"
	}
}

// getUsernameClass 根据用户名内容返回对应的CSS类
func getUsernameClass(username string) string {
	if username == "" {
		return "default"
	}
	return "username"
}

// getPasswordClass 根据密码内容返回对应的CSS类
func getPasswordClass(password string) string {
	if password == "" {
		return "default"
	}
	return "password"
}

// getExtraClass 根据其他信息内容返回对应的CSS类
func getExtraClass(extra string) string {
	if extra == "" || extra == "-" {
		return "default"
	}
	return "extra"
}

// generateExtraInfo 生成额外信息的HTML - 已移除，不再使用
func generateExtraInfo(extra string) string {
	// 这个函数已经不再使用，因为额外信息现在直接在主函数中处理
	return ""
}
