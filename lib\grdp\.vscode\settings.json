{
    "go.toolsEnvVars": {
        "GOFLAGS": "-mod=vendor",
        "GO111MODULE": "on",
        "GOPROXY": "https://goproxy.cn,direct"
    },
    "go.autocompleteUnimportedPackages": true,
    "go.formatTool": "goimports",
    "go.languageServerExperimentalFeatures": {
        "diagnostics": false
    },
    "go.useCodeSnippetsOnFunctionSuggest": false,
    "go.buildTags": "",
    "go.docsTool": "guru",
    "go.vetOnSave": "off",
    "tabnine.experimentalAutoImports": true,
}