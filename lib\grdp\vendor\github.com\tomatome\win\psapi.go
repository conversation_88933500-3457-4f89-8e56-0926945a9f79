// This file was automatically generated by https://github.com/kbinani/win/blob/generator/internal/cmd/gen/gen.go
// go run internal/cmd/gen/gen.go

// +build windows

package win

import (
	"syscall"
	"unsafe"
)

var (
	// Library
	libpsapi uintptr

	// Functions
	emptyWorkingSet             uintptr
	enumDeviceDrivers           uintptr
	enumPageFiles               uintptr
	enumProcessModules          uintptr
	enumProcessModulesEx        uintptr
	enumProcesses               uintptr
	getDeviceDriverBaseName     uintptr
	getDeviceDriverFileName     uintptr
	getMappedFileName           uintptr
	getModuleBaseName           uintptr
	getModuleFileNameEx         uintptr
	getModuleInformation        uintptr
	getPerformanceInfo          uintptr
	getProcessImageFileName     uintptr
	getProcessMemoryInfo        uintptr
	getWsChanges                uintptr
	getWsChangesEx              uintptr
	initializeProcessForWsWatch uintptr
	queryWorkingSet             uintptr
	queryWorkingSetEx           uintptr
)

func init() {
	// Library
	libpsapi = doLoadLibrary("psapi.dll")

	// Functions
	emptyWorkingSet = doGetProcAddress(libpsapi, "EmptyWorkingSet")
	enumDeviceDrivers = doGetProcAddress(libpsapi, "EnumDeviceDrivers")
	enumPageFiles = doGetProcAddress(libpsapi, "EnumPageFilesW")
	enumProcessModules = doGetProcAddress(libpsapi, "EnumProcessModules")
	enumProcessModulesEx = doGetProcAddress(libpsapi, "EnumProcessModulesEx")
	enumProcesses = doGetProcAddress(libpsapi, "EnumProcesses")
	getDeviceDriverBaseName = doGetProcAddress(libpsapi, "GetDeviceDriverBaseNameW")
	getDeviceDriverFileName = doGetProcAddress(libpsapi, "GetDeviceDriverFileNameW")
	getMappedFileName = doGetProcAddress(libpsapi, "GetMappedFileNameW")
	getModuleBaseName = doGetProcAddress(libpsapi, "GetModuleBaseNameW")
	getModuleFileNameEx = doGetProcAddress(libpsapi, "GetModuleFileNameExW")
	getModuleInformation = doGetProcAddress(libpsapi, "GetModuleInformation")
	getPerformanceInfo = doGetProcAddress(libpsapi, "GetPerformanceInfo")
	getProcessImageFileName = doGetProcAddress(libpsapi, "GetProcessImageFileNameW")
	getProcessMemoryInfo = doGetProcAddress(libpsapi, "GetProcessMemoryInfo")
	getWsChanges = doGetProcAddress(libpsapi, "GetWsChanges")
	getWsChangesEx = doGetProcAddress(libpsapi, "GetWsChangesEx")
	initializeProcessForWsWatch = doGetProcAddress(libpsapi, "InitializeProcessForWsWatch")
	queryWorkingSet = doGetProcAddress(libpsapi, "QueryWorkingSet")
	queryWorkingSetEx = doGetProcAddress(libpsapi, "QueryWorkingSetEx")
}

func EmptyWorkingSet(hProcess HANDLE) bool {
	ret1 := syscall3(emptyWorkingSet, 1,
		uintptr(hProcess),
		0,
		0)
	return ret1 != 0
}

func EnumDeviceDrivers(lpImageBase *LPVOID, cb DWORD, lpcbNeeded *uint32) bool {
	ret1 := syscall3(enumDeviceDrivers, 3,
		uintptr(unsafe.Pointer(lpImageBase)),
		uintptr(cb),
		uintptr(unsafe.Pointer(lpcbNeeded)))
	return ret1 != 0
}

func EnumPageFiles(pCallBackRoutine PENUM_PAGE_FILE_CALLBACK, pContext LPVOID) bool {
	pCallBackRoutineCallback := syscall.NewCallback(func(pContextRawArg LPVOID, pPageFileInfoRawArg PENUM_PAGE_FILE_INFORMATION, lpFilenameRawArg /*const*/ *uint16) uintptr {
		lpFilename := stringFromUnicode16(lpFilenameRawArg)
		ret := pCallBackRoutine(pContextRawArg, pPageFileInfoRawArg, lpFilename)
		return uintptr(ret)
	})
	ret1 := syscall3(enumPageFiles, 2,
		pCallBackRoutineCallback,
		uintptr(unsafe.Pointer(pContext)),
		0)
	return ret1 != 0
}

func EnumProcessModules(hProcess HANDLE, lphModule *HMODULE, cb DWORD, lpcbNeeded *uint32) bool {
	ret1 := syscall6(enumProcessModules, 4,
		uintptr(hProcess),
		uintptr(unsafe.Pointer(lphModule)),
		uintptr(cb),
		uintptr(unsafe.Pointer(lpcbNeeded)),
		0,
		0)
	return ret1 != 0
}

func EnumProcessModulesEx(hProcess HANDLE, lphModule *HMODULE, cb DWORD, lpcbNeeded *uint32, dwFilterFlag DWORD) bool {
	ret1 := syscall6(enumProcessModulesEx, 5,
		uintptr(hProcess),
		uintptr(unsafe.Pointer(lphModule)),
		uintptr(cb),
		uintptr(unsafe.Pointer(lpcbNeeded)),
		uintptr(dwFilterFlag),
		0)
	return ret1 != 0
}

func EnumProcesses(lpidProcess *uint32, cb DWORD, cbNeeded *uint32) bool {
	ret1 := syscall3(enumProcesses, 3,
		uintptr(unsafe.Pointer(lpidProcess)),
		uintptr(cb),
		uintptr(unsafe.Pointer(cbNeeded)))
	return ret1 != 0
}

func GetDeviceDriverBaseName(imageBase LPVOID, lpBaseName LPWSTR, nSize DWORD) DWORD {
	ret1 := syscall3(getDeviceDriverBaseName, 3,
		uintptr(unsafe.Pointer(imageBase)),
		uintptr(unsafe.Pointer(lpBaseName)),
		uintptr(nSize))
	return DWORD(ret1)
}

func GetDeviceDriverFileName(imageBase LPVOID, lpFilename LPWSTR, nSize DWORD) DWORD {
	ret1 := syscall3(getDeviceDriverFileName, 3,
		uintptr(unsafe.Pointer(imageBase)),
		uintptr(unsafe.Pointer(lpFilename)),
		uintptr(nSize))
	return DWORD(ret1)
}

func GetMappedFileName(hProcess HANDLE, lpv LPVOID, lpFilename LPWSTR, nSize DWORD) DWORD {
	ret1 := syscall6(getMappedFileName, 4,
		uintptr(hProcess),
		uintptr(unsafe.Pointer(lpv)),
		uintptr(unsafe.Pointer(lpFilename)),
		uintptr(nSize),
		0,
		0)
	return DWORD(ret1)
}

func GetModuleBaseName(hProcess HANDLE, hModule HMODULE, lpBaseName LPWSTR, nSize DWORD) DWORD {
	ret1 := syscall6(getModuleBaseName, 4,
		uintptr(hProcess),
		uintptr(hModule),
		uintptr(unsafe.Pointer(lpBaseName)),
		uintptr(nSize),
		0,
		0)
	return DWORD(ret1)
}

func GetModuleFileNameEx(hProcess HANDLE, hModule HMODULE, lpFilename LPWSTR, nSize DWORD) DWORD {
	ret1 := syscall6(getModuleFileNameEx, 4,
		uintptr(hProcess),
		uintptr(hModule),
		uintptr(unsafe.Pointer(lpFilename)),
		uintptr(nSize),
		0,
		0)
	return DWORD(ret1)
}

func GetModuleInformation(hProcess HANDLE, hModule HMODULE, lpmodinfo *MODULEINFO, cb DWORD) bool {
	ret1 := syscall6(getModuleInformation, 4,
		uintptr(hProcess),
		uintptr(hModule),
		uintptr(unsafe.Pointer(lpmodinfo)),
		uintptr(cb),
		0,
		0)
	return ret1 != 0
}

func GetPerformanceInfo(pPerformanceInformation PPERFORMACE_INFORMATION, cb DWORD) bool {
	ret1 := syscall3(getPerformanceInfo, 2,
		uintptr(unsafe.Pointer(pPerformanceInformation)),
		uintptr(cb),
		0)
	return ret1 != 0
}

func GetProcessImageFileName(hProcess HANDLE, lpImageFileName LPWSTR, nSize DWORD) DWORD {
	ret1 := syscall3(getProcessImageFileName, 3,
		uintptr(hProcess),
		uintptr(unsafe.Pointer(lpImageFileName)),
		uintptr(nSize))
	return DWORD(ret1)
}

func GetProcessMemoryInfo(process HANDLE, ppsmemCounters PPROCESS_MEMORY_COUNTERS, cb DWORD) bool {
	ret1 := syscall3(getProcessMemoryInfo, 3,
		uintptr(process),
		uintptr(unsafe.Pointer(ppsmemCounters)),
		uintptr(cb))
	return ret1 != 0
}

func GetWsChanges(hProcess HANDLE, lpWatchInfo PPSAPI_WS_WATCH_INFORMATION, cb DWORD) bool {
	ret1 := syscall3(getWsChanges, 3,
		uintptr(hProcess),
		uintptr(unsafe.Pointer(lpWatchInfo)),
		uintptr(cb))
	return ret1 != 0
}

func GetWsChangesEx(hProcess HANDLE, lpWatchInfoEx PPSAPI_WS_WATCH_INFORMATION_EX, cb DWORD) bool {
	ret1 := syscall3(getWsChangesEx, 3,
		uintptr(hProcess),
		uintptr(unsafe.Pointer(lpWatchInfoEx)),
		uintptr(cb))
	return ret1 != 0
}

func InitializeProcessForWsWatch(hProcess HANDLE) bool {
	ret1 := syscall3(initializeProcessForWsWatch, 1,
		uintptr(hProcess),
		0,
		0)
	return ret1 != 0
}

func QueryWorkingSet(hProcess HANDLE, pv uintptr, cb DWORD) bool {
	ret1 := syscall3(queryWorkingSet, 3,
		uintptr(hProcess),
		pv,
		uintptr(cb))
	return ret1 != 0
}

func QueryWorkingSetEx(hProcess HANDLE, pv uintptr, cb DWORD) bool {
	ret1 := syscall3(queryWorkingSetEx, 3,
		uintptr(hProcess),
		pv,
		uintptr(cb))
	return ret1 != 0
}
