sudo: false
addons:
  apt_packages:
    - libgles2-mesa-dev
language: go
go:
  - 1.x
  - master
matrix:
  allow_failures:
    - go: master
  fast_finish: true
install:
  - # Do nothing. This is needed to prevent default install action "go get -t -v ./..." from happening here (we want it to happen inside script step).
before_script:
  - export DISPLAY=:99.0
  - sh -e /etc/init.d/xvfb start
  - sleep 3 # Give xvfb some time to start.
script:
  - go get -t -v ./...
  - diff -u <(echo -n) <(gofmt -d -s .)
  - go tool vet .
  - go test -v -race ./...
