package common

import (
	"sync"
)

// ServicePlugin 服务插件配置
type ServicePlugin struct {
	// 插件是否启用
	Enabled bool
	// 插件可用状态
	Available bool
}

// 数据库爆破配置
type BruteConfig struct {
	// 爆破尝试的用户名列表(全局)
	Usernames []string
	// 爆破尝试的密码列表(全局)
	Passwords []string
	// 服务特定的用户名字典
	UserDict map[string][]string
	// 默认密码字典
	DefaultPasswords []string
	// 爆破线程数
	Threads int
	// 爆破超时时间(秒)
	Timeout int
	// 最大尝试次数
	MaxAttempts int
}

// 漏洞检测配置
type VulnConfig struct {
}

// 工作池配置
type WorkerPoolConfig struct {
	// 工作池大小
	PoolSize int
	// 任务队列大小
	QueueSize int
	// 工作池互斥锁
	Mutex sync.Mutex
}

// 端口列表配置
type PortListConfig struct {
	// 端口列表映射表，键为列表名称，值为端口列表
	PortMap map[string][]int
}

// 全局配置变量
var (
	// 全局爆破配置
	GlobalBruteConfig = &BruteConfig{
		// 全局默认用户名列表
		Usernames: []string{
			"root", "admin", "administrator", "test", "user", "guest", "default", "sa",
			"mysql", "oracle", "postgres", "mssql", "sqlserver", "mongodb", "redis",
		},
		// 全局默认密码列表
		Passwords: []string{
			"", "password", "123456", "admin", "admin123", "root", "public", "root123", "qwerty",
			"P@ssw0rd", "123456789", "12345678", "1234567890", "000000", "111111", "password123",
			"admin@123", "Admin@123", "root@123", "Root@123", "1q2w3e4r", "1qaz2wsx", "qazwsx",
			"123qwe", "oracle", "mysql", "postgres", "sqlserver", "sa", "sa123", "mongodb", "redis",
		},
		// 服务特定的用户名字典
		UserDict: map[string][]string{
			"FTP":        {"ftp", "admin", "www", "web", "root", "db", "wwwroot", "data"},
			"MySQL":      {"root", "mysql"},
			"MSSQL":      {"sa", "sql"},
			"SMB":        {"administrator", "admin", "guest"},
			"RDP":        {"administrator", "admin", "guest"},
			"PostgreSQL": {"postgres", "admin"},
			"SSH":        {"root", "admin"},
			"MongoDB":    {"root", "admin"},
			"Oracle":     {"sys", "system", "admin", "test", "web", "orcl"},
			"Telnet":     {"root", "admin", "test"},
			"Elastic":    {"elastic", "admin", "kibana"},
			"RabbitMQ":   {"guest", "admin", "administrator", "rabbit", "rabbitmq", "root"},
			"Kafka":      {"admin", "kafka", "root", "test"},
			"ActiveMQ":   {"admin", "root", "activemq", "system", "user"},
			"LDAP":       {"admin", "administrator", "root", "cn=admin", "cn=administrator", "cn=manager"},
			"SMTP":       {"admin", "root", "postmaster", "mail", "smtp", "administrator"},
			"IMAP":       {"admin", "mail", "postmaster", "root", "user", "test"},
			"POP3":       {"admin", "root", "mail", "user", "test", "postmaster"},
			"Zabbix":     {"Admin", "admin", "guest", "user"},
			"Rsync":      {"rsync", "root", "admin", "backup"},
			"Cassandra":  {"cassandra", "admin", "root", "system"},
			"Neo4j":      {"neo4j", "admin", "root", "test"},
			"Redis":      {"redis", "admin", "root"},
			"Memcached":  {"memcached", "admin", "root"},
			"MQTT":       {"admin", "root", "mqtt", "user", "guest", "test", "mosquitto", "emqx", "system"},
			"SOCKS5":     {"admin", "root", "socks5", "user", "guest", "test"},
			"DM":         {"SYSDBA", "SYSDBA", "SYSSSO", "SYSAUDITOR", "DM", "dm"},
			"Kingbase":   {"system", "admin", "kingbase"},
			"RTSP":       {"admin", "root", "user", "guest", "operator", "camera", "rtsp", "hikvision", "dahua", "onvif", "axis", "supervisor"},
			"TiDB":       {"root", "tidb", "admin", "test", "user"},
			"ClickHouse": {"default", "admin", "clickhouse", "root", "user", "system"},
			"GaussDB":    {"gaussdb", "admin", "omm", "postgres"},
		},
		// 默认密码字典
		DefaultPasswords: []string{
			"123456", "admin", "admin123", "root", "", "pass123", "Admin@9000", "pass@123", "password",
			"Password", "P@ssword123", "123123", "654321", "111111", "123", "1",
			"admin@123", "Admin@123", "admin123!@#", "{user}", "{user}1", "{user}111",
			"{user}123", "{user}@123", "{user}_123", "{user}#123", "{user}@111",
			"{user}@2019", "{user}@123#4", "P@ssw0rd!", "P@ssw0rd", "Passw0rd", "qwe123",
			"12345678", "test", "test123", "123qwe", "123qwe!@#", "123456789", "123321",
			"666666", "a123456.", "123456~a", "123456!a", "000000", "1234567890", "8888888",
			"!QAZ2wsx", "1qaz2wsx", "abc123", "abc123456", "1qaz@WSX", "a11111", "a12345",
			"Aa1234", "Aa1234.", "Aa12345", "a123456", "a123123", "Aa123123", "Aa123456",
			"Aa12345.", "sysadmin", "system", "1qaz!QAZ", "2wsx@WSX", "qwe123!@#", "Aa123456!",
			"A123456s!", "sa123456", "1q2w3e", "Charge123", "Aa123456789", "elastic123",
		},
		Threads:     10,
		Timeout:     5,
		MaxAttempts: 500,
	}

	// 全局漏洞检测配置
	GlobalVulnConfig = &VulnConfig{}

	// 全局工作池配置
	GlobalWorkerPoolConfig = &WorkerPoolConfig{
		PoolSize:  50,
		QueueSize: 1000,
	}

	// 全局端口列表配置
	GlobalPortListConfig = &PortListConfig{
		PortMap: map[string][]int{
			// 通用默认端口列表
			"default": {9440, 2379, 18848, 18083, 28848, 38848, 48848, 58848, 1883, 12222, 32222, 10022, 20022, 13389, 23389, 33389, 43389, 53389, 33890, 33891, 3307, 33060, 13306, 23306, 6380, 16379, 26379, 27019, 2443, 15060, 25060, 21, 22, 23, 53, 69, 80, 81, 88, 89, 135, 161, 445, 139, 137, 143, 389, 443, 512, 513, 514, 548, 873, 1433, 1521, 2181, 3306, 3389, 3690, 4848, 5000, 5001, 5432, 5632, 5900, 5901, 5902, 6379, 7000, 7001, 7002, 8000, 8001, 8007, 8008, 8009, 8069, 8080, 8081, 8088, 8089, 8090, 8091, 9060, 9090, 9091, 9200, 9300, 10000, 11211, 27017, 27018, 50000, 1080, 888, 1158, 2100, 2424, 2601, 2604, 3128, 5984, 7080, 8010, 8082, 8083, 8084, 8085, 8086, 8087, 8222, 8443, 8686, 8888, 9000, 9001, 9002, 9003, 9004, 9005, 9006, 9007, 9008, 9009, 9010, 9043, 9080, 9081, 9418, 9999, 50030, 50060, 50070, 82, 83, 84, 85, 86, 87, 7003, 7004, 7005, 7006, 7007, 7008, 7009, 7010, 7070, 7071, 7072, 7073, 7074, 7075, 7076, 7077, 7078, 7079, 8002, 8003, 8004, 8005, 8006, 8200, 90, 801, 8011, 8100, 8012, 8070, 99, 7777, 8028, 808, 38888, 8181, 800, 18080, 8099, 8899, 8360, 8300, 8800, 8180, 3505, 8053, 1000, 8989, 28017, 49166, 3000, 41516, 880, 8484, 6677, 8016, 7200, 9085, 5555, 8280, 1980, 8161, 7890, 8060, 6080, 8880, 8020, 889, 8881, 38501, 1010, 93, 6666, 100, 6789, 7060, 8018, 8022, 3050, 8787, 2000, 10001, 8013, 6888, 8040, 10021, 2011, 6006, 4000, 8055, 4430, 1723, 6060, 7788, 8066, 9898, 6001, 8801, 10040, 9998, 803, 6688, 10080, 8050, 7011, 40310, 18090, 802, 10003, 8014, 2080, 7288, 8044, 9992, 8889, 5644, 8886, 9500, 58031, 9020, 8015, 8887, 8021, 8700, 91, 9900, 9191, 3312, 8186, 8735, 8380, 1234, 38080, 9088, 9988, 2110, 21245, 3333, 2046, 9061, 2375, 9011, 8061, 8093, 9876, 8030, 8282, 60465, 2222, 98, 1100, 18081, 70, 8383, 5155, 92, 8188, 2517, 8062, 11324, 2008, 9231, 999, 28214, 16080, 8092, 8987, 8038, 809, 2010, 8983, 7700, 3535, 7921, 9093, 11080, 6778, 805, 9083, 8073, 10002, 114, 2012, 701, 8810, 8400, 9099, 8098, 8808, 20000, 8065, 8822, 15000, 9901, 11158, 1107, 28099, 12345, 2006, 9527, 51106, 688, 25006, 8045, 8023, 8029, 9997, 7048, 8580, 8585, 2001, 8035, 10088, 20022, 4001, 2013, 20808, 8095, 106, 3580, 7742, 8119, 6868, 32766, 50075, 7272, 3380, 3220, 7801, 5256, 5255, 10086, 1300, 5200, 8096, 6198, 6889, 3503, 6088, 9991, 806, 5050, 8183, 8688, 1001, 58080, 1182, 9025, 8112, 7776, 7321, 235, 8077, 8500, 11347, 7081, 8877, 8480, 9182, 58000, 8026, 11001, 10089, 5888, 8196, 8078, 9995, 2014, 5656, 8019, 5003, 8481, 6002, 9889, 9015, 8866, 8182, 8057, 8399, 10010, 8308, 511, 12881, 4016, 8042, 1039, 28080, 5678, 7500, 8051, 18801, 15018, 15888, 38443, 8123, 8144, 94, 9070, 1800, 9112, 8990, 3456, 2051, 9098, 444, 9131, 97, 7100, 7711, 7180, 11000, 8037, 6988, 122, 8885, 14007, 8184, 7012, 8079, 9888, 9301, 59999, 49705, 1979, 8900, 5080, 5013, 1550, 8844, 4850, 206, 5156, 8813, 3030, 1790, 8802, 9012, 5544, 3721, 8980, 10009, 8043, 8390, 7943, 8381, 8056, 7111, 1500, 7088, 5881, 9437, 5655, 8102, 6000, 65486, 4443, 10025, 8024, 8333, 8666, 103, 8, 9666, 8999, 9111, 8071, 9092, 522, 11381, 20806, 8041, 1085, 8864, 7900, 1700, 8036, 8032, 8033, 8111, 60022, 955, 3080, 8788, 7443, 8192, 6969, 9909, 5002, 9990, 188, 8910, 9022, 10004, 866, 8582, 4300, 9101, 6879, 8891, 4567, 4440, 10051, 10068, 50080, 8341, 30001, 6890, 8168, 8955, 16788, 8190, 18060, 7041, 42424, 8848, 15693, 2521, 19010, 18103, 6010, 8898, 9910, 9190, 9082, 8260, 8445, 1680, 8890, 8649, 30082, 3013, 30000, 2480, 7202, 9704, 5233, 8991, 11366, 7888, 8780, 7129, 6600, 9443, 47088, 7791, 18888, 50045, 15672, 9089, 2585, 60, 9494, 31945, 2060, 8610, 8860, 58060, 6118, 2348, 8097, 38000, 18880, 13382, 6611, 8064, 7101, 5081, 7380, 7942, 10016, 8027, 2093, 403, 9014, 8133, 6886, 95, 8058, 9201, 6443, 5966, 27000, 7017, 6680, 8401, 9036, 8988, 8806, 6180, 421, 423, 57880, 7778, 18881, 812, 15004, 9110, 8213, 8868, 1213, 8193, 8956, 1108, 778, 65000, 7020, 1122, 9031, 17000, 8039, 8600, 50090, 1863, 8191, 65, 6587, 8136, 9507, 132, 200, 2070, 308, 5811, 3465, 8680, 7999, 7084, 18082, 3938, 18001, 9595, 442, 4433, 7171, 9084, 7567, 811, 1128, 6003, 2125, 6090, 10007, 7022, 1949, 6565, 65001, 1301, 19244, 10087, 8025, 5098, 21080, 1200, 15801, 1005, 22343, 7086, 8601, 6259, 7102, 10333, 211, 10082, 18085, 180, 40000, 7021, 7702, 66, 38086, 666, 6603, 1212, 65493, 96, 9053, 7031, 23454, 30088, 6226, 8660, 6170, 8972, 9981, 48080, 9086, 10118, 40069, 28780, 20153, 20021, 20151, 58898, 10066, 1818, 9914, 55351, 8343, 18000, 6546, 3880, 8902, 22222, 19045, 5561, 7979, 5203, 8879, 50240, 49960, 2007, 1722, 8913, 8912, 9504, 8103, 8567, 1666, 8720, 8197, 3012, 8220, 9039, 5898, 925, 38517, 8382, 6842, 8895, 2808, 447, 3600, 3606, 9095, 45177, 19101, 171, 133, 8189, 7108, 10154, 47078, 6800, 8122, 381, 1443, 15580, 23352, 3443, 1180, 268, 2382, 43651, 10099, 65533, 7018, 60010, 60101, 6699, 2005, 18002, 2009, 59777, 591, 1933, 9013, 8477, 9696, 9030, 2015, 7925, 6510, 18803, 280, 5601, 2901, 2301, 5201, 302, 610, 8031, 5552, 8809, 6869, 9212, 17095, 20001, 8781, 25024, 5280, 7909, 17003, 1088, 7117, 20052, 1900, 10038, 30551, 9980, 9180, 59009, 28280, 7028, 61999, 7915, 8384, 9918, 9919, 55858, 7215, 77, 9845, 20140, 8288, 7856, 1982, 1123, 17777, 8839, 208, 2886, 877, 6101, 5100, 804, 983, 5600, 8402, 5887, 8322, 770, 13333, 7330, 3216, 31188, 47583, 8710, 22580, 1042, 2020, 34440, 20, 7703, 65055, 8997, 6543, 6388, 8283, 7201, 4040, 61081, 12001, 3588, 7123, 2490, 4389, 1313, 19080, 9050, 6920, 299, 20046, 8892, 9302, 7899, 30058, 7094, 6801, 321, 1356, 12333, 11362, 11372, 6602, 7709, 45149, 3668, 517, 9912, 9096, 8130, 7050, 7713, 40080, 8104, 13988, 18264, 8799, 55070, 23458, 8176, 9517, 9541, 9542, 9512, 8905, 11660, 1025, 44445, 44401, 17173, 436, 560, 733, 968, 602, 3133, 3398, 16580, 8488, 8901, 8512, 10443, 9113, 9119, 6606, 22080, 5560, 7, 5757, 1600, 8250, 10024, 10200, 333, 73, 7547, 8054, 6372, 223, 3737, 9800, 9019, 8067, 45692, 15400, 15698, 9038, 37006, 2086, 1002, 9188, 8094, 8201, 8202, 30030, 2663, 9105, 10017, 4503, 1104, 8893, 40001, 27779, 3010, 7083, 5010, 5501, 309, 1389, 10070, 10069, 10056, 3094, 10057, 10078, 10050, 10060, 10098, 4180, 10777, 270, 6365, 9801, 1046, 7140, 1004, 9198, 8465, 8548, 108, 30015, 8153, 1020, 50100, 8391, 34899, 7090, 6100, 8777, 8298, 8281, 7023, 3377},

			// 数据库服务端口列表
			"db": {
				// MySQL
				3306, 3307, 13306, 23306, 33060,
				// MSSQL
				1433, 1434, 2433,
				// PostgreSQL
				5432, 5433,
				// Oracle
				1521, 1522, 1523, 1524, 1526, 2483, 2484,
				// MongoDB
				27017, 27018, 27019, 28017,
				// Redis
				6379, 6380, 6381, 6382, 16379, 26379,
				// Cassandra
				9042, 9160,
				// Neo4j
				7474, 7687,
				// Elasticsearch
				9200, 9300,
				// Memcached
				11211, 11212,
				// DM(达梦数据库)
				5236, 5237, 5238,
				// Kingbase(人大金仓)
				6432, 54321,
				//Tidb
				4000,
				//ClickHouse
				8123, 9000, 9440,
			},

			// Web服务端口列表
			"web": {
				80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100,
				443, 800, 801, 808, 880, 888, 889, 1000, 1010, 1080, 1158, 1888, 2000, 2001, 2080, 2100,
				2222, 2601, 2604, 3000, 3128, 3333, 4000, 4001, 4002, 4100, 5000, 5001, 5555, 6000, 6001,
				6666, 6868, 7000, 7001, 7002, 7003, 7004, 7005, 7070, 7071, 7072, 7077, 7078, 7777, 7890,
				8000, 8001, 8002, 8003, 8004, 8005, 8006, 8007, 8008, 8009, 8010, 8011, 8012, 8016, 8018,
				8020, 8022, 8025, 8026, 8028, 8030, 8031, 8033, 8038, 8040, 8042, 8044, 8046, 8048, 8053,
				8060, 8069, 8070, 8080, 8081, 8082, 8083, 8084, 8085, 8086, 8087, 8088, 8089, 8090, 8091,
				8092, 8093, 8094, 8095, 8096, 8097, 8098, 8099, 8100, 8101, 8108, 8118, 8161, 8180, 8181,
				8200, 8222, 8280, 8300, 8360, 8443, 8484, 8800, 8848, 8880, 8881, 8888, 8899, 8983, 8989,
				9000, 9001, 9002, 9008, 9010, 9043, 9060, 9080, 9081, 9082, 9083, 9084, 9085, 9086, 9087,
				9088, 9089, 9090, 9091, 9092, 9093, 9094, 9095, 9096, 9097, 9098, 9099, 9100, 9200, 9300,
				9443, 9800, 9981, 9988, 9998, 9999, 10000, 10001, 10002, 10004, 10008, 10010, 10250, 12018,
				12443, 14000, 16080, 18000, 18001, 18002, 18004, 18008, 18080, 18082, 18088, 18090, 18098,
				20000, 20720, 20880, 28018, 18083, 2379,
			},

			// CDN常见Web端口列表（用于CDN域名扫描）
			"cdn-web": {
				80, 81, 82, 83, 85, 88, 443, 888, 3443, 4430, 4433, 4443, 5443, 7001, 8001, 8002, 8003, 8008, 8009, 8010, 8080, 8081, 8082, 8086, 8088, 8089, 8090, 8443, 9043, 9100, 9200, 9443, 10443,
			},
			"all": {
				1-65535,
			},
			
			// 邮件服务端口列表
			"mail": {
				// SMTP
				25, 465, 587,
				// POP3
				110, 995,
				// IMAP
				143, 993,
			},

			// 远程管理服务端口列表
			"remote": {
				// SSH
				22, 2222, 10022, 20022,
				// Telnet
				23, 2323,
				// RDP
				3389, 13389, 23389, 33389, 43389, 53389, 33890, 33891,
				// VNC
				5900, 5901, 5902, 5903, 5904, 5905, 5906, 5907, 5908, 5909, 5910,
			},

			// 文件服务端口列表
			"file": {
				// FTP
				21, 2121,
				// SFTP
				22,
				// SMB/CIFS
				139, 445,
				// NFS
				2049,
				// RSYNC
				873,
			},

			// 消息队列服务端口列表
			"mq": {
				// RabbitMQ
				5672, 15672, 25672, 4369,
				// Kafka
				9092, 9093,
				// ActiveMQ
				61616, 8161,
				// MQTT
				1883, 8883,
			},

			// 常见漏洞端口列表
			"vuln": {
				// MS17-010 SMB漏洞
				445,
				// Log4j2
				8080, 8443, 8000, 8001, 8002, 8003, 8004, 8005, 8006, 8007, 8008, 8009,
				// Spring Boot Actuator
				8080, 8081, 8090, 8091, 8092, 8093, 8094, 8095, 8096, 8097, 8098, 8099,
				// Tomcat AJP
				8009, 8019, 8029, 8039, 8049,
				// Shiro
				8080, 8081, 8082, 8083, 8084, 8085, 8086, 8087, 8088, 8089,
				// Fastjson
				8080, 8081, 8082, 8083, 8084, 8085, 8086, 8087, 8088, 8089,
			},

			// 未授权访问检测端口列表
			"unauth": {
				// Redis未授权
				6379, 6380, 6381, 6382, 16379, 26379,
				// MongoDB未授权
				27017, 27018, 27019, 28017,
				// Memcached未授权
				11211, 11212,
				// Elasticsearch未授权
				9200, 9300,
				// Zookeeper未授权
				2181, 2182, 2183, 2184, 2185,
				// JDWP未授权
				8000, 8453, 8454, 8455, 8456, 8457,
				// RMI未授权
				1090, 1098, 1099, 10999, 11099,
				// ADB未授权
				5555, 5037,
				// Modbus未授权
				502, 503, 504, 505, 506,
			},

			// Top 100常用端口列表
			"top100": {
				21, 22, 23, 25, 53, 69, 80, 81, 88, 89, 110, 111, 135, 137, 139, 143, 161, 389,
				443, 445, 465, 512, 513, 514, 548, 587, 631, 873, 993, 995, 1080, 1158, 1433, 1521,
				2049, 2181, 3306, 3389, 3690, 4848, 5000, 5001, 5432, 5632, 5900, 5901, 5902, 6379,
				7001, 7002, 8000, 8008, 8009, 8069, 8080, 8081, 8088, 8089, 8090, 8091, 8443, 9060,
				9090, 9091, 9200, 9300, 10000, 11211, 27017, 27018, 50000, 1090, 1098, 1099, 4444,
				4445, 4786, 5555, 5556, 6666, 6667, 7777, 8181, 8222, 8787, 8888, 9999, 10001, 10002,
				10003, 10004, 10005, 12345, 12346, 20000, 20001, 20002, 20003, 20004, 20005, 32768,
				32769, 32770, 32771, 32772, 32773, 32774, 32775, 32776, 32777, 32778, 32779, 32780,
				32781, 32782, 32783, 32784, 32785,
			},

			// 所有插件支持的端口列表
			"plugin": {
				// MySQL
				3306, 3307, 13306, 23306, 33060,
				// MSSQL
				1433, 1434, 2433,
				// PostgreSQL
				5432, 5433,
				// Oracle
				1521, 1522, 1523, 1524, 1526, 2483, 2484,
				// MongoDB
				27017, 27018, 27019, 28017,
				// Redis
				6379, 6380, 6381, 6382, 16379, 26379,
				// SSH
				22, 2222, 10022, 20022,
				// FTP
				21, 2121,
				// Telnet
				23, 2323,
				// SMTP
				25, 465, 587,
				// POP3
				110, 995,
				// IMAP
				143, 993,
				// RDP
				3389, 13389, 23389, 33389, 43389, 53389, 33890, 33891,
				// VNC
				5900, 5901, 5902, 5903, 5904, 5905,
				// SMB
				139, 445,
				// LDAP
				389, 636,
				// RSYNC
				873,
				// SNMP
				161, 162,
				// Cassandra
				9042, 9160,
				// Neo4j
				7474, 7687,
				// RabbitMQ
				5672, 15672,
				// Kafka
				9092, 9093,
				// ActiveMQ
				61616, 8161,
				// MQTT
				1883, 8883,
				// SOCKS5
				1080, 1081, 1090, 10800, 10801, 10808,
				// DM(达梦数据库)
				5236, 5237, 5238,
				// Kingbase(人大金仓)
				6432, 54321,
				// Memcached
				11211, 11212,
				// Zookeeper
				2181, 2182, 2183,
				// JDWP
				8000, 8453, 8454, 8455,
				// RMI
				1090, 1098, 1099, 10999, 11099,
				// ADB
				5555, 5037,
				// Modbus
				502, 503, 504,
				// MS17-010
				445,
				// Tomcat AJP
				8009, 8019, 8029,
				// RTSP
				554, 8554, 10554, 1935,
				// TiDB
				4000, 10080,
				// ClickHouse
				8123, 9000, 9440,
			},
		},
	}
)
