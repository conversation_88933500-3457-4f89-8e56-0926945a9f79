package main

import (
	"Lightx/internal/common"
	"Lightx/internal/core"
	"Lightx/pkg/helper"
	"fmt"
	"github.com/projectdiscovery/gologger"
	"os"
	"time"
)

func main() {
	// 开始计时
	startTime := time.Now()

	// 显示 Banner
	common.Banner()

	// 执行安全认证检查
	if !common.ValidateAuth() {
		os.Exit(1)
	}

	// 初始化选项和全局配置（但不再调用 Banner）
	options := common.NewOptions()
	// 此处直接设置 GlobalConfig，不再调用 InitGlobalConfig()
	common.GlobalConfig = options

	// 检查是否需要显示插件列表
	if common.GlobalConfig.ShowPluginList {
		helper.ShowPluginList()
		os.Exit(0)
	}

	// 创建并初始化引擎
	engine := core.NewEngine(options)
	if err := engine.Initialize(); err != nil {
		gologger.Error().Msg(err.Error())
		os.Exit(1)
	}

	// 运行引擎
	engine.Run()

	// 清理资源
	engine.Cleanup()

	// 输出扫描耗时
	gologger.Info().Msg(fmt.Sprintf("\u001B[32m扫描结束,总共耗时: %s\u001B[37m", time.Since(startTime)))
}
