/**
 * Loader generated by glad 2.0.0-beta on Sun Apr 14 17:03:38 2019
 *
 * Generator: C/C++
 * Specification: vk
 * Extensions: 3
 *
 * APIs:
 *  - vulkan=1.1
 *
 * Options:
 *  - MX_GLOBAL = False
 *  - LOADER = False
 *  - ALIAS = False
 *  - HEADER_ONLY = False
 *  - DEBUG = False
 *  - MX = False
 *
 * Commandline:
 *    --api='vulkan=1.1' --extensions='VK_EXT_debug_report,VK_KHR_surface,VK_KHR_swapchain' c
 *
 * Online:
 *    http://glad.sh/#api=vulkan%3D1.1&extensions=VK_EXT_debug_report%2CVK_KHR_surface%2CVK_KHR_swapchain&generator=c&options=
 *
 */

#ifndef GLAD_VULKAN_H_
#define GLAD_VULKAN_H_

#ifdef VULKAN_H_
    #error  header already included (API: vulkan), remove previous include!
#endif
#define VULKAN_H_ 1

#ifdef VULKAN_CORE_H_
    #error  header already included (API: vulkan), remove previous include!
#endif
#define VULKAN_CORE_H_ 1


#define GLAD_VULKAN

#ifdef __cplusplus
extern "C" {
#endif

#ifndef GLAD_PLATFORM_H_
#define GLAD_PLATFORM_H_

#ifndef GLAD_PLATFORM_WIN32
  #if defined(_WIN32) || defined(__WIN32__) || defined(WIN32) || defined(__MINGW32__)
    #define GLAD_PLATFORM_WIN32 1
  #else
    #define GLAD_PLATFORM_WIN32 0
  #endif
#endif

#ifndef GLAD_PLATFORM_APPLE
  #ifdef __APPLE__
    #define GLAD_PLATFORM_APPLE 1
  #else
    #define GLAD_PLATFORM_APPLE 0
  #endif
#endif

#ifndef GLAD_PLATFORM_EMSCRIPTEN
  #ifdef __EMSCRIPTEN__
    #define GLAD_PLATFORM_EMSCRIPTEN 1
  #else
    #define GLAD_PLATFORM_EMSCRIPTEN 0
  #endif
#endif

#ifndef GLAD_PLATFORM_UWP
  #if defined(_MSC_VER) && !defined(GLAD_INTERNAL_HAVE_WINAPIFAMILY)
    #ifdef __has_include
      #if __has_include(<winapifamily.h>)
        #define GLAD_INTERNAL_HAVE_WINAPIFAMILY 1
      #endif
    #elif _MSC_VER >= 1700 && !_USING_V110_SDK71_
      #define GLAD_INTERNAL_HAVE_WINAPIFAMILY 1
    #endif
  #endif

  #ifdef GLAD_INTERNAL_HAVE_WINAPIFAMILY
    #include <winapifamily.h>
    #if !WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP) && WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
      #define GLAD_PLATFORM_UWP 1
    #endif
  #endif

  #ifndef GLAD_PLATFORM_UWP
    #define GLAD_PLATFORM_UWP 0
  #endif
#endif

#ifdef __GNUC__
  #define GLAD_GNUC_EXTENSION __extension__
#else
  #define GLAD_GNUC_EXTENSION
#endif

#ifndef GLAD_API_CALL
  #if defined(GLAD_API_CALL_EXPORT)
    #if GLAD_PLATFORM_WIN32 || defined(__CYGWIN__)
      #if defined(GLAD_API_CALL_EXPORT_BUILD)
        #if defined(__GNUC__)
          #define GLAD_API_CALL __attribute__ ((dllexport)) extern
        #else
          #define GLAD_API_CALL __declspec(dllexport) extern
        #endif
      #else
        #if defined(__GNUC__)
          #define GLAD_API_CALL __attribute__ ((dllimport)) extern
        #else
          #define GLAD_API_CALL __declspec(dllimport) extern
        #endif
      #endif
    #elif defined(__GNUC__) && defined(GLAD_API_CALL_EXPORT_BUILD)
      #define GLAD_API_CALL __attribute__ ((visibility ("default"))) extern
    #else
      #define GLAD_API_CALL extern
    #endif
  #else
    #define GLAD_API_CALL extern
  #endif
#endif

#ifdef APIENTRY
  #define GLAD_API_PTR APIENTRY
#elif GLAD_PLATFORM_WIN32
  #define GLAD_API_PTR __stdcall
#else
  #define GLAD_API_PTR
#endif

#ifndef GLAPI
#define GLAPI GLAD_API_CALL
#endif

#ifndef GLAPIENTRY
#define GLAPIENTRY GLAD_API_PTR
#endif


#define GLAD_MAKE_VERSION(major, minor) (major * 10000 + minor)
#define GLAD_VERSION_MAJOR(version) (version / 10000)
#define GLAD_VERSION_MINOR(version) (version % 10000)

typedef void (*GLADapiproc)(void);

typedef GLADapiproc (*GLADloadfunc)(const char *name);
typedef GLADapiproc (*GLADuserptrloadfunc)(const char *name, void *userptr);

typedef void (*GLADprecallback)(const char *name, GLADapiproc apiproc, int len_args, ...);
typedef void (*GLADpostcallback)(void *ret, const char *name, GLADapiproc apiproc, int len_args, ...);

#endif /* GLAD_PLATFORM_H_ */

#define VK_ATTACHMENT_UNUSED (~0U)
#define VK_EXT_DEBUG_REPORT_EXTENSION_NAME "VK_EXT_debug_report"
#define VK_EXT_DEBUG_REPORT_SPEC_VERSION 9
#define VK_FALSE 0
#define VK_KHR_SURFACE_EXTENSION_NAME "VK_KHR_surface"
#define VK_KHR_SURFACE_SPEC_VERSION 25
#define VK_KHR_SWAPCHAIN_EXTENSION_NAME "VK_KHR_swapchain"
#define VK_KHR_SWAPCHAIN_SPEC_VERSION 70
#define VK_LOD_CLAMP_NONE 1000.0f
#define VK_LUID_SIZE 8
#define VK_MAX_DESCRIPTION_SIZE 256
#define VK_MAX_DEVICE_GROUP_SIZE 32
#define VK_MAX_EXTENSION_NAME_SIZE 256
#define VK_MAX_MEMORY_HEAPS 16
#define VK_MAX_MEMORY_TYPES 32
#define VK_MAX_PHYSICAL_DEVICE_NAME_SIZE 256
#define VK_QUEUE_FAMILY_EXTERNAL (~0U-1)
#define VK_QUEUE_FAMILY_IGNORED (~0U)
#define VK_REMAINING_ARRAY_LAYERS (~0U)
#define VK_REMAINING_MIP_LEVELS (~0U)
#define VK_SUBPASS_EXTERNAL (~0U)
#define VK_TRUE 1
#define VK_UUID_SIZE 16
#define VK_WHOLE_SIZE (~0ULL)


#include <glad/vk_platform.h>
#define VK_MAKE_VERSION(major, minor, patch) \
    (((major) << 22) | ((minor) << 12) | (patch))
#define VK_VERSION_MAJOR(version) ((uint32_t)(version) >> 22)
#define VK_VERSION_MINOR(version) (((uint32_t)(version) >> 12) & 0x3ff)
#define VK_VERSION_PATCH(version) ((uint32_t)(version) & 0xfff)
/* DEPRECATED: This define has been removed. Specific version defines (e.g. VK_API_VERSION_1_0), or the VK_MAKE_VERSION macro, should be used instead. */
/*#define VK_API_VERSION VK_MAKE_VERSION(1, 0, 0) // Patch version should always be set to 0 */
/* Vulkan 1.0 version number */
#define VK_API_VERSION_1_0 VK_MAKE_VERSION(1, 0, 0)/* Patch version should always be set to 0 */
/* Vulkan 1.1 version number */
#define VK_API_VERSION_1_1 VK_MAKE_VERSION(1, 1, 0)/* Patch version should always be set to 0 */
/* Version of this file */
#define VK_HEADER_VERSION 106
#define VK_DEFINE_HANDLE(object) typedef struct object##_T* object;
#if !defined(VK_DEFINE_NON_DISPATCHABLE_HANDLE)
#if defined(__LP64__) || defined(_WIN64) || (defined(__x86_64__) && !defined(__ILP32__) ) || defined(_M_X64) || defined(__ia64) || defined (_M_IA64) || defined(__aarch64__) || defined(__powerpc64__)
        #define VK_DEFINE_NON_DISPATCHABLE_HANDLE(object) typedef struct object##_T *object;
#else
        #define VK_DEFINE_NON_DISPATCHABLE_HANDLE(object) typedef uint64_t object;
#endif
#endif
#define VK_NULL_HANDLE 0








VK_DEFINE_HANDLE(VkInstance)
VK_DEFINE_HANDLE(VkPhysicalDevice)
VK_DEFINE_HANDLE(VkDevice)
VK_DEFINE_HANDLE(VkQueue)
VK_DEFINE_HANDLE(VkCommandBuffer)
VK_DEFINE_NON_DISPATCHABLE_HANDLE(VkDeviceMemory)
VK_DEFINE_NON_DISPATCHABLE_HANDLE(VkCommandPool)
VK_DEFINE_NON_DISPATCHABLE_HANDLE(VkBuffer)
VK_DEFINE_NON_DISPATCHABLE_HANDLE(VkBufferView)
VK_DEFINE_NON_DISPATCHABLE_HANDLE(VkImage)
VK_DEFINE_NON_DISPATCHABLE_HANDLE(VkImageView)
VK_DEFINE_NON_DISPATCHABLE_HANDLE(VkShaderModule)
VK_DEFINE_NON_DISPATCHABLE_HANDLE(VkPipeline)
VK_DEFINE_NON_DISPATCHABLE_HANDLE(VkPipelineLayout)
VK_DEFINE_NON_DISPATCHABLE_HANDLE(VkSampler)
VK_DEFINE_NON_DISPATCHABLE_HANDLE(VkDescriptorSet)
VK_DEFINE_NON_DISPATCHABLE_HANDLE(VkDescriptorSetLayout)
VK_DEFINE_NON_DISPATCHABLE_HANDLE(VkDescriptorPool)
VK_DEFINE_NON_DISPATCHABLE_HANDLE(VkFence)
VK_DEFINE_NON_DISPATCHABLE_HANDLE(VkSemaphore)
VK_DEFINE_NON_DISPATCHABLE_HANDLE(VkEvent)
VK_DEFINE_NON_DISPATCHABLE_HANDLE(VkQueryPool)
VK_DEFINE_NON_DISPATCHABLE_HANDLE(VkFramebuffer)
VK_DEFINE_NON_DISPATCHABLE_HANDLE(VkRenderPass)
VK_DEFINE_NON_DISPATCHABLE_HANDLE(VkPipelineCache)
VK_DEFINE_NON_DISPATCHABLE_HANDLE(VkDescriptorUpdateTemplate)
VK_DEFINE_NON_DISPATCHABLE_HANDLE(VkSamplerYcbcrConversion)
VK_DEFINE_NON_DISPATCHABLE_HANDLE(VkSurfaceKHR)
VK_DEFINE_NON_DISPATCHABLE_HANDLE(VkSwapchainKHR)
VK_DEFINE_NON_DISPATCHABLE_HANDLE(VkDebugReportCallbackEXT)
typedef enum VkAttachmentLoadOp {
    VK_ATTACHMENT_LOAD_OP_LOAD = 0,
    VK_ATTACHMENT_LOAD_OP_CLEAR = 1,
    VK_ATTACHMENT_LOAD_OP_DONT_CARE = 2
} VkAttachmentLoadOp;
typedef enum VkAttachmentStoreOp {
    VK_ATTACHMENT_STORE_OP_STORE = 0,
    VK_ATTACHMENT_STORE_OP_DONT_CARE = 1
} VkAttachmentStoreOp;
typedef enum VkBlendFactor {
    VK_BLEND_FACTOR_ZERO = 0,
    VK_BLEND_FACTOR_ONE = 1,
    VK_BLEND_FACTOR_SRC_COLOR = 2,
    VK_BLEND_FACTOR_ONE_MINUS_SRC_COLOR = 3,
    VK_BLEND_FACTOR_DST_COLOR = 4,
    VK_BLEND_FACTOR_ONE_MINUS_DST_COLOR = 5,
    VK_BLEND_FACTOR_SRC_ALPHA = 6,
    VK_BLEND_FACTOR_ONE_MINUS_SRC_ALPHA = 7,
    VK_BLEND_FACTOR_DST_ALPHA = 8,
    VK_BLEND_FACTOR_ONE_MINUS_DST_ALPHA = 9,
    VK_BLEND_FACTOR_CONSTANT_COLOR = 10,
    VK_BLEND_FACTOR_ONE_MINUS_CONSTANT_COLOR = 11,
    VK_BLEND_FACTOR_CONSTANT_ALPHA = 12,
    VK_BLEND_FACTOR_ONE_MINUS_CONSTANT_ALPHA = 13,
    VK_BLEND_FACTOR_SRC_ALPHA_SATURATE = 14,
    VK_BLEND_FACTOR_SRC1_COLOR = 15,
    VK_BLEND_FACTOR_ONE_MINUS_SRC1_COLOR = 16,
    VK_BLEND_FACTOR_SRC1_ALPHA = 17,
    VK_BLEND_FACTOR_ONE_MINUS_SRC1_ALPHA = 18
} VkBlendFactor;
typedef enum VkBlendOp {
    VK_BLEND_OP_ADD = 0,
    VK_BLEND_OP_SUBTRACT = 1,
    VK_BLEND_OP_REVERSE_SUBTRACT = 2,
    VK_BLEND_OP_MIN = 3,
    VK_BLEND_OP_MAX = 4
} VkBlendOp;
typedef enum VkBorderColor {
    VK_BORDER_COLOR_FLOAT_TRANSPARENT_BLACK = 0,
    VK_BORDER_COLOR_INT_TRANSPARENT_BLACK = 1,
    VK_BORDER_COLOR_FLOAT_OPAQUE_BLACK = 2,
    VK_BORDER_COLOR_INT_OPAQUE_BLACK = 3,
    VK_BORDER_COLOR_FLOAT_OPAQUE_WHITE = 4,
    VK_BORDER_COLOR_INT_OPAQUE_WHITE = 5
} VkBorderColor;

typedef enum VkPipelineCacheHeaderVersion {
    VK_PIPELINE_CACHE_HEADER_VERSION_ONE = 1
} VkPipelineCacheHeaderVersion;

typedef enum VkDeviceQueueCreateFlagBits {
    VK_DEVICE_QUEUE_CREATE_PROTECTED_BIT = 1
} VkDeviceQueueCreateFlagBits;
typedef enum VkBufferCreateFlagBits {
    VK_BUFFER_CREATE_SPARSE_BINDING_BIT = 1,
    VK_BUFFER_CREATE_SPARSE_RESIDENCY_BIT = 2,
    VK_BUFFER_CREATE_SPARSE_ALIASED_BIT = 4,
    VK_BUFFER_CREATE_PROTECTED_BIT = 8
} VkBufferCreateFlagBits;
typedef enum VkBufferUsageFlagBits {
    VK_BUFFER_USAGE_TRANSFER_SRC_BIT = 1,
    VK_BUFFER_USAGE_TRANSFER_DST_BIT = 2,
    VK_BUFFER_USAGE_UNIFORM_TEXEL_BUFFER_BIT = 4,
    VK_BUFFER_USAGE_STORAGE_TEXEL_BUFFER_BIT = 8,
    VK_BUFFER_USAGE_UNIFORM_BUFFER_BIT = 16,
    VK_BUFFER_USAGE_STORAGE_BUFFER_BIT = 32,
    VK_BUFFER_USAGE_INDEX_BUFFER_BIT = 64,
    VK_BUFFER_USAGE_VERTEX_BUFFER_BIT = 128,
    VK_BUFFER_USAGE_INDIRECT_BUFFER_BIT = 256
} VkBufferUsageFlagBits;
typedef enum VkColorComponentFlagBits {
    VK_COLOR_COMPONENT_R_BIT = 1,
    VK_COLOR_COMPONENT_G_BIT = 2,
    VK_COLOR_COMPONENT_B_BIT = 4,
    VK_COLOR_COMPONENT_A_BIT = 8
} VkColorComponentFlagBits;
typedef enum VkComponentSwizzle {
    VK_COMPONENT_SWIZZLE_IDENTITY = 0,
    VK_COMPONENT_SWIZZLE_ZERO = 1,
    VK_COMPONENT_SWIZZLE_ONE = 2,
    VK_COMPONENT_SWIZZLE_R = 3,
    VK_COMPONENT_SWIZZLE_G = 4,
    VK_COMPONENT_SWIZZLE_B = 5,
    VK_COMPONENT_SWIZZLE_A = 6
} VkComponentSwizzle;
typedef enum VkCommandPoolCreateFlagBits {
    VK_COMMAND_POOL_CREATE_TRANSIENT_BIT = 1,
    VK_COMMAND_POOL_CREATE_RESET_COMMAND_BUFFER_BIT = 2,
    VK_COMMAND_POOL_CREATE_PROTECTED_BIT = 4
} VkCommandPoolCreateFlagBits;
typedef enum VkCommandPoolResetFlagBits {
    VK_COMMAND_POOL_RESET_RELEASE_RESOURCES_BIT = 1
} VkCommandPoolResetFlagBits;
typedef enum VkCommandBufferResetFlagBits {
    VK_COMMAND_BUFFER_RESET_RELEASE_RESOURCES_BIT = 1
} VkCommandBufferResetFlagBits;
typedef enum VkCommandBufferLevel {
    VK_COMMAND_BUFFER_LEVEL_PRIMARY = 0,
    VK_COMMAND_BUFFER_LEVEL_SECONDARY = 1
} VkCommandBufferLevel;
typedef enum VkCommandBufferUsageFlagBits {
    VK_COMMAND_BUFFER_USAGE_ONE_TIME_SUBMIT_BIT = 1,
    VK_COMMAND_BUFFER_USAGE_RENDER_PASS_CONTINUE_BIT = 2,
    VK_COMMAND_BUFFER_USAGE_SIMULTANEOUS_USE_BIT = 4
} VkCommandBufferUsageFlagBits;
typedef enum VkCompareOp {
    VK_COMPARE_OP_NEVER = 0,
    VK_COMPARE_OP_LESS = 1,
    VK_COMPARE_OP_EQUAL = 2,
    VK_COMPARE_OP_LESS_OR_EQUAL = 3,
    VK_COMPARE_OP_GREATER = 4,
    VK_COMPARE_OP_NOT_EQUAL = 5,
    VK_COMPARE_OP_GREATER_OR_EQUAL = 6,
    VK_COMPARE_OP_ALWAYS = 7
} VkCompareOp;
typedef enum VkCullModeFlagBits {
    VK_CULL_MODE_NONE = 0,
    VK_CULL_MODE_FRONT_BIT = 1,
    VK_CULL_MODE_BACK_BIT = 2,
    VK_CULL_MODE_FRONT_AND_BACK = 0x00000003
} VkCullModeFlagBits;
typedef enum VkDescriptorType {
    VK_DESCRIPTOR_TYPE_SAMPLER = 0,
    VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER = 1,
    VK_DESCRIPTOR_TYPE_SAMPLED_IMAGE = 2,
    VK_DESCRIPTOR_TYPE_STORAGE_IMAGE = 3,
    VK_DESCRIPTOR_TYPE_UNIFORM_TEXEL_BUFFER = 4,
    VK_DESCRIPTOR_TYPE_STORAGE_TEXEL_BUFFER = 5,
    VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER = 6,
    VK_DESCRIPTOR_TYPE_STORAGE_BUFFER = 7,
    VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER_DYNAMIC = 8,
    VK_DESCRIPTOR_TYPE_STORAGE_BUFFER_DYNAMIC = 9,
    VK_DESCRIPTOR_TYPE_INPUT_ATTACHMENT = 10
} VkDescriptorType;
typedef enum VkDynamicState {
    VK_DYNAMIC_STATE_VIEWPORT = 0,
    VK_DYNAMIC_STATE_SCISSOR = 1,
    VK_DYNAMIC_STATE_LINE_WIDTH = 2,
    VK_DYNAMIC_STATE_DEPTH_BIAS = 3,
    VK_DYNAMIC_STATE_BLEND_CONSTANTS = 4,
    VK_DYNAMIC_STATE_DEPTH_BOUNDS = 5,
    VK_DYNAMIC_STATE_STENCIL_COMPARE_MASK = 6,
    VK_DYNAMIC_STATE_STENCIL_WRITE_MASK = 7,
    VK_DYNAMIC_STATE_STENCIL_REFERENCE = 8,
    VK_DYNAMIC_STATE_RANGE_SIZE = (VK_DYNAMIC_STATE_STENCIL_REFERENCE - VK_DYNAMIC_STATE_VIEWPORT + 1)
} VkDynamicState;
typedef enum VkFenceCreateFlagBits {
    VK_FENCE_CREATE_SIGNALED_BIT = 1
} VkFenceCreateFlagBits;
typedef enum VkPolygonMode {
    VK_POLYGON_MODE_FILL = 0,
    VK_POLYGON_MODE_LINE = 1,
    VK_POLYGON_MODE_POINT = 2
} VkPolygonMode;
typedef enum VkFormat {
    VK_FORMAT_UNDEFINED = 0,
    VK_FORMAT_R4G4_UNORM_PACK8 = 1,
    VK_FORMAT_R4G4B4A4_UNORM_PACK16 = 2,
    VK_FORMAT_B4G4R4A4_UNORM_PACK16 = 3,
    VK_FORMAT_R5G6B5_UNORM_PACK16 = 4,
    VK_FORMAT_B5G6R5_UNORM_PACK16 = 5,
    VK_FORMAT_R5G5B5A1_UNORM_PACK16 = 6,
    VK_FORMAT_B5G5R5A1_UNORM_PACK16 = 7,
    VK_FORMAT_A1R5G5B5_UNORM_PACK16 = 8,
    VK_FORMAT_R8_UNORM = 9,
    VK_FORMAT_R8_SNORM = 10,
    VK_FORMAT_R8_USCALED = 11,
    VK_FORMAT_R8_SSCALED = 12,
    VK_FORMAT_R8_UINT = 13,
    VK_FORMAT_R8_SINT = 14,
    VK_FORMAT_R8_SRGB = 15,
    VK_FORMAT_R8G8_UNORM = 16,
    VK_FORMAT_R8G8_SNORM = 17,
    VK_FORMAT_R8G8_USCALED = 18,
    VK_FORMAT_R8G8_SSCALED = 19,
    VK_FORMAT_R8G8_UINT = 20,
    VK_FORMAT_R8G8_SINT = 21,
    VK_FORMAT_R8G8_SRGB = 22,
    VK_FORMAT_R8G8B8_UNORM = 23,
    VK_FORMAT_R8G8B8_SNORM = 24,
    VK_FORMAT_R8G8B8_USCALED = 25,
    VK_FORMAT_R8G8B8_SSCALED = 26,
    VK_FORMAT_R8G8B8_UINT = 27,
    VK_FORMAT_R8G8B8_SINT = 28,
    VK_FORMAT_R8G8B8_SRGB = 29,
    VK_FORMAT_B8G8R8_UNORM = 30,
    VK_FORMAT_B8G8R8_SNORM = 31,
    VK_FORMAT_B8G8R8_USCALED = 32,
    VK_FORMAT_B8G8R8_SSCALED = 33,
    VK_FORMAT_B8G8R8_UINT = 34,
    VK_FORMAT_B8G8R8_SINT = 35,
    VK_FORMAT_B8G8R8_SRGB = 36,
    VK_FORMAT_R8G8B8A8_UNORM = 37,
    VK_FORMAT_R8G8B8A8_SNORM = 38,
    VK_FORMAT_R8G8B8A8_USCALED = 39,
    VK_FORMAT_R8G8B8A8_SSCALED = 40,
    VK_FORMAT_R8G8B8A8_UINT = 41,
    VK_FORMAT_R8G8B8A8_SINT = 42,
    VK_FORMAT_R8G8B8A8_SRGB = 43,
    VK_FORMAT_B8G8R8A8_UNORM = 44,
    VK_FORMAT_B8G8R8A8_SNORM = 45,
    VK_FORMAT_B8G8R8A8_USCALED = 46,
    VK_FORMAT_B8G8R8A8_SSCALED = 47,
    VK_FORMAT_B8G8R8A8_UINT = 48,
    VK_FORMAT_B8G8R8A8_SINT = 49,
    VK_FORMAT_B8G8R8A8_SRGB = 50,
    VK_FORMAT_A8B8G8R8_UNORM_PACK32 = 51,
    VK_FORMAT_A8B8G8R8_SNORM_PACK32 = 52,
    VK_FORMAT_A8B8G8R8_USCALED_PACK32 = 53,
    VK_FORMAT_A8B8G8R8_SSCALED_PACK32 = 54,
    VK_FORMAT_A8B8G8R8_UINT_PACK32 = 55,
    VK_FORMAT_A8B8G8R8_SINT_PACK32 = 56,
    VK_FORMAT_A8B8G8R8_SRGB_PACK32 = 57,
    VK_FORMAT_A2R10G10B10_UNORM_PACK32 = 58,
    VK_FORMAT_A2R10G10B10_SNORM_PACK32 = 59,
    VK_FORMAT_A2R10G10B10_USCALED_PACK32 = 60,
    VK_FORMAT_A2R10G10B10_SSCALED_PACK32 = 61,
    VK_FORMAT_A2R10G10B10_UINT_PACK32 = 62,
    VK_FORMAT_A2R10G10B10_SINT_PACK32 = 63,
    VK_FORMAT_A2B10G10R10_UNORM_PACK32 = 64,
    VK_FORMAT_A2B10G10R10_SNORM_PACK32 = 65,
    VK_FORMAT_A2B10G10R10_USCALED_PACK32 = 66,
    VK_FORMAT_A2B10G10R10_SSCALED_PACK32 = 67,
    VK_FORMAT_A2B10G10R10_UINT_PACK32 = 68,
    VK_FORMAT_A2B10G10R10_SINT_PACK32 = 69,
    VK_FORMAT_R16_UNORM = 70,
    VK_FORMAT_R16_SNORM = 71,
    VK_FORMAT_R16_USCALED = 72,
    VK_FORMAT_R16_SSCALED = 73,
    VK_FORMAT_R16_UINT = 74,
    VK_FORMAT_R16_SINT = 75,
    VK_FORMAT_R16_SFLOAT = 76,
    VK_FORMAT_R16G16_UNORM = 77,
    VK_FORMAT_R16G16_SNORM = 78,
    VK_FORMAT_R16G16_USCALED = 79,
    VK_FORMAT_R16G16_SSCALED = 80,
    VK_FORMAT_R16G16_UINT = 81,
    VK_FORMAT_R16G16_SINT = 82,
    VK_FORMAT_R16G16_SFLOAT = 83,
    VK_FORMAT_R16G16B16_UNORM = 84,
    VK_FORMAT_R16G16B16_SNORM = 85,
    VK_FORMAT_R16G16B16_USCALED = 86,
    VK_FORMAT_R16G16B16_SSCALED = 87,
    VK_FORMAT_R16G16B16_UINT = 88,
    VK_FORMAT_R16G16B16_SINT = 89,
    VK_FORMAT_R16G16B16_SFLOAT = 90,
    VK_FORMAT_R16G16B16A16_UNORM = 91,
    VK_FORMAT_R16G16B16A16_SNORM = 92,
    VK_FORMAT_R16G16B16A16_USCALED = 93,
    VK_FORMAT_R16G16B16A16_SSCALED = 94,
    VK_FORMAT_R16G16B16A16_UINT = 95,
    VK_FORMAT_R16G16B16A16_SINT = 96,
    VK_FORMAT_R16G16B16A16_SFLOAT = 97,
    VK_FORMAT_R32_UINT = 98,
    VK_FORMAT_R32_SINT = 99,
    VK_FORMAT_R32_SFLOAT = 100,
    VK_FORMAT_R32G32_UINT = 101,
    VK_FORMAT_R32G32_SINT = 102,
    VK_FORMAT_R32G32_SFLOAT = 103,
    VK_FORMAT_R32G32B32_UINT = 104,
    VK_FORMAT_R32G32B32_SINT = 105,
    VK_FORMAT_R32G32B32_SFLOAT = 106,
    VK_FORMAT_R32G32B32A32_UINT = 107,
    VK_FORMAT_R32G32B32A32_SINT = 108,
    VK_FORMAT_R32G32B32A32_SFLOAT = 109,
    VK_FORMAT_R64_UINT = 110,
    VK_FORMAT_R64_SINT = 111,
    VK_FORMAT_R64_SFLOAT = 112,
    VK_FORMAT_R64G64_UINT = 113,
    VK_FORMAT_R64G64_SINT = 114,
    VK_FORMAT_R64G64_SFLOAT = 115,
    VK_FORMAT_R64G64B64_UINT = 116,
    VK_FORMAT_R64G64B64_SINT = 117,
    VK_FORMAT_R64G64B64_SFLOAT = 118,
    VK_FORMAT_R64G64B64A64_UINT = 119,
    VK_FORMAT_R64G64B64A64_SINT = 120,
    VK_FORMAT_R64G64B64A64_SFLOAT = 121,
    VK_FORMAT_B10G11R11_UFLOAT_PACK32 = 122,
    VK_FORMAT_E5B9G9R9_UFLOAT_PACK32 = 123,
    VK_FORMAT_D16_UNORM = 124,
    VK_FORMAT_X8_D24_UNORM_PACK32 = 125,
    VK_FORMAT_D32_SFLOAT = 126,
    VK_FORMAT_S8_UINT = 127,
    VK_FORMAT_D16_UNORM_S8_UINT = 128,
    VK_FORMAT_D24_UNORM_S8_UINT = 129,
    VK_FORMAT_D32_SFLOAT_S8_UINT = 130,
    VK_FORMAT_BC1_RGB_UNORM_BLOCK = 131,
    VK_FORMAT_BC1_RGB_SRGB_BLOCK = 132,
    VK_FORMAT_BC1_RGBA_UNORM_BLOCK = 133,
    VK_FORMAT_BC1_RGBA_SRGB_BLOCK = 134,
    VK_FORMAT_BC2_UNORM_BLOCK = 135,
    VK_FORMAT_BC2_SRGB_BLOCK = 136,
    VK_FORMAT_BC3_UNORM_BLOCK = 137,
    VK_FORMAT_BC3_SRGB_BLOCK = 138,
    VK_FORMAT_BC4_UNORM_BLOCK = 139,
    VK_FORMAT_BC4_SNORM_BLOCK = 140,
    VK_FORMAT_BC5_UNORM_BLOCK = 141,
    VK_FORMAT_BC5_SNORM_BLOCK = 142,
    VK_FORMAT_BC6H_UFLOAT_BLOCK = 143,
    VK_FORMAT_BC6H_SFLOAT_BLOCK = 144,
    VK_FORMAT_BC7_UNORM_BLOCK = 145,
    VK_FORMAT_BC7_SRGB_BLOCK = 146,
    VK_FORMAT_ETC2_R8G8B8_UNORM_BLOCK = 147,
    VK_FORMAT_ETC2_R8G8B8_SRGB_BLOCK = 148,
    VK_FORMAT_ETC2_R8G8B8A1_UNORM_BLOCK = 149,
    VK_FORMAT_ETC2_R8G8B8A1_SRGB_BLOCK = 150,
    VK_FORMAT_ETC2_R8G8B8A8_UNORM_BLOCK = 151,
    VK_FORMAT_ETC2_R8G8B8A8_SRGB_BLOCK = 152,
    VK_FORMAT_EAC_R11_UNORM_BLOCK = 153,
    VK_FORMAT_EAC_R11_SNORM_BLOCK = 154,
    VK_FORMAT_EAC_R11G11_UNORM_BLOCK = 155,
    VK_FORMAT_EAC_R11G11_SNORM_BLOCK = 156,
    VK_FORMAT_ASTC_4x4_UNORM_BLOCK = 157,
    VK_FORMAT_ASTC_4x4_SRGB_BLOCK = 158,
    VK_FORMAT_ASTC_5x4_UNORM_BLOCK = 159,
    VK_FORMAT_ASTC_5x4_SRGB_BLOCK = 160,
    VK_FORMAT_ASTC_5x5_UNORM_BLOCK = 161,
    VK_FORMAT_ASTC_5x5_SRGB_BLOCK = 162,
    VK_FORMAT_ASTC_6x5_UNORM_BLOCK = 163,
    VK_FORMAT_ASTC_6x5_SRGB_BLOCK = 164,
    VK_FORMAT_ASTC_6x6_UNORM_BLOCK = 165,
    VK_FORMAT_ASTC_6x6_SRGB_BLOCK = 166,
    VK_FORMAT_ASTC_8x5_UNORM_BLOCK = 167,
    VK_FORMAT_ASTC_8x5_SRGB_BLOCK = 168,
    VK_FORMAT_ASTC_8x6_UNORM_BLOCK = 169,
    VK_FORMAT_ASTC_8x6_SRGB_BLOCK = 170,
    VK_FORMAT_ASTC_8x8_UNORM_BLOCK = 171,
    VK_FORMAT_ASTC_8x8_SRGB_BLOCK = 172,
    VK_FORMAT_ASTC_10x5_UNORM_BLOCK = 173,
    VK_FORMAT_ASTC_10x5_SRGB_BLOCK = 174,
    VK_FORMAT_ASTC_10x6_UNORM_BLOCK = 175,
    VK_FORMAT_ASTC_10x6_SRGB_BLOCK = 176,
    VK_FORMAT_ASTC_10x8_UNORM_BLOCK = 177,
    VK_FORMAT_ASTC_10x8_SRGB_BLOCK = 178,
    VK_FORMAT_ASTC_10x10_UNORM_BLOCK = 179,
    VK_FORMAT_ASTC_10x10_SRGB_BLOCK = 180,
    VK_FORMAT_ASTC_12x10_UNORM_BLOCK = 181,
    VK_FORMAT_ASTC_12x10_SRGB_BLOCK = 182,
    VK_FORMAT_ASTC_12x12_UNORM_BLOCK = 183,
    VK_FORMAT_ASTC_12x12_SRGB_BLOCK = 184,
    VK_FORMAT_G8B8G8R8_422_UNORM = 1000156000,
    VK_FORMAT_B8G8R8G8_422_UNORM = 1000156001,
    VK_FORMAT_G8_B8_R8_3PLANE_420_UNORM = 1000156002,
    VK_FORMAT_G8_B8R8_2PLANE_420_UNORM = 1000156003,
    VK_FORMAT_G8_B8_R8_3PLANE_422_UNORM = 1000156004,
    VK_FORMAT_G8_B8R8_2PLANE_422_UNORM = 1000156005,
    VK_FORMAT_G8_B8_R8_3PLANE_444_UNORM = 1000156006,
    VK_FORMAT_R10X6_UNORM_PACK16 = 1000156007,
    VK_FORMAT_R10X6G10X6_UNORM_2PACK16 = 1000156008,
    VK_FORMAT_R10X6G10X6B10X6A10X6_UNORM_4PACK16 = 1000156009,
    VK_FORMAT_G10X6B10X6G10X6R10X6_422_UNORM_4PACK16 = 1000156010,
    VK_FORMAT_B10X6G10X6R10X6G10X6_422_UNORM_4PACK16 = 1000156011,
    VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_420_UNORM_3PACK16 = 1000156012,
    VK_FORMAT_G10X6_B10X6R10X6_2PLANE_420_UNORM_3PACK16 = 1000156013,
    VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_422_UNORM_3PACK16 = 1000156014,
    VK_FORMAT_G10X6_B10X6R10X6_2PLANE_422_UNORM_3PACK16 = 1000156015,
    VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_444_UNORM_3PACK16 = 1000156016,
    VK_FORMAT_R12X4_UNORM_PACK16 = 1000156017,
    VK_FORMAT_R12X4G12X4_UNORM_2PACK16 = 1000156018,
    VK_FORMAT_R12X4G12X4B12X4A12X4_UNORM_4PACK16 = 1000156019,
    VK_FORMAT_G12X4B12X4G12X4R12X4_422_UNORM_4PACK16 = 1000156020,
    VK_FORMAT_B12X4G12X4R12X4G12X4_422_UNORM_4PACK16 = 1000156021,
    VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_420_UNORM_3PACK16 = 1000156022,
    VK_FORMAT_G12X4_B12X4R12X4_2PLANE_420_UNORM_3PACK16 = 1000156023,
    VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_422_UNORM_3PACK16 = 1000156024,
    VK_FORMAT_G12X4_B12X4R12X4_2PLANE_422_UNORM_3PACK16 = 1000156025,
    VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_444_UNORM_3PACK16 = 1000156026,
    VK_FORMAT_G16B16G16R16_422_UNORM = 1000156027,
    VK_FORMAT_B16G16R16G16_422_UNORM = 1000156028,
    VK_FORMAT_G16_B16_R16_3PLANE_420_UNORM = 1000156029,
    VK_FORMAT_G16_B16R16_2PLANE_420_UNORM = 1000156030,
    VK_FORMAT_G16_B16_R16_3PLANE_422_UNORM = 1000156031,
    VK_FORMAT_G16_B16R16_2PLANE_422_UNORM = 1000156032,
    VK_FORMAT_G16_B16_R16_3PLANE_444_UNORM = 1000156033
} VkFormat;
typedef enum VkFormatFeatureFlagBits {
    VK_FORMAT_FEATURE_SAMPLED_IMAGE_BIT = 1,
    VK_FORMAT_FEATURE_STORAGE_IMAGE_BIT = 2,
    VK_FORMAT_FEATURE_STORAGE_IMAGE_ATOMIC_BIT = 4,
    VK_FORMAT_FEATURE_UNIFORM_TEXEL_BUFFER_BIT = 8,
    VK_FORMAT_FEATURE_STORAGE_TEXEL_BUFFER_BIT = 16,
    VK_FORMAT_FEATURE_STORAGE_TEXEL_BUFFER_ATOMIC_BIT = 32,
    VK_FORMAT_FEATURE_VERTEX_BUFFER_BIT = 64,
    VK_FORMAT_FEATURE_COLOR_ATTACHMENT_BIT = 128,
    VK_FORMAT_FEATURE_COLOR_ATTACHMENT_BLEND_BIT = 256,
    VK_FORMAT_FEATURE_DEPTH_STENCIL_ATTACHMENT_BIT = 512,
    VK_FORMAT_FEATURE_BLIT_SRC_BIT = 1024,
    VK_FORMAT_FEATURE_BLIT_DST_BIT = 2048,
    VK_FORMAT_FEATURE_SAMPLED_IMAGE_FILTER_LINEAR_BIT = 4096,
    VK_FORMAT_FEATURE_TRANSFER_SRC_BIT = 16384,
    VK_FORMAT_FEATURE_TRANSFER_DST_BIT = 32768,
    VK_FORMAT_FEATURE_MIDPOINT_CHROMA_SAMPLES_BIT = 131072,
    VK_FORMAT_FEATURE_SAMPLED_IMAGE_YCBCR_CONVERSION_LINEAR_FILTER_BIT = 262144,
    VK_FORMAT_FEATURE_SAMPLED_IMAGE_YCBCR_CONVERSION_SEPARATE_RECONSTRUCTION_FILTER_BIT = 524288,
    VK_FORMAT_FEATURE_SAMPLED_IMAGE_YCBCR_CONVERSION_CHROMA_RECONSTRUCTION_EXPLICIT_BIT = 1048576,
    VK_FORMAT_FEATURE_SAMPLED_IMAGE_YCBCR_CONVERSION_CHROMA_RECONSTRUCTION_EXPLICIT_FORCEABLE_BIT = 2097152,
    VK_FORMAT_FEATURE_DISJOINT_BIT = 4194304,
    VK_FORMAT_FEATURE_COSITED_CHROMA_SAMPLES_BIT = 8388608
} VkFormatFeatureFlagBits;
typedef enum VkFrontFace {
    VK_FRONT_FACE_COUNTER_CLOCKWISE = 0,
    VK_FRONT_FACE_CLOCKWISE = 1
} VkFrontFace;
typedef enum VkImageAspectFlagBits {
    VK_IMAGE_ASPECT_COLOR_BIT = 1,
    VK_IMAGE_ASPECT_DEPTH_BIT = 2,
    VK_IMAGE_ASPECT_STENCIL_BIT = 4,
    VK_IMAGE_ASPECT_METADATA_BIT = 8,
    VK_IMAGE_ASPECT_PLANE_0_BIT = 16,
    VK_IMAGE_ASPECT_PLANE_1_BIT = 32,
    VK_IMAGE_ASPECT_PLANE_2_BIT = 64
} VkImageAspectFlagBits;
typedef enum VkImageCreateFlagBits {
    VK_IMAGE_CREATE_SPARSE_BINDING_BIT = 1,
    VK_IMAGE_CREATE_SPARSE_RESIDENCY_BIT = 2,
    VK_IMAGE_CREATE_SPARSE_ALIASED_BIT = 4,
    VK_IMAGE_CREATE_MUTABLE_FORMAT_BIT = 8,
    VK_IMAGE_CREATE_CUBE_COMPATIBLE_BIT = 16,
    VK_IMAGE_CREATE_ALIAS_BIT = 1024,
    VK_IMAGE_CREATE_SPLIT_INSTANCE_BIND_REGIONS_BIT = 64,
    VK_IMAGE_CREATE_2D_ARRAY_COMPATIBLE_BIT = 32,
    VK_IMAGE_CREATE_BLOCK_TEXEL_VIEW_COMPATIBLE_BIT = 128,
    VK_IMAGE_CREATE_EXTENDED_USAGE_BIT = 256,
    VK_IMAGE_CREATE_PROTECTED_BIT = 2048,
    VK_IMAGE_CREATE_DISJOINT_BIT = 512
} VkImageCreateFlagBits;
typedef enum VkImageLayout {
    VK_IMAGE_LAYOUT_UNDEFINED = 0,
    VK_IMAGE_LAYOUT_GENERAL = 1,
    VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL = 2,
    VK_IMAGE_LAYOUT_DEPTH_STENCIL_ATTACHMENT_OPTIMAL = 3,
    VK_IMAGE_LAYOUT_DEPTH_STENCIL_READ_ONLY_OPTIMAL = 4,
    VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL = 5,
    VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL = 6,
    VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL = 7,
    VK_IMAGE_LAYOUT_PREINITIALIZED = 8,
    VK_IMAGE_LAYOUT_DEPTH_READ_ONLY_STENCIL_ATTACHMENT_OPTIMAL = 1000117000,
    VK_IMAGE_LAYOUT_DEPTH_ATTACHMENT_STENCIL_READ_ONLY_OPTIMAL = 1000117001,
    VK_IMAGE_LAYOUT_PRESENT_SRC_KHR = 1000001002
} VkImageLayout;
typedef enum VkImageTiling {
    VK_IMAGE_TILING_OPTIMAL = 0,
    VK_IMAGE_TILING_LINEAR = 1
} VkImageTiling;
typedef enum VkImageType {
    VK_IMAGE_TYPE_1D = 0,
    VK_IMAGE_TYPE_2D = 1,
    VK_IMAGE_TYPE_3D = 2
} VkImageType;
typedef enum VkImageUsageFlagBits {
    VK_IMAGE_USAGE_TRANSFER_SRC_BIT = 1,
    VK_IMAGE_USAGE_TRANSFER_DST_BIT = 2,
    VK_IMAGE_USAGE_SAMPLED_BIT = 4,
    VK_IMAGE_USAGE_STORAGE_BIT = 8,
    VK_IMAGE_USAGE_COLOR_ATTACHMENT_BIT = 16,
    VK_IMAGE_USAGE_DEPTH_STENCIL_ATTACHMENT_BIT = 32,
    VK_IMAGE_USAGE_TRANSIENT_ATTACHMENT_BIT = 64,
    VK_IMAGE_USAGE_INPUT_ATTACHMENT_BIT = 128
} VkImageUsageFlagBits;

typedef enum VkImageViewType {
    VK_IMAGE_VIEW_TYPE_1D = 0,
    VK_IMAGE_VIEW_TYPE_2D = 1,
    VK_IMAGE_VIEW_TYPE_3D = 2,
    VK_IMAGE_VIEW_TYPE_CUBE = 3,
    VK_IMAGE_VIEW_TYPE_1D_ARRAY = 4,
    VK_IMAGE_VIEW_TYPE_2D_ARRAY = 5,
    VK_IMAGE_VIEW_TYPE_CUBE_ARRAY = 6
} VkImageViewType;
typedef enum VkSharingMode {
    VK_SHARING_MODE_EXCLUSIVE = 0,
    VK_SHARING_MODE_CONCURRENT = 1
} VkSharingMode;
typedef enum VkIndexType {
    VK_INDEX_TYPE_UINT16 = 0,
    VK_INDEX_TYPE_UINT32 = 1
} VkIndexType;
typedef enum VkLogicOp {
    VK_LOGIC_OP_CLEAR = 0,
    VK_LOGIC_OP_AND = 1,
    VK_LOGIC_OP_AND_REVERSE = 2,
    VK_LOGIC_OP_COPY = 3,
    VK_LOGIC_OP_AND_INVERTED = 4,
    VK_LOGIC_OP_NO_OP = 5,
    VK_LOGIC_OP_XOR = 6,
    VK_LOGIC_OP_OR = 7,
    VK_LOGIC_OP_NOR = 8,
    VK_LOGIC_OP_EQUIVALENT = 9,
    VK_LOGIC_OP_INVERT = 10,
    VK_LOGIC_OP_OR_REVERSE = 11,
    VK_LOGIC_OP_COPY_INVERTED = 12,
    VK_LOGIC_OP_OR_INVERTED = 13,
    VK_LOGIC_OP_NAND = 14,
    VK_LOGIC_OP_SET = 15
} VkLogicOp;
typedef enum VkMemoryHeapFlagBits {
    VK_MEMORY_HEAP_DEVICE_LOCAL_BIT = 1,
    VK_MEMORY_HEAP_MULTI_INSTANCE_BIT = 2
} VkMemoryHeapFlagBits;
typedef enum VkAccessFlagBits {
    VK_ACCESS_INDIRECT_COMMAND_READ_BIT = 1,
    VK_ACCESS_INDEX_READ_BIT = 2,
    VK_ACCESS_VERTEX_ATTRIBUTE_READ_BIT = 4,
    VK_ACCESS_UNIFORM_READ_BIT = 8,
    VK_ACCESS_INPUT_ATTACHMENT_READ_BIT = 16,
    VK_ACCESS_SHADER_READ_BIT = 32,
    VK_ACCESS_SHADER_WRITE_BIT = 64,
    VK_ACCESS_COLOR_ATTACHMENT_READ_BIT = 128,
    VK_ACCESS_COLOR_ATTACHMENT_WRITE_BIT = 256,
    VK_ACCESS_DEPTH_STENCIL_ATTACHMENT_READ_BIT = 512,
    VK_ACCESS_DEPTH_STENCIL_ATTACHMENT_WRITE_BIT = 1024,
    VK_ACCESS_TRANSFER_READ_BIT = 2048,
    VK_ACCESS_TRANSFER_WRITE_BIT = 4096,
    VK_ACCESS_HOST_READ_BIT = 8192,
    VK_ACCESS_HOST_WRITE_BIT = 16384,
    VK_ACCESS_MEMORY_READ_BIT = 32768,
    VK_ACCESS_MEMORY_WRITE_BIT = 65536
} VkAccessFlagBits;
typedef enum VkMemoryPropertyFlagBits {
    VK_MEMORY_PROPERTY_DEVICE_LOCAL_BIT = 1,
    VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT = 2,
    VK_MEMORY_PROPERTY_HOST_COHERENT_BIT = 4,
    VK_MEMORY_PROPERTY_HOST_CACHED_BIT = 8,
    VK_MEMORY_PROPERTY_LAZILY_ALLOCATED_BIT = 16,
    VK_MEMORY_PROPERTY_PROTECTED_BIT = 32
} VkMemoryPropertyFlagBits;
typedef enum VkPhysicalDeviceType {
    VK_PHYSICAL_DEVICE_TYPE_OTHER = 0,
    VK_PHYSICAL_DEVICE_TYPE_INTEGRATED_GPU = 1,
    VK_PHYSICAL_DEVICE_TYPE_DISCRETE_GPU = 2,
    VK_PHYSICAL_DEVICE_TYPE_VIRTUAL_GPU = 3,
    VK_PHYSICAL_DEVICE_TYPE_CPU = 4
} VkPhysicalDeviceType;
typedef enum VkPipelineBindPoint {
    VK_PIPELINE_BIND_POINT_GRAPHICS = 0,
    VK_PIPELINE_BIND_POINT_COMPUTE = 1
} VkPipelineBindPoint;
typedef enum VkPipelineCreateFlagBits {
    VK_PIPELINE_CREATE_DISABLE_OPTIMIZATION_BIT = 1,
    VK_PIPELINE_CREATE_ALLOW_DERIVATIVES_BIT = 2,
    VK_PIPELINE_CREATE_DERIVATIVE_BIT = 4,
    VK_PIPELINE_CREATE_VIEW_INDEX_FROM_DEVICE_INDEX_BIT = 8,
    VK_PIPELINE_CREATE_DISPATCH_BASE = 16
} VkPipelineCreateFlagBits;
typedef enum VkPrimitiveTopology {
    VK_PRIMITIVE_TOPOLOGY_POINT_LIST = 0,
    VK_PRIMITIVE_TOPOLOGY_LINE_LIST = 1,
    VK_PRIMITIVE_TOPOLOGY_LINE_STRIP = 2,
    VK_PRIMITIVE_TOPOLOGY_TRIANGLE_LIST = 3,
    VK_PRIMITIVE_TOPOLOGY_TRIANGLE_STRIP = 4,
    VK_PRIMITIVE_TOPOLOGY_TRIANGLE_FAN = 5,
    VK_PRIMITIVE_TOPOLOGY_LINE_LIST_WITH_ADJACENCY = 6,
    VK_PRIMITIVE_TOPOLOGY_LINE_STRIP_WITH_ADJACENCY = 7,
    VK_PRIMITIVE_TOPOLOGY_TRIANGLE_LIST_WITH_ADJACENCY = 8,
    VK_PRIMITIVE_TOPOLOGY_TRIANGLE_STRIP_WITH_ADJACENCY = 9,
    VK_PRIMITIVE_TOPOLOGY_PATCH_LIST = 10
} VkPrimitiveTopology;
typedef enum VkQueryControlFlagBits {
    VK_QUERY_CONTROL_PRECISE_BIT = 1
} VkQueryControlFlagBits;
typedef enum VkQueryPipelineStatisticFlagBits {
    VK_QUERY_PIPELINE_STATISTIC_INPUT_ASSEMBLY_VERTICES_BIT = 1,
    VK_QUERY_PIPELINE_STATISTIC_INPUT_ASSEMBLY_PRIMITIVES_BIT = 2,
    VK_QUERY_PIPELINE_STATISTIC_VERTEX_SHADER_INVOCATIONS_BIT = 4,
    VK_QUERY_PIPELINE_STATISTIC_GEOMETRY_SHADER_INVOCATIONS_BIT = 8,
    VK_QUERY_PIPELINE_STATISTIC_GEOMETRY_SHADER_PRIMITIVES_BIT = 16,
    VK_QUERY_PIPELINE_STATISTIC_CLIPPING_INVOCATIONS_BIT = 32,
    VK_QUERY_PIPELINE_STATISTIC_CLIPPING_PRIMITIVES_BIT = 64,
    VK_QUERY_PIPELINE_STATISTIC_FRAGMENT_SHADER_INVOCATIONS_BIT = 128,
    VK_QUERY_PIPELINE_STATISTIC_TESSELLATION_CONTROL_SHADER_PATCHES_BIT = 256,
    VK_QUERY_PIPELINE_STATISTIC_TESSELLATION_EVALUATION_SHADER_INVOCATIONS_BIT = 512,
    VK_QUERY_PIPELINE_STATISTIC_COMPUTE_SHADER_INVOCATIONS_BIT = 1024
} VkQueryPipelineStatisticFlagBits;
typedef enum VkQueryResultFlagBits {
    VK_QUERY_RESULT_64_BIT = 1,
    VK_QUERY_RESULT_WAIT_BIT = 2,
    VK_QUERY_RESULT_WITH_AVAILABILITY_BIT = 4,
    VK_QUERY_RESULT_PARTIAL_BIT = 8
} VkQueryResultFlagBits;
typedef enum VkQueryType {
    VK_QUERY_TYPE_OCCLUSION = 0,
    VK_QUERY_TYPE_PIPELINE_STATISTICS = 1,
    VK_QUERY_TYPE_TIMESTAMP = 2
} VkQueryType;
typedef enum VkQueueFlagBits {
    VK_QUEUE_GRAPHICS_BIT = 1,
    VK_QUEUE_COMPUTE_BIT = 2,
    VK_QUEUE_TRANSFER_BIT = 4,
    VK_QUEUE_SPARSE_BINDING_BIT = 8,
    VK_QUEUE_PROTECTED_BIT = 16
} VkQueueFlagBits;
typedef enum VkSubpassContents {
    VK_SUBPASS_CONTENTS_INLINE = 0,
    VK_SUBPASS_CONTENTS_SECONDARY_COMMAND_BUFFERS = 1
} VkSubpassContents;
typedef enum VkResult {
    VK_SUCCESS = 0,
    VK_NOT_READY = 1,
    VK_TIMEOUT = 2,
    VK_EVENT_SET = 3,
    VK_EVENT_RESET = 4,
    VK_INCOMPLETE = 5,
    VK_ERROR_OUT_OF_HOST_MEMORY = -1,
    VK_ERROR_OUT_OF_DEVICE_MEMORY = -2,
    VK_ERROR_INITIALIZATION_FAILED = -3,
    VK_ERROR_DEVICE_LOST = -4,
    VK_ERROR_MEMORY_MAP_FAILED = -5,
    VK_ERROR_LAYER_NOT_PRESENT = -6,
    VK_ERROR_EXTENSION_NOT_PRESENT = -7,
    VK_ERROR_FEATURE_NOT_PRESENT = -8,
    VK_ERROR_INCOMPATIBLE_DRIVER = -9,
    VK_ERROR_TOO_MANY_OBJECTS = -10,
    VK_ERROR_FORMAT_NOT_SUPPORTED = -11,
    VK_ERROR_FRAGMENTED_POOL = -12,
    VK_ERROR_OUT_OF_POOL_MEMORY = -1000069000,
    VK_ERROR_INVALID_EXTERNAL_HANDLE = -1000072003,
    VK_ERROR_SURFACE_LOST_KHR = -1000000000,
    VK_ERROR_NATIVE_WINDOW_IN_USE_KHR = -1000000001,
    VK_SUBOPTIMAL_KHR = 1000001003,
    VK_ERROR_OUT_OF_DATE_KHR = -1000001004,
    VK_ERROR_VALIDATION_FAILED_EXT = -1000011001
} VkResult;
typedef enum VkShaderStageFlagBits {
    VK_SHADER_STAGE_VERTEX_BIT = 1,
    VK_SHADER_STAGE_TESSELLATION_CONTROL_BIT = 2,
    VK_SHADER_STAGE_TESSELLATION_EVALUATION_BIT = 4,
    VK_SHADER_STAGE_GEOMETRY_BIT = 8,
    VK_SHADER_STAGE_FRAGMENT_BIT = 16,
    VK_SHADER_STAGE_COMPUTE_BIT = 32,
    VK_SHADER_STAGE_ALL_GRAPHICS = 0x0000001F,
    VK_SHADER_STAGE_ALL = 0x7FFFFFFF
} VkShaderStageFlagBits;
typedef enum VkSparseMemoryBindFlagBits {
    VK_SPARSE_MEMORY_BIND_METADATA_BIT = 1
} VkSparseMemoryBindFlagBits;
typedef enum VkStencilFaceFlagBits {
    VK_STENCIL_FACE_FRONT_BIT = 1,
    VK_STENCIL_FACE_BACK_BIT = 2,
    VK_STENCIL_FRONT_AND_BACK = 0x00000003
} VkStencilFaceFlagBits;
typedef enum VkStencilOp {
    VK_STENCIL_OP_KEEP = 0,
    VK_STENCIL_OP_ZERO = 1,
    VK_STENCIL_OP_REPLACE = 2,
    VK_STENCIL_OP_INCREMENT_AND_CLAMP = 3,
    VK_STENCIL_OP_DECREMENT_AND_CLAMP = 4,
    VK_STENCIL_OP_INVERT = 5,
    VK_STENCIL_OP_INCREMENT_AND_WRAP = 6,
    VK_STENCIL_OP_DECREMENT_AND_WRAP = 7
} VkStencilOp;
typedef enum VkStructureType {
    VK_STRUCTURE_TYPE_APPLICATION_INFO = 0,
    VK_STRUCTURE_TYPE_INSTANCE_CREATE_INFO = 1,
    VK_STRUCTURE_TYPE_DEVICE_QUEUE_CREATE_INFO = 2,
    VK_STRUCTURE_TYPE_DEVICE_CREATE_INFO = 3,
    VK_STRUCTURE_TYPE_SUBMIT_INFO = 4,
    VK_STRUCTURE_TYPE_MEMORY_ALLOCATE_INFO = 5,
    VK_STRUCTURE_TYPE_MAPPED_MEMORY_RANGE = 6,
    VK_STRUCTURE_TYPE_BIND_SPARSE_INFO = 7,
    VK_STRUCTURE_TYPE_FENCE_CREATE_INFO = 8,
    VK_STRUCTURE_TYPE_SEMAPHORE_CREATE_INFO = 9,
    VK_STRUCTURE_TYPE_EVENT_CREATE_INFO = 10,
    VK_STRUCTURE_TYPE_QUERY_POOL_CREATE_INFO = 11,
    VK_STRUCTURE_TYPE_BUFFER_CREATE_INFO = 12,
    VK_STRUCTURE_TYPE_BUFFER_VIEW_CREATE_INFO = 13,
    VK_STRUCTURE_TYPE_IMAGE_CREATE_INFO = 14,
    VK_STRUCTURE_TYPE_IMAGE_VIEW_CREATE_INFO = 15,
    VK_STRUCTURE_TYPE_SHADER_MODULE_CREATE_INFO = 16,
    VK_STRUCTURE_TYPE_PIPELINE_CACHE_CREATE_INFO = 17,
    VK_STRUCTURE_TYPE_PIPELINE_SHADER_STAGE_CREATE_INFO = 18,
    VK_STRUCTURE_TYPE_PIPELINE_VERTEX_INPUT_STATE_CREATE_INFO = 19,
    VK_STRUCTURE_TYPE_PIPELINE_INPUT_ASSEMBLY_STATE_CREATE_INFO = 20,
    VK_STRUCTURE_TYPE_PIPELINE_TESSELLATION_STATE_CREATE_INFO = 21,
    VK_STRUCTURE_TYPE_PIPELINE_VIEWPORT_STATE_CREATE_INFO = 22,
    VK_STRUCTURE_TYPE_PIPELINE_RASTERIZATION_STATE_CREATE_INFO = 23,
    VK_STRUCTURE_TYPE_PIPELINE_MULTISAMPLE_STATE_CREATE_INFO = 24,
    VK_STRUCTURE_TYPE_PIPELINE_DEPTH_STENCIL_STATE_CREATE_INFO = 25,
    VK_STRUCTURE_TYPE_PIPELINE_COLOR_BLEND_STATE_CREATE_INFO = 26,
    VK_STRUCTURE_TYPE_PIPELINE_DYNAMIC_STATE_CREATE_INFO = 27,
    VK_STRUCTURE_TYPE_GRAPHICS_PIPELINE_CREATE_INFO = 28,
    VK_STRUCTURE_TYPE_COMPUTE_PIPELINE_CREATE_INFO = 29,
    VK_STRUCTURE_TYPE_PIPELINE_LAYOUT_CREATE_INFO = 30,
    VK_STRUCTURE_TYPE_SAMPLER_CREATE_INFO = 31,
    VK_STRUCTURE_TYPE_DESCRIPTOR_SET_LAYOUT_CREATE_INFO = 32,
    VK_STRUCTURE_TYPE_DESCRIPTOR_POOL_CREATE_INFO = 33,
    VK_STRUCTURE_TYPE_DESCRIPTOR_SET_ALLOCATE_INFO = 34,
    VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET = 35,
    VK_STRUCTURE_TYPE_COPY_DESCRIPTOR_SET = 36,
    VK_STRUCTURE_TYPE_FRAMEBUFFER_CREATE_INFO = 37,
    VK_STRUCTURE_TYPE_RENDER_PASS_CREATE_INFO = 38,
    VK_STRUCTURE_TYPE_COMMAND_POOL_CREATE_INFO = 39,
    VK_STRUCTURE_TYPE_COMMAND_BUFFER_ALLOCATE_INFO = 40,
    VK_STRUCTURE_TYPE_COMMAND_BUFFER_INHERITANCE_INFO = 41,
    VK_STRUCTURE_TYPE_COMMAND_BUFFER_BEGIN_INFO = 42,
    VK_STRUCTURE_TYPE_RENDER_PASS_BEGIN_INFO = 43,
    VK_STRUCTURE_TYPE_BUFFER_MEMORY_BARRIER = 44,
    VK_STRUCTURE_TYPE_IMAGE_MEMORY_BARRIER = 45,
    VK_STRUCTURE_TYPE_MEMORY_BARRIER = 46,
    VK_STRUCTURE_TYPE_LOADER_INSTANCE_CREATE_INFO = 47,
    VK_STRUCTURE_TYPE_LOADER_DEVICE_CREATE_INFO = 48,
    VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SUBGROUP_PROPERTIES = 1000094000,
    VK_STRUCTURE_TYPE_BIND_BUFFER_MEMORY_INFO = 1000157000,
    VK_STRUCTURE_TYPE_BIND_IMAGE_MEMORY_INFO = 1000157001,
    VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_16BIT_STORAGE_FEATURES = 1000083000,
    VK_STRUCTURE_TYPE_MEMORY_DEDICATED_REQUIREMENTS = 1000127000,
    VK_STRUCTURE_TYPE_MEMORY_DEDICATED_ALLOCATE_INFO = 1000127001,
    VK_STRUCTURE_TYPE_MEMORY_ALLOCATE_FLAGS_INFO = 1000060000,
    VK_STRUCTURE_TYPE_DEVICE_GROUP_RENDER_PASS_BEGIN_INFO = 1000060003,
    VK_STRUCTURE_TYPE_DEVICE_GROUP_COMMAND_BUFFER_BEGIN_INFO = 1000060004,
    VK_STRUCTURE_TYPE_DEVICE_GROUP_SUBMIT_INFO = 1000060005,
    VK_STRUCTURE_TYPE_DEVICE_GROUP_BIND_SPARSE_INFO = 1000060006,
    VK_STRUCTURE_TYPE_BIND_BUFFER_MEMORY_DEVICE_GROUP_INFO = 1000060013,
    VK_STRUCTURE_TYPE_BIND_IMAGE_MEMORY_DEVICE_GROUP_INFO = 1000060014,
    VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_GROUP_PROPERTIES = 1000070000,
    VK_STRUCTURE_TYPE_DEVICE_GROUP_DEVICE_CREATE_INFO = 1000070001,
    VK_STRUCTURE_TYPE_BUFFER_MEMORY_REQUIREMENTS_INFO_2 = 1000146000,
    VK_STRUCTURE_TYPE_IMAGE_MEMORY_REQUIREMENTS_INFO_2 = 1000146001,
    VK_STRUCTURE_TYPE_IMAGE_SPARSE_MEMORY_REQUIREMENTS_INFO_2 = 1000146002,
    VK_STRUCTURE_TYPE_MEMORY_REQUIREMENTS_2 = 1000146003,
    VK_STRUCTURE_TYPE_SPARSE_IMAGE_MEMORY_REQUIREMENTS_2 = 1000146004,
    VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_FEATURES_2 = 1000059000,
    VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_PROPERTIES_2 = 1000059001,
    VK_STRUCTURE_TYPE_FORMAT_PROPERTIES_2 = 1000059002,
    VK_STRUCTURE_TYPE_IMAGE_FORMAT_PROPERTIES_2 = 1000059003,
    VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_IMAGE_FORMAT_INFO_2 = 1000059004,
    VK_STRUCTURE_TYPE_QUEUE_FAMILY_PROPERTIES_2 = 1000059005,
    VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_MEMORY_PROPERTIES_2 = 1000059006,
    VK_STRUCTURE_TYPE_SPARSE_IMAGE_FORMAT_PROPERTIES_2 = 1000059007,
    VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SPARSE_IMAGE_FORMAT_INFO_2 = 1000059008,
    VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_POINT_CLIPPING_PROPERTIES = 1000117000,
    VK_STRUCTURE_TYPE_RENDER_PASS_INPUT_ATTACHMENT_ASPECT_CREATE_INFO = 1000117001,
    VK_STRUCTURE_TYPE_IMAGE_VIEW_USAGE_CREATE_INFO = 1000117002,
    VK_STRUCTURE_TYPE_PIPELINE_TESSELLATION_DOMAIN_ORIGIN_STATE_CREATE_INFO = 1000117003,
    VK_STRUCTURE_TYPE_RENDER_PASS_MULTIVIEW_CREATE_INFO = 1000053000,
    VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_MULTIVIEW_FEATURES = 1000053001,
    VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_MULTIVIEW_PROPERTIES = 1000053002,
    VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_VARIABLE_POINTERS_FEATURES = 1000120000,
    VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_VARIABLE_POINTER_FEATURES = VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_VARIABLE_POINTERS_FEATURES,
    VK_STRUCTURE_TYPE_PROTECTED_SUBMIT_INFO = 1000145000,
    VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_PROTECTED_MEMORY_FEATURES = 1000145001,
    VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_PROTECTED_MEMORY_PROPERTIES = 1000145002,
    VK_STRUCTURE_TYPE_DEVICE_QUEUE_INFO_2 = 1000145003,
    VK_STRUCTURE_TYPE_SAMPLER_YCBCR_CONVERSION_CREATE_INFO = 1000156000,
    VK_STRUCTURE_TYPE_SAMPLER_YCBCR_CONVERSION_INFO = 1000156001,
    VK_STRUCTURE_TYPE_BIND_IMAGE_PLANE_MEMORY_INFO = 1000156002,
    VK_STRUCTURE_TYPE_IMAGE_PLANE_MEMORY_REQUIREMENTS_INFO = 1000156003,
    VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SAMPLER_YCBCR_CONVERSION_FEATURES = 1000156004,
    VK_STRUCTURE_TYPE_SAMPLER_YCBCR_CONVERSION_IMAGE_FORMAT_PROPERTIES = 1000156005,
    VK_STRUCTURE_TYPE_DESCRIPTOR_UPDATE_TEMPLATE_CREATE_INFO = 1000085000,
    VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_EXTERNAL_IMAGE_FORMAT_INFO = 1000071000,
    VK_STRUCTURE_TYPE_EXTERNAL_IMAGE_FORMAT_PROPERTIES = 1000071001,
    VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_EXTERNAL_BUFFER_INFO = 1000071002,
    VK_STRUCTURE_TYPE_EXTERNAL_BUFFER_PROPERTIES = 1000071003,
    VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_ID_PROPERTIES = 1000071004,
    VK_STRUCTURE_TYPE_EXTERNAL_MEMORY_BUFFER_CREATE_INFO = 1000072000,
    VK_STRUCTURE_TYPE_EXTERNAL_MEMORY_IMAGE_CREATE_INFO = 1000072001,
    VK_STRUCTURE_TYPE_EXPORT_MEMORY_ALLOCATE_INFO = 1000072002,
    VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_EXTERNAL_FENCE_INFO = 1000112000,
    VK_STRUCTURE_TYPE_EXTERNAL_FENCE_PROPERTIES = 1000112001,
    VK_STRUCTURE_TYPE_EXPORT_FENCE_CREATE_INFO = 1000113000,
    VK_STRUCTURE_TYPE_EXPORT_SEMAPHORE_CREATE_INFO = 1000077000,
    VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_EXTERNAL_SEMAPHORE_INFO = 1000076000,
    VK_STRUCTURE_TYPE_EXTERNAL_SEMAPHORE_PROPERTIES = 1000076001,
    VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_MAINTENANCE_3_PROPERTIES = 1000168000,
    VK_STRUCTURE_TYPE_DESCRIPTOR_SET_LAYOUT_SUPPORT = 1000168001,
    VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SHADER_DRAW_PARAMETERS_FEATURES = 1000063000,
    VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SHADER_DRAW_PARAMETER_FEATURES = VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SHADER_DRAW_PARAMETERS_FEATURES,
    VK_STRUCTURE_TYPE_SWAPCHAIN_CREATE_INFO_KHR = 1000001000,
    VK_STRUCTURE_TYPE_PRESENT_INFO_KHR = 1000001001,
    VK_STRUCTURE_TYPE_DEVICE_GROUP_PRESENT_CAPABILITIES_KHR = 1000060007,
    VK_STRUCTURE_TYPE_IMAGE_SWAPCHAIN_CREATE_INFO_KHR = 1000060008,
    VK_STRUCTURE_TYPE_BIND_IMAGE_MEMORY_SWAPCHAIN_INFO_KHR = 1000060009,
    VK_STRUCTURE_TYPE_ACQUIRE_NEXT_IMAGE_INFO_KHR = 1000060010,
    VK_STRUCTURE_TYPE_DEVICE_GROUP_PRESENT_INFO_KHR = 1000060011,
    VK_STRUCTURE_TYPE_DEVICE_GROUP_SWAPCHAIN_CREATE_INFO_KHR = 1000060012,
    VK_STRUCTURE_TYPE_DEBUG_REPORT_CALLBACK_CREATE_INFO_EXT = 1000011000,
    VK_STRUCTURE_TYPE_DEBUG_REPORT_CREATE_INFO_EXT = VK_STRUCTURE_TYPE_DEBUG_REPORT_CALLBACK_CREATE_INFO_EXT
} VkStructureType;
typedef enum VkSystemAllocationScope {
    VK_SYSTEM_ALLOCATION_SCOPE_COMMAND = 0,
    VK_SYSTEM_ALLOCATION_SCOPE_OBJECT = 1,
    VK_SYSTEM_ALLOCATION_SCOPE_CACHE = 2,
    VK_SYSTEM_ALLOCATION_SCOPE_DEVICE = 3,
    VK_SYSTEM_ALLOCATION_SCOPE_INSTANCE = 4
} VkSystemAllocationScope;
typedef enum VkInternalAllocationType {
    VK_INTERNAL_ALLOCATION_TYPE_EXECUTABLE = 0
} VkInternalAllocationType;
typedef enum VkSamplerAddressMode {
    VK_SAMPLER_ADDRESS_MODE_REPEAT = 0,
    VK_SAMPLER_ADDRESS_MODE_MIRRORED_REPEAT = 1,
    VK_SAMPLER_ADDRESS_MODE_CLAMP_TO_EDGE = 2,
    VK_SAMPLER_ADDRESS_MODE_CLAMP_TO_BORDER = 3
} VkSamplerAddressMode;
typedef enum VkFilter {
    VK_FILTER_NEAREST = 0,
    VK_FILTER_LINEAR = 1
} VkFilter;
typedef enum VkSamplerMipmapMode {
    VK_SAMPLER_MIPMAP_MODE_NEAREST = 0,
    VK_SAMPLER_MIPMAP_MODE_LINEAR = 1
} VkSamplerMipmapMode;
typedef enum VkVertexInputRate {
    VK_VERTEX_INPUT_RATE_VERTEX = 0,
    VK_VERTEX_INPUT_RATE_INSTANCE = 1
} VkVertexInputRate;
typedef enum VkPipelineStageFlagBits {
    VK_PIPELINE_STAGE_TOP_OF_PIPE_BIT = 1,
    VK_PIPELINE_STAGE_DRAW_INDIRECT_BIT = 2,
    VK_PIPELINE_STAGE_VERTEX_INPUT_BIT = 4,
    VK_PIPELINE_STAGE_VERTEX_SHADER_BIT = 8,
    VK_PIPELINE_STAGE_TESSELLATION_CONTROL_SHADER_BIT = 16,
    VK_PIPELINE_STAGE_TESSELLATION_EVALUATION_SHADER_BIT = 32,
    VK_PIPELINE_STAGE_GEOMETRY_SHADER_BIT = 64,
    VK_PIPELINE_STAGE_FRAGMENT_SHADER_BIT = 128,
    VK_PIPELINE_STAGE_EARLY_FRAGMENT_TESTS_BIT = 256,
    VK_PIPELINE_STAGE_LATE_FRAGMENT_TESTS_BIT = 512,
    VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT = 1024,
    VK_PIPELINE_STAGE_COMPUTE_SHADER_BIT = 2048,
    VK_PIPELINE_STAGE_TRANSFER_BIT = 4096,
    VK_PIPELINE_STAGE_BOTTOM_OF_PIPE_BIT = 8192,
    VK_PIPELINE_STAGE_HOST_BIT = 16384,
    VK_PIPELINE_STAGE_ALL_GRAPHICS_BIT = 32768,
    VK_PIPELINE_STAGE_ALL_COMMANDS_BIT = 65536
} VkPipelineStageFlagBits;
typedef enum VkSparseImageFormatFlagBits {
    VK_SPARSE_IMAGE_FORMAT_SINGLE_MIPTAIL_BIT = 1,
    VK_SPARSE_IMAGE_FORMAT_ALIGNED_MIP_SIZE_BIT = 2,
    VK_SPARSE_IMAGE_FORMAT_NONSTANDARD_BLOCK_SIZE_BIT = 4
} VkSparseImageFormatFlagBits;
typedef enum VkSampleCountFlagBits {
    VK_SAMPLE_COUNT_1_BIT = 1,
    VK_SAMPLE_COUNT_2_BIT = 2,
    VK_SAMPLE_COUNT_4_BIT = 4,
    VK_SAMPLE_COUNT_8_BIT = 8,
    VK_SAMPLE_COUNT_16_BIT = 16,
    VK_SAMPLE_COUNT_32_BIT = 32,
    VK_SAMPLE_COUNT_64_BIT = 64
} VkSampleCountFlagBits;
typedef enum VkAttachmentDescriptionFlagBits {
    VK_ATTACHMENT_DESCRIPTION_MAY_ALIAS_BIT = 1
} VkAttachmentDescriptionFlagBits;
typedef enum VkDescriptorPoolCreateFlagBits {
    VK_DESCRIPTOR_POOL_CREATE_FREE_DESCRIPTOR_SET_BIT = 1
} VkDescriptorPoolCreateFlagBits;
typedef enum VkDependencyFlagBits {
    VK_DEPENDENCY_BY_REGION_BIT = 1,
    VK_DEPENDENCY_DEVICE_GROUP_BIT = 4,
    VK_DEPENDENCY_VIEW_LOCAL_BIT = 2
} VkDependencyFlagBits;
typedef enum VkObjectType {
    VK_OBJECT_TYPE_UNKNOWN = 0,
    VK_OBJECT_TYPE_INSTANCE = 1,
    VK_OBJECT_TYPE_PHYSICAL_DEVICE = 2,
    VK_OBJECT_TYPE_DEVICE = 3,
    VK_OBJECT_TYPE_QUEUE = 4,
    VK_OBJECT_TYPE_SEMAPHORE = 5,
    VK_OBJECT_TYPE_COMMAND_BUFFER = 6,
    VK_OBJECT_TYPE_FENCE = 7,
    VK_OBJECT_TYPE_DEVICE_MEMORY = 8,
    VK_OBJECT_TYPE_BUFFER = 9,
    VK_OBJECT_TYPE_IMAGE = 10,
    VK_OBJECT_TYPE_EVENT = 11,
    VK_OBJECT_TYPE_QUERY_POOL = 12,
    VK_OBJECT_TYPE_BUFFER_VIEW = 13,
    VK_OBJECT_TYPE_IMAGE_VIEW = 14,
    VK_OBJECT_TYPE_SHADER_MODULE = 15,
    VK_OBJECT_TYPE_PIPELINE_CACHE = 16,
    VK_OBJECT_TYPE_PIPELINE_LAYOUT = 17,
    VK_OBJECT_TYPE_RENDER_PASS = 18,
    VK_OBJECT_TYPE_PIPELINE = 19,
    VK_OBJECT_TYPE_DESCRIPTOR_SET_LAYOUT = 20,
    VK_OBJECT_TYPE_SAMPLER = 21,
    VK_OBJECT_TYPE_DESCRIPTOR_POOL = 22,
    VK_OBJECT_TYPE_DESCRIPTOR_SET = 23,
    VK_OBJECT_TYPE_FRAMEBUFFER = 24,
    VK_OBJECT_TYPE_COMMAND_POOL = 25,
    VK_OBJECT_TYPE_SAMPLER_YCBCR_CONVERSION = 1000156000,
    VK_OBJECT_TYPE_DESCRIPTOR_UPDATE_TEMPLATE = 1000085000,
    VK_OBJECT_TYPE_SURFACE_KHR = 1000000000,
    VK_OBJECT_TYPE_SWAPCHAIN_KHR = 1000001000,
    VK_OBJECT_TYPE_DEBUG_REPORT_CALLBACK_EXT = 1000011000
} VkObjectType;
typedef enum VkDescriptorUpdateTemplateType {
    VK_DESCRIPTOR_UPDATE_TEMPLATE_TYPE_DESCRIPTOR_SET = 0
} VkDescriptorUpdateTemplateType;

typedef enum VkPointClippingBehavior {
    VK_POINT_CLIPPING_BEHAVIOR_ALL_CLIP_PLANES = 0,
    VK_POINT_CLIPPING_BEHAVIOR_USER_CLIP_PLANES_ONLY = 1
} VkPointClippingBehavior;
typedef enum VkColorSpaceKHR {
    VK_COLOR_SPACE_SRGB_NONLINEAR_KHR = 0,
    VK_COLORSPACE_SRGB_NONLINEAR_KHR = VK_COLOR_SPACE_SRGB_NONLINEAR_KHR
} VkColorSpaceKHR;
typedef enum VkCompositeAlphaFlagBitsKHR {
    VK_COMPOSITE_ALPHA_OPAQUE_BIT_KHR = 1,
    VK_COMPOSITE_ALPHA_PRE_MULTIPLIED_BIT_KHR = 2,
    VK_COMPOSITE_ALPHA_POST_MULTIPLIED_BIT_KHR = 4,
    VK_COMPOSITE_ALPHA_INHERIT_BIT_KHR = 8
} VkCompositeAlphaFlagBitsKHR;
typedef enum VkPresentModeKHR {
    VK_PRESENT_MODE_IMMEDIATE_KHR = 0,
    VK_PRESENT_MODE_MAILBOX_KHR = 1,
    VK_PRESENT_MODE_FIFO_KHR = 2,
    VK_PRESENT_MODE_FIFO_RELAXED_KHR = 3
} VkPresentModeKHR;
typedef enum VkSurfaceTransformFlagBitsKHR {
    VK_SURFACE_TRANSFORM_IDENTITY_BIT_KHR = 1,
    VK_SURFACE_TRANSFORM_ROTATE_90_BIT_KHR = 2,
    VK_SURFACE_TRANSFORM_ROTATE_180_BIT_KHR = 4,
    VK_SURFACE_TRANSFORM_ROTATE_270_BIT_KHR = 8,
    VK_SURFACE_TRANSFORM_HORIZONTAL_MIRROR_BIT_KHR = 16,
    VK_SURFACE_TRANSFORM_HORIZONTAL_MIRROR_ROTATE_90_BIT_KHR = 32,
    VK_SURFACE_TRANSFORM_HORIZONTAL_MIRROR_ROTATE_180_BIT_KHR = 64,
    VK_SURFACE_TRANSFORM_HORIZONTAL_MIRROR_ROTATE_270_BIT_KHR = 128,
    VK_SURFACE_TRANSFORM_INHERIT_BIT_KHR = 256
} VkSurfaceTransformFlagBitsKHR;
typedef enum VkDebugReportFlagBitsEXT {
    VK_DEBUG_REPORT_INFORMATION_BIT_EXT = 1,
    VK_DEBUG_REPORT_WARNING_BIT_EXT = 2,
    VK_DEBUG_REPORT_PERFORMANCE_WARNING_BIT_EXT = 4,
    VK_DEBUG_REPORT_ERROR_BIT_EXT = 8,
    VK_DEBUG_REPORT_DEBUG_BIT_EXT = 16
} VkDebugReportFlagBitsEXT;
typedef enum VkDebugReportObjectTypeEXT {
    VK_DEBUG_REPORT_OBJECT_TYPE_UNKNOWN_EXT = 0,
    VK_DEBUG_REPORT_OBJECT_TYPE_INSTANCE_EXT = 1,
    VK_DEBUG_REPORT_OBJECT_TYPE_PHYSICAL_DEVICE_EXT = 2,
    VK_DEBUG_REPORT_OBJECT_TYPE_DEVICE_EXT = 3,
    VK_DEBUG_REPORT_OBJECT_TYPE_QUEUE_EXT = 4,
    VK_DEBUG_REPORT_OBJECT_TYPE_SEMAPHORE_EXT = 5,
    VK_DEBUG_REPORT_OBJECT_TYPE_COMMAND_BUFFER_EXT = 6,
    VK_DEBUG_REPORT_OBJECT_TYPE_FENCE_EXT = 7,
    VK_DEBUG_REPORT_OBJECT_TYPE_DEVICE_MEMORY_EXT = 8,
    VK_DEBUG_REPORT_OBJECT_TYPE_BUFFER_EXT = 9,
    VK_DEBUG_REPORT_OBJECT_TYPE_IMAGE_EXT = 10,
    VK_DEBUG_REPORT_OBJECT_TYPE_EVENT_EXT = 11,
    VK_DEBUG_REPORT_OBJECT_TYPE_QUERY_POOL_EXT = 12,
    VK_DEBUG_REPORT_OBJECT_TYPE_BUFFER_VIEW_EXT = 13,
    VK_DEBUG_REPORT_OBJECT_TYPE_IMAGE_VIEW_EXT = 14,
    VK_DEBUG_REPORT_OBJECT_TYPE_SHADER_MODULE_EXT = 15,
    VK_DEBUG_REPORT_OBJECT_TYPE_PIPELINE_CACHE_EXT = 16,
    VK_DEBUG_REPORT_OBJECT_TYPE_PIPELINE_LAYOUT_EXT = 17,
    VK_DEBUG_REPORT_OBJECT_TYPE_RENDER_PASS_EXT = 18,
    VK_DEBUG_REPORT_OBJECT_TYPE_PIPELINE_EXT = 19,
    VK_DEBUG_REPORT_OBJECT_TYPE_DESCRIPTOR_SET_LAYOUT_EXT = 20,
    VK_DEBUG_REPORT_OBJECT_TYPE_SAMPLER_EXT = 21,
    VK_DEBUG_REPORT_OBJECT_TYPE_DESCRIPTOR_POOL_EXT = 22,
    VK_DEBUG_REPORT_OBJECT_TYPE_DESCRIPTOR_SET_EXT = 23,
    VK_DEBUG_REPORT_OBJECT_TYPE_FRAMEBUFFER_EXT = 24,
    VK_DEBUG_REPORT_OBJECT_TYPE_COMMAND_POOL_EXT = 25,
    VK_DEBUG_REPORT_OBJECT_TYPE_SURFACE_KHR_EXT = 26,
    VK_DEBUG_REPORT_OBJECT_TYPE_SWAPCHAIN_KHR_EXT = 27,
    VK_DEBUG_REPORT_OBJECT_TYPE_DEBUG_REPORT_CALLBACK_EXT_EXT = 28,
    VK_DEBUG_REPORT_OBJECT_TYPE_DEBUG_REPORT_EXT = VK_DEBUG_REPORT_OBJECT_TYPE_DEBUG_REPORT_CALLBACK_EXT_EXT,
    VK_DEBUG_REPORT_OBJECT_TYPE_DISPLAY_KHR_EXT = 29,
    VK_DEBUG_REPORT_OBJECT_TYPE_DISPLAY_MODE_KHR_EXT = 30,
    VK_DEBUG_REPORT_OBJECT_TYPE_OBJECT_TABLE_NVX_EXT = 31,
    VK_DEBUG_REPORT_OBJECT_TYPE_INDIRECT_COMMANDS_LAYOUT_NVX_EXT = 32,
    VK_DEBUG_REPORT_OBJECT_TYPE_VALIDATION_CACHE_EXT_EXT = 33,
    VK_DEBUG_REPORT_OBJECT_TYPE_VALIDATION_CACHE_EXT = VK_DEBUG_REPORT_OBJECT_TYPE_VALIDATION_CACHE_EXT_EXT,
    VK_DEBUG_REPORT_OBJECT_TYPE_SAMPLER_YCBCR_CONVERSION_EXT = 1000156000,
    VK_DEBUG_REPORT_OBJECT_TYPE_DESCRIPTOR_UPDATE_TEMPLATE_EXT = 1000085000
} VkDebugReportObjectTypeEXT;
typedef enum VkExternalMemoryHandleTypeFlagBits {
    VK_EXTERNAL_MEMORY_HANDLE_TYPE_OPAQUE_FD_BIT = 1,
    VK_EXTERNAL_MEMORY_HANDLE_TYPE_OPAQUE_WIN32_BIT = 2,
    VK_EXTERNAL_MEMORY_HANDLE_TYPE_OPAQUE_WIN32_KMT_BIT = 4,
    VK_EXTERNAL_MEMORY_HANDLE_TYPE_D3D11_TEXTURE_BIT = 8,
    VK_EXTERNAL_MEMORY_HANDLE_TYPE_D3D11_TEXTURE_KMT_BIT = 16,
    VK_EXTERNAL_MEMORY_HANDLE_TYPE_D3D12_HEAP_BIT = 32,
    VK_EXTERNAL_MEMORY_HANDLE_TYPE_D3D12_RESOURCE_BIT = 64
} VkExternalMemoryHandleTypeFlagBits;
typedef enum VkExternalMemoryFeatureFlagBits {
    VK_EXTERNAL_MEMORY_FEATURE_DEDICATED_ONLY_BIT = 1,
    VK_EXTERNAL_MEMORY_FEATURE_EXPORTABLE_BIT = 2,
    VK_EXTERNAL_MEMORY_FEATURE_IMPORTABLE_BIT = 4
} VkExternalMemoryFeatureFlagBits;
typedef enum VkExternalSemaphoreHandleTypeFlagBits {
    VK_EXTERNAL_SEMAPHORE_HANDLE_TYPE_OPAQUE_FD_BIT = 1,
    VK_EXTERNAL_SEMAPHORE_HANDLE_TYPE_OPAQUE_WIN32_BIT = 2,
    VK_EXTERNAL_SEMAPHORE_HANDLE_TYPE_OPAQUE_WIN32_KMT_BIT = 4,
    VK_EXTERNAL_SEMAPHORE_HANDLE_TYPE_D3D12_FENCE_BIT = 8,
    VK_EXTERNAL_SEMAPHORE_HANDLE_TYPE_SYNC_FD_BIT = 16
} VkExternalSemaphoreHandleTypeFlagBits;
typedef enum VkExternalSemaphoreFeatureFlagBits {
    VK_EXTERNAL_SEMAPHORE_FEATURE_EXPORTABLE_BIT = 1,
    VK_EXTERNAL_SEMAPHORE_FEATURE_IMPORTABLE_BIT = 2
} VkExternalSemaphoreFeatureFlagBits;
typedef enum VkSemaphoreImportFlagBits {
    VK_SEMAPHORE_IMPORT_TEMPORARY_BIT = 1
} VkSemaphoreImportFlagBits;
typedef enum VkExternalFenceHandleTypeFlagBits {
    VK_EXTERNAL_FENCE_HANDLE_TYPE_OPAQUE_FD_BIT = 1,
    VK_EXTERNAL_FENCE_HANDLE_TYPE_OPAQUE_WIN32_BIT = 2,
    VK_EXTERNAL_FENCE_HANDLE_TYPE_OPAQUE_WIN32_KMT_BIT = 4,
    VK_EXTERNAL_FENCE_HANDLE_TYPE_SYNC_FD_BIT = 8
} VkExternalFenceHandleTypeFlagBits;
typedef enum VkExternalFenceFeatureFlagBits {
    VK_EXTERNAL_FENCE_FEATURE_EXPORTABLE_BIT = 1,
    VK_EXTERNAL_FENCE_FEATURE_IMPORTABLE_BIT = 2
} VkExternalFenceFeatureFlagBits;
typedef enum VkFenceImportFlagBits {
    VK_FENCE_IMPORT_TEMPORARY_BIT = 1
} VkFenceImportFlagBits;
typedef enum VkPeerMemoryFeatureFlagBits {
    VK_PEER_MEMORY_FEATURE_COPY_SRC_BIT = 1,
    VK_PEER_MEMORY_FEATURE_COPY_DST_BIT = 2,
    VK_PEER_MEMORY_FEATURE_GENERIC_SRC_BIT = 4,
    VK_PEER_MEMORY_FEATURE_GENERIC_DST_BIT = 8
} VkPeerMemoryFeatureFlagBits;
typedef enum VkMemoryAllocateFlagBits {
    VK_MEMORY_ALLOCATE_DEVICE_MASK_BIT = 1
} VkMemoryAllocateFlagBits;
typedef enum VkDeviceGroupPresentModeFlagBitsKHR {
    VK_DEVICE_GROUP_PRESENT_MODE_LOCAL_BIT_KHR = 1,
    VK_DEVICE_GROUP_PRESENT_MODE_REMOTE_BIT_KHR = 2,
    VK_DEVICE_GROUP_PRESENT_MODE_SUM_BIT_KHR = 4,
    VK_DEVICE_GROUP_PRESENT_MODE_LOCAL_MULTI_DEVICE_BIT_KHR = 8
} VkDeviceGroupPresentModeFlagBitsKHR;
typedef enum VkSwapchainCreateFlagBitsKHR {
    VK_SWAPCHAIN_CREATE_SPLIT_INSTANCE_BIND_REGIONS_BIT_KHR = 1,
    VK_SWAPCHAIN_CREATE_PROTECTED_BIT_KHR = 2
} VkSwapchainCreateFlagBitsKHR;
typedef enum VkSubgroupFeatureFlagBits {
    VK_SUBGROUP_FEATURE_BASIC_BIT = 1,
    VK_SUBGROUP_FEATURE_VOTE_BIT = 2,
    VK_SUBGROUP_FEATURE_ARITHMETIC_BIT = 4,
    VK_SUBGROUP_FEATURE_BALLOT_BIT = 8,
    VK_SUBGROUP_FEATURE_SHUFFLE_BIT = 16,
    VK_SUBGROUP_FEATURE_SHUFFLE_RELATIVE_BIT = 32,
    VK_SUBGROUP_FEATURE_CLUSTERED_BIT = 64,
    VK_SUBGROUP_FEATURE_QUAD_BIT = 128
} VkSubgroupFeatureFlagBits;
typedef enum VkTessellationDomainOrigin {
    VK_TESSELLATION_DOMAIN_ORIGIN_UPPER_LEFT = 0,
    VK_TESSELLATION_DOMAIN_ORIGIN_LOWER_LEFT = 1
} VkTessellationDomainOrigin;
typedef enum VkSamplerYcbcrModelConversion {
    VK_SAMPLER_YCBCR_MODEL_CONVERSION_RGB_IDENTITY = 0,
    VK_SAMPLER_YCBCR_MODEL_CONVERSION_YCBCR_IDENTITY = 1,
    VK_SAMPLER_YCBCR_MODEL_CONVERSION_YCBCR_709 = 2,
    VK_SAMPLER_YCBCR_MODEL_CONVERSION_YCBCR_601 = 3,
    VK_SAMPLER_YCBCR_MODEL_CONVERSION_YCBCR_2020 = 4
} VkSamplerYcbcrModelConversion;
typedef enum VkSamplerYcbcrRange {
    VK_SAMPLER_YCBCR_RANGE_ITU_FULL = 0,
    VK_SAMPLER_YCBCR_RANGE_ITU_NARROW = 1
} VkSamplerYcbcrRange;
typedef enum VkChromaLocation {
    VK_CHROMA_LOCATION_COSITED_EVEN = 0,
    VK_CHROMA_LOCATION_MIDPOINT = 1
} VkChromaLocation;
typedef enum VkVendorId {
    VK_VENDOR_ID_VIV = 0x10001,
    VK_VENDOR_ID_VSI = 0x10002,
    VK_VENDOR_ID_KAZAN = 0x10003
} VkVendorId;
typedef void (VKAPI_PTR *PFN_vkInternalAllocationNotification)(
    void*                                       pUserData,
    size_t                                      size,
    VkInternalAllocationType                    allocationType,
    VkSystemAllocationScope                     allocationScope);
typedef void (VKAPI_PTR *PFN_vkInternalFreeNotification)(
    void*                                       pUserData,
    size_t                                      size,
    VkInternalAllocationType                    allocationType,
    VkSystemAllocationScope                     allocationScope);
typedef void* (VKAPI_PTR *PFN_vkReallocationFunction)(
    void*                                       pUserData,
    void*                                       pOriginal,
    size_t                                      size,
    size_t                                      alignment,
    VkSystemAllocationScope                     allocationScope);
typedef void* (VKAPI_PTR *PFN_vkAllocationFunction)(
    void*                                       pUserData,
    size_t                                      size,
    size_t                                      alignment,
    VkSystemAllocationScope                     allocationScope);
typedef void (VKAPI_PTR *PFN_vkFreeFunction)(
    void*                                       pUserData,
    void*                                       pMemory);
typedef void (VKAPI_PTR *PFN_vkVoidFunction)(void);
typedef struct VkBaseOutStructure {
    VkStructureType   sType;
    struct  VkBaseOutStructure *  pNext;
} VkBaseOutStructure;
typedef struct VkBaseInStructure {
    VkStructureType   sType;
    const struct  VkBaseInStructure *  pNext;
} VkBaseInStructure;
typedef struct VkOffset2D {
    int32_t          x;
    int32_t          y;
} VkOffset2D;
typedef struct VkOffset3D {
    int32_t          x;
    int32_t          y;
    int32_t          z;
} VkOffset3D;
typedef struct VkExtent2D {
    uint32_t          width;
    uint32_t          height;
} VkExtent2D;
typedef struct VkExtent3D {
    uint32_t          width;
    uint32_t          height;
    uint32_t          depth;
} VkExtent3D;
typedef struct VkViewport {
    float   x;
    float   y;
    float   width;
    float   height;
    float                         minDepth;
    float                         maxDepth;
} VkViewport;
typedef struct VkRect2D {
    VkOffset2D       offset;
    VkExtent2D       extent;
} VkRect2D;
typedef struct VkClearRect {
    VkRect2D         rect;
    uint32_t         baseArrayLayer;
    uint32_t         layerCount;
} VkClearRect;
typedef struct VkComponentMapping {
    VkComponentSwizzle   r;
    VkComponentSwizzle   g;
    VkComponentSwizzle   b;
    VkComponentSwizzle   a;
} VkComponentMapping;
typedef struct VkExtensionProperties {
    char              extensionName [ VK_MAX_EXTENSION_NAME_SIZE ];
    uint32_t          specVersion;
} VkExtensionProperties;
typedef struct VkLayerProperties {
    char              layerName [ VK_MAX_EXTENSION_NAME_SIZE ];
    uint32_t          specVersion;
    uint32_t          implementationVersion;
    char              description [ VK_MAX_DESCRIPTION_SIZE ];
} VkLayerProperties;
typedef struct VkApplicationInfo {
    VkStructureType   sType;
    const  void *      pNext;
    const  char *      pApplicationName;
    uint32_t          applicationVersion;
    const  char *      pEngineName;
    uint32_t          engineVersion;
    uint32_t          apiVersion;
} VkApplicationInfo;
typedef struct VkAllocationCallbacks {
    void *            pUserData;
    PFN_vkAllocationFunction     pfnAllocation;
    PFN_vkReallocationFunction   pfnReallocation;
    PFN_vkFreeFunction      pfnFree;
    PFN_vkInternalAllocationNotification   pfnInternalAllocation;
    PFN_vkInternalFreeNotification   pfnInternalFree;
} VkAllocationCallbacks;
typedef struct VkDescriptorImageInfo {
    VkSampler         sampler;
    VkImageView       imageView;
    VkImageLayout     imageLayout;
} VkDescriptorImageInfo;
typedef struct VkCopyDescriptorSet {
    VkStructureType   sType;
    const  void *             pNext;
    VkDescriptorSet          srcSet;
    uint32_t                 srcBinding;
    uint32_t                 srcArrayElement;
    VkDescriptorSet          dstSet;
    uint32_t                 dstBinding;
    uint32_t                 dstArrayElement;
    uint32_t                 descriptorCount;
} VkCopyDescriptorSet;
typedef struct VkDescriptorPoolSize {
    VkDescriptorType         type;
    uint32_t                 descriptorCount;
} VkDescriptorPoolSize;
typedef struct VkDescriptorSetAllocateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkDescriptorPool         descriptorPool;
    uint32_t                 descriptorSetCount;
    const  VkDescriptorSetLayout *  pSetLayouts;
} VkDescriptorSetAllocateInfo;
typedef struct VkSpecializationMapEntry {
    uint32_t                       constantID;
    uint32_t                       offset;
    size_t   size;
} VkSpecializationMapEntry;
typedef struct VkSpecializationInfo {
    uint32_t                 mapEntryCount;
    const  VkSpecializationMapEntry *  pMapEntries;
    size_t                   dataSize;
    const  void *             pData;
} VkSpecializationInfo;
typedef struct VkVertexInputBindingDescription {
    uint32_t                 binding;
    uint32_t                 stride;
    VkVertexInputRate        inputRate;
} VkVertexInputBindingDescription;
typedef struct VkVertexInputAttributeDescription {
    uint32_t                 location;
    uint32_t                 binding;
    VkFormat                 format;
    uint32_t                 offset;
} VkVertexInputAttributeDescription;
typedef struct VkStencilOpState {
    VkStencilOp              failOp;
    VkStencilOp              passOp;
    VkStencilOp              depthFailOp;
    VkCompareOp              compareOp;
    uint32_t                 compareMask;
    uint32_t                 writeMask;
    uint32_t                 reference;
} VkStencilOpState;
typedef struct VkCommandBufferAllocateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkCommandPool            commandPool;
    VkCommandBufferLevel     level;
    uint32_t                 commandBufferCount;
} VkCommandBufferAllocateInfo;
typedef union VkClearColorValue {
    float                    float32 [4];
    int32_t                  int32 [4];
    uint32_t                 uint32 [4];
} VkClearColorValue;
typedef struct VkClearDepthStencilValue {
    float                    depth;
    uint32_t                 stencil;
} VkClearDepthStencilValue;
typedef union VkClearValue {
    VkClearColorValue        color;
    VkClearDepthStencilValue   depthStencil;
} VkClearValue;
typedef struct VkAttachmentReference {
    uint32_t                 attachment;
    VkImageLayout            layout;
} VkAttachmentReference;
typedef struct VkDrawIndirectCommand {
    uint32_t                         vertexCount;
    uint32_t                         instanceCount;
    uint32_t                         firstVertex;
    uint32_t   firstInstance;
} VkDrawIndirectCommand;
typedef struct VkDrawIndexedIndirectCommand {
    uint32_t                         indexCount;
    uint32_t                         instanceCount;
    uint32_t                         firstIndex;
    int32_t                          vertexOffset;
    uint32_t   firstInstance;
} VkDrawIndexedIndirectCommand;
typedef struct VkDispatchIndirectCommand {
    uint32_t   x;
    uint32_t   y;
    uint32_t   z;
} VkDispatchIndirectCommand;
typedef struct VkSurfaceFormatKHR {
    VkFormat                           format;
    VkColorSpaceKHR                    colorSpace;
} VkSurfaceFormatKHR;
typedef struct VkPresentInfoKHR {
    VkStructureType   sType;
    const  void *   pNext;
    uint32_t           waitSemaphoreCount;
    const  VkSemaphore *  pWaitSemaphores;
    uint32_t                           swapchainCount;
    const  VkSwapchainKHR *  pSwapchains;
    const  uint32_t *  pImageIndices;
    VkResult *  pResults;
} VkPresentInfoKHR;
typedef struct VkPhysicalDeviceExternalImageFormatInfo {
    VkStructureType   sType;
    const  void *                       pNext;
    VkExternalMemoryHandleTypeFlagBits   handleType;
} VkPhysicalDeviceExternalImageFormatInfo;
typedef struct VkPhysicalDeviceExternalSemaphoreInfo {
    VkStructureType   sType;
    const  void *                       pNext;
    VkExternalSemaphoreHandleTypeFlagBits   handleType;
} VkPhysicalDeviceExternalSemaphoreInfo;
typedef struct VkPhysicalDeviceExternalFenceInfo {
    VkStructureType   sType;
    const  void *                       pNext;
    VkExternalFenceHandleTypeFlagBits   handleType;
} VkPhysicalDeviceExternalFenceInfo;
typedef struct VkPhysicalDeviceMultiviewProperties {
    VkStructureType   sType;
    void *                             pNext;
    uint32_t                           maxMultiviewViewCount;
    uint32_t                           maxMultiviewInstanceIndex;
} VkPhysicalDeviceMultiviewProperties;
typedef struct VkRenderPassMultiviewCreateInfo {
    VkStructureType          sType;
    const  void *             pNext;
    uint32_t                 subpassCount;
    const  uint32_t *      pViewMasks;
    uint32_t                 dependencyCount;
    const  int32_t *    pViewOffsets;
    uint32_t                 correlationMaskCount;
    const  uint32_t *  pCorrelationMasks;
} VkRenderPassMultiviewCreateInfo;
typedef struct VkBindBufferMemoryDeviceGroupInfo {
    VkStructureType   sType;
    const  void *                       pNext;
    uint32_t           deviceIndexCount;
    const  uint32_t *   pDeviceIndices;
} VkBindBufferMemoryDeviceGroupInfo;
typedef struct VkBindImageMemoryDeviceGroupInfo {
    VkStructureType   sType;
    const  void *                       pNext;
    uint32_t           deviceIndexCount;
    const  uint32_t *   pDeviceIndices;
    uint32_t           splitInstanceBindRegionCount;
    const  VkRect2D *   pSplitInstanceBindRegions;
} VkBindImageMemoryDeviceGroupInfo;
typedef struct VkDeviceGroupRenderPassBeginInfo {
    VkStructureType   sType;
    const  void *                       pNext;
    uint32_t                           deviceMask;
    uint32_t           deviceRenderAreaCount;
    const  VkRect2D *   pDeviceRenderAreas;
} VkDeviceGroupRenderPassBeginInfo;
typedef struct VkDeviceGroupCommandBufferBeginInfo {
    VkStructureType   sType;
    const  void *                       pNext;
    uint32_t                           deviceMask;
} VkDeviceGroupCommandBufferBeginInfo;
typedef struct VkDeviceGroupSubmitInfo {
    VkStructureType   sType;
    const  void *                       pNext;
    uint32_t           waitSemaphoreCount;
    const  uint32_t *     pWaitSemaphoreDeviceIndices;
    uint32_t           commandBufferCount;
    const  uint32_t *     pCommandBufferDeviceMasks;
    uint32_t           signalSemaphoreCount;
    const  uint32_t *   pSignalSemaphoreDeviceIndices;
} VkDeviceGroupSubmitInfo;
typedef struct VkDeviceGroupBindSparseInfo {
    VkStructureType   sType;
    const  void *                       pNext;
    uint32_t                           resourceDeviceIndex;
    uint32_t                           memoryDeviceIndex;
} VkDeviceGroupBindSparseInfo;
typedef struct VkImageSwapchainCreateInfoKHR {
    VkStructureType   sType;
    const  void *                       pNext;
    VkSwapchainKHR     swapchain;
} VkImageSwapchainCreateInfoKHR;
typedef struct VkBindImageMemorySwapchainInfoKHR {
    VkStructureType   sType;
    const  void *                       pNext;
    VkSwapchainKHR   swapchain;
    uint32_t                           imageIndex;
} VkBindImageMemorySwapchainInfoKHR;
typedef struct VkAcquireNextImageInfoKHR {
    VkStructureType   sType;
    const  void *                       pNext;
    VkSwapchainKHR   swapchain;
    uint64_t                           timeout;
    VkSemaphore   semaphore;
    VkFence   fence;
    uint32_t                           deviceMask;
} VkAcquireNextImageInfoKHR;
typedef struct VkDeviceGroupPresentInfoKHR {
    VkStructureType   sType;
    const  void *                       pNext;
    uint32_t           swapchainCount;
    const  uint32_t *  pDeviceMasks;
    VkDeviceGroupPresentModeFlagBitsKHR   mode;
} VkDeviceGroupPresentInfoKHR;
typedef struct VkDeviceGroupDeviceCreateInfo {
    VkStructureType   sType;
    const  void *                       pNext;
    uint32_t                           physicalDeviceCount;
    const  VkPhysicalDevice *   pPhysicalDevices;
} VkDeviceGroupDeviceCreateInfo;
typedef struct VkDescriptorUpdateTemplateEntry {
    uint32_t                           dstBinding;
    uint32_t                           dstArrayElement;
    uint32_t                           descriptorCount;
    VkDescriptorType                   descriptorType;
    size_t                             offset;
    size_t                             stride;
} VkDescriptorUpdateTemplateEntry;
typedef struct VkBufferMemoryRequirementsInfo2 {
    VkStructureType   sType;
    const  void *                                                           pNext;
    VkBuffer                                                               buffer;
} VkBufferMemoryRequirementsInfo2;
typedef struct VkImageMemoryRequirementsInfo2 {
    VkStructureType   sType;
    const  void *                                                           pNext;
    VkImage                                                                image;
} VkImageMemoryRequirementsInfo2;
typedef struct VkImageSparseMemoryRequirementsInfo2 {
    VkStructureType   sType;
    const  void *                                                           pNext;
    VkImage                                                                image;
} VkImageSparseMemoryRequirementsInfo2;
typedef struct VkPhysicalDevicePointClippingProperties {
    VkStructureType   sType;
    void *                             pNext;
    VkPointClippingBehavior        pointClippingBehavior;
} VkPhysicalDevicePointClippingProperties;
typedef struct VkMemoryDedicatedAllocateInfo {
    VkStructureType   sType;
    const  void *                       pNext;
    VkImage            image;
    VkBuffer           buffer;
} VkMemoryDedicatedAllocateInfo;
typedef struct VkPipelineTessellationDomainOriginStateCreateInfo {
    VkStructureType   sType;
    const  void *                       pNext;
    VkTessellationDomainOrigin      domainOrigin;
} VkPipelineTessellationDomainOriginStateCreateInfo;
typedef struct VkSamplerYcbcrConversionInfo {
    VkStructureType   sType;
    const  void *                       pNext;
    VkSamplerYcbcrConversion        conversion;
} VkSamplerYcbcrConversionInfo;
typedef struct VkBindImagePlaneMemoryInfo {
    VkStructureType   sType;
    const  void *                       pNext;
    VkImageAspectFlagBits              planeAspect;
} VkBindImagePlaneMemoryInfo;
typedef struct VkImagePlaneMemoryRequirementsInfo {
    VkStructureType   sType;
    const  void *                       pNext;
    VkImageAspectFlagBits              planeAspect;
} VkImagePlaneMemoryRequirementsInfo;
typedef struct VkSamplerYcbcrConversionImageFormatProperties {
    VkStructureType   sType;
    void *       pNext;
    uint32_t                           combinedImageSamplerDescriptorCount;
} VkSamplerYcbcrConversionImageFormatProperties;
typedef uint32_t VkSampleMask;
typedef uint32_t VkBool32;
typedef uint32_t VkFlags;
typedef uint64_t VkDeviceSize;
typedef VkFlags VkFramebufferCreateFlags;
typedef VkFlags VkQueryPoolCreateFlags;
typedef VkFlags VkRenderPassCreateFlags;
typedef VkFlags VkSamplerCreateFlags;
typedef VkFlags VkPipelineLayoutCreateFlags;
typedef VkFlags VkPipelineCacheCreateFlags;
typedef VkFlags VkPipelineDepthStencilStateCreateFlags;
typedef VkFlags VkPipelineDynamicStateCreateFlags;
typedef VkFlags VkPipelineColorBlendStateCreateFlags;
typedef VkFlags VkPipelineMultisampleStateCreateFlags;
typedef VkFlags VkPipelineRasterizationStateCreateFlags;
typedef VkFlags VkPipelineViewportStateCreateFlags;
typedef VkFlags VkPipelineTessellationStateCreateFlags;
typedef VkFlags VkPipelineInputAssemblyStateCreateFlags;
typedef VkFlags VkPipelineVertexInputStateCreateFlags;
typedef VkFlags VkPipelineShaderStageCreateFlags;
typedef VkFlags VkDescriptorSetLayoutCreateFlags;
typedef VkFlags VkBufferViewCreateFlags;
typedef VkFlags VkInstanceCreateFlags;
typedef VkFlags VkDeviceCreateFlags;
typedef VkFlags VkDeviceQueueCreateFlags;
typedef VkFlags VkQueueFlags;
typedef VkFlags VkMemoryPropertyFlags;
typedef VkFlags VkMemoryHeapFlags;
typedef VkFlags VkAccessFlags;
typedef VkFlags VkBufferUsageFlags;
typedef VkFlags VkBufferCreateFlags;
typedef VkFlags VkShaderStageFlags;
typedef VkFlags VkImageUsageFlags;
typedef VkFlags VkImageCreateFlags;
typedef VkFlags VkImageViewCreateFlags;
typedef VkFlags VkPipelineCreateFlags;
typedef VkFlags VkColorComponentFlags;
typedef VkFlags VkFenceCreateFlags;
typedef VkFlags VkSemaphoreCreateFlags;
typedef VkFlags VkFormatFeatureFlags;
typedef VkFlags VkQueryControlFlags;
typedef VkFlags VkQueryResultFlags;
typedef VkFlags VkShaderModuleCreateFlags;
typedef VkFlags VkEventCreateFlags;
typedef VkFlags VkCommandPoolCreateFlags;
typedef VkFlags VkCommandPoolResetFlags;
typedef VkFlags VkCommandBufferResetFlags;
typedef VkFlags VkCommandBufferUsageFlags;
typedef VkFlags VkQueryPipelineStatisticFlags;
typedef VkFlags VkMemoryMapFlags;
typedef VkFlags VkImageAspectFlags;
typedef VkFlags VkSparseMemoryBindFlags;
typedef VkFlags VkSparseImageFormatFlags;
typedef VkFlags VkSubpassDescriptionFlags;
typedef VkFlags VkPipelineStageFlags;
typedef VkFlags VkSampleCountFlags;
typedef VkFlags VkAttachmentDescriptionFlags;
typedef VkFlags VkStencilFaceFlags;
typedef VkFlags VkCullModeFlags;
typedef VkFlags VkDescriptorPoolCreateFlags;
typedef VkFlags VkDescriptorPoolResetFlags;
typedef VkFlags VkDependencyFlags;
typedef VkFlags VkSubgroupFeatureFlags;
typedef VkFlags VkDescriptorUpdateTemplateCreateFlags;
typedef VkFlags VkCompositeAlphaFlagsKHR;
typedef VkFlags VkSurfaceTransformFlagsKHR;
typedef VkFlags VkSwapchainCreateFlagsKHR;
typedef VkFlags VkPeerMemoryFeatureFlags;
typedef VkFlags VkMemoryAllocateFlags;
typedef VkFlags VkDeviceGroupPresentModeFlagsKHR;
typedef VkFlags VkDebugReportFlagsEXT;
typedef VkFlags VkCommandPoolTrimFlags;
typedef VkFlags VkExternalMemoryHandleTypeFlags;
typedef VkFlags VkExternalMemoryFeatureFlags;
typedef VkFlags VkExternalSemaphoreHandleTypeFlags;
typedef VkFlags VkExternalSemaphoreFeatureFlags;
typedef VkFlags VkSemaphoreImportFlags;
typedef VkFlags VkExternalFenceHandleTypeFlags;
typedef VkFlags VkExternalFenceFeatureFlags;
typedef VkFlags VkFenceImportFlags;
typedef VkBool32 (VKAPI_PTR *PFN_vkDebugReportCallbackEXT)(
    VkDebugReportFlagsEXT                       flags,
    VkDebugReportObjectTypeEXT                  objectType,
    uint64_t                                    object,
    size_t                                      location,
    int32_t                                     messageCode,
    const char*                                 pLayerPrefix,
    const char*                                 pMessage,
    void*                                       pUserData);
typedef struct VkDeviceQueueCreateInfo {
    VkStructureType   sType;
    const  void *      pNext;
    VkDeviceQueueCreateFlags      flags;
    uint32_t          queueFamilyIndex;
    uint32_t          queueCount;
    const  float *     pQueuePriorities;
} VkDeviceQueueCreateInfo;
typedef struct VkInstanceCreateInfo {
    VkStructureType   sType;
    const  void *      pNext;
    VkInstanceCreateFlags    flags;
    const  VkApplicationInfo *  pApplicationInfo;
    uint32_t                 enabledLayerCount;
    const  char * const*       ppEnabledLayerNames;
    uint32_t                 enabledExtensionCount;
    const  char * const*       ppEnabledExtensionNames;
} VkInstanceCreateInfo;
typedef struct VkQueueFamilyProperties {
    VkQueueFlags             queueFlags;
    uint32_t                 queueCount;
    uint32_t                 timestampValidBits;
    VkExtent3D               minImageTransferGranularity;
} VkQueueFamilyProperties;
typedef struct VkMemoryAllocateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkDeviceSize             allocationSize;
    uint32_t                 memoryTypeIndex;
} VkMemoryAllocateInfo;
typedef struct VkMemoryRequirements {
    VkDeviceSize             size;
    VkDeviceSize             alignment;
    uint32_t                 memoryTypeBits;
} VkMemoryRequirements;
typedef struct VkSparseImageFormatProperties {
    VkImageAspectFlags       aspectMask;
    VkExtent3D               imageGranularity;
    VkSparseImageFormatFlags   flags;
} VkSparseImageFormatProperties;
typedef struct VkSparseImageMemoryRequirements {
    VkSparseImageFormatProperties   formatProperties;
    uint32_t                 imageMipTailFirstLod;
    VkDeviceSize             imageMipTailSize;
    VkDeviceSize             imageMipTailOffset;
    VkDeviceSize             imageMipTailStride;
} VkSparseImageMemoryRequirements;
typedef struct VkMemoryType {
    VkMemoryPropertyFlags    propertyFlags;
    uint32_t                 heapIndex;
} VkMemoryType;
typedef struct VkMemoryHeap {
    VkDeviceSize             size;
    VkMemoryHeapFlags        flags;
} VkMemoryHeap;
typedef struct VkMappedMemoryRange {
    VkStructureType   sType;
    const  void *             pNext;
    VkDeviceMemory           memory;
    VkDeviceSize             offset;
    VkDeviceSize             size;
} VkMappedMemoryRange;
typedef struct VkFormatProperties {
    VkFormatFeatureFlags     linearTilingFeatures;
    VkFormatFeatureFlags     optimalTilingFeatures;
    VkFormatFeatureFlags     bufferFeatures;
} VkFormatProperties;
typedef struct VkImageFormatProperties {
    VkExtent3D               maxExtent;
    uint32_t                 maxMipLevels;
    uint32_t                 maxArrayLayers;
    VkSampleCountFlags       sampleCounts;
    VkDeviceSize             maxResourceSize;
} VkImageFormatProperties;
typedef struct VkDescriptorBufferInfo {
    VkBuffer                 buffer;
    VkDeviceSize             offset;
    VkDeviceSize             range;
} VkDescriptorBufferInfo;
typedef struct VkWriteDescriptorSet {
    VkStructureType   sType;
    const  void *             pNext;
    VkDescriptorSet          dstSet;
    uint32_t                 dstBinding;
    uint32_t                 dstArrayElement;
    uint32_t                 descriptorCount;
    VkDescriptorType         descriptorType;
    const  VkDescriptorImageInfo *  pImageInfo;
    const  VkDescriptorBufferInfo *  pBufferInfo;
    const  VkBufferView *     pTexelBufferView;
} VkWriteDescriptorSet;
typedef struct VkBufferCreateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkBufferCreateFlags      flags;
    VkDeviceSize             size;
    VkBufferUsageFlags       usage;
    VkSharingMode            sharingMode;
    uint32_t                 queueFamilyIndexCount;
    const  uint32_t *         pQueueFamilyIndices;
} VkBufferCreateInfo;
typedef struct VkBufferViewCreateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkBufferViewCreateFlags flags;
    VkBuffer                 buffer;
    VkFormat                 format;
    VkDeviceSize             offset;
    VkDeviceSize             range;
} VkBufferViewCreateInfo;
typedef struct VkImageSubresource {
    VkImageAspectFlags       aspectMask;
    uint32_t                 mipLevel;
    uint32_t                 arrayLayer;
} VkImageSubresource;
typedef struct VkImageSubresourceLayers {
    VkImageAspectFlags       aspectMask;
    uint32_t                 mipLevel;
    uint32_t                 baseArrayLayer;
    uint32_t                 layerCount;
} VkImageSubresourceLayers;
typedef struct VkImageSubresourceRange {
    VkImageAspectFlags       aspectMask;
    uint32_t                 baseMipLevel;
    uint32_t                 levelCount;
    uint32_t                 baseArrayLayer;
    uint32_t                 layerCount;
} VkImageSubresourceRange;
typedef struct VkMemoryBarrier {
    VkStructureType   sType;
    const  void *             pNext;
    VkAccessFlags            srcAccessMask;
    VkAccessFlags            dstAccessMask;
} VkMemoryBarrier;
typedef struct VkBufferMemoryBarrier {
    VkStructureType   sType;
    const  void *             pNext;
    VkAccessFlags            srcAccessMask;
    VkAccessFlags            dstAccessMask;
    uint32_t                 srcQueueFamilyIndex;
    uint32_t                 dstQueueFamilyIndex;
    VkBuffer                 buffer;
    VkDeviceSize             offset;
    VkDeviceSize             size;
} VkBufferMemoryBarrier;
typedef struct VkImageMemoryBarrier {
    VkStructureType   sType;
    const  void *             pNext;
    VkAccessFlags            srcAccessMask;
    VkAccessFlags            dstAccessMask;
    VkImageLayout            oldLayout;
    VkImageLayout            newLayout;
    uint32_t                 srcQueueFamilyIndex;
    uint32_t                 dstQueueFamilyIndex;
    VkImage                  image;
    VkImageSubresourceRange   subresourceRange;
} VkImageMemoryBarrier;
typedef struct VkImageCreateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkImageCreateFlags       flags;
    VkImageType              imageType;
    VkFormat                 format;
    VkExtent3D               extent;
    uint32_t                 mipLevels;
    uint32_t                 arrayLayers;
    VkSampleCountFlagBits    samples;
    VkImageTiling            tiling;
    VkImageUsageFlags        usage;
    VkSharingMode            sharingMode;
    uint32_t                 queueFamilyIndexCount;
    const  uint32_t *         pQueueFamilyIndices;
    VkImageLayout            initialLayout;
} VkImageCreateInfo;
typedef struct VkSubresourceLayout {
    VkDeviceSize             offset;
    VkDeviceSize             size;
    VkDeviceSize             rowPitch;
    VkDeviceSize             arrayPitch;
    VkDeviceSize             depthPitch;
} VkSubresourceLayout;
typedef struct VkImageViewCreateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkImageViewCreateFlags   flags;
    VkImage                  image;
    VkImageViewType          viewType;
    VkFormat                 format;
    VkComponentMapping       components;
    VkImageSubresourceRange   subresourceRange;
} VkImageViewCreateInfo;
typedef struct VkBufferCopy {
    VkDeviceSize                         srcOffset;
    VkDeviceSize                         dstOffset;
    VkDeviceSize   size;
} VkBufferCopy;
typedef struct VkSparseMemoryBind {
    VkDeviceSize             resourceOffset;
    VkDeviceSize             size;
    VkDeviceMemory           memory;
    VkDeviceSize             memoryOffset;
    VkSparseMemoryBindFlags flags;
} VkSparseMemoryBind;
typedef struct VkSparseImageMemoryBind {
    VkImageSubresource       subresource;
    VkOffset3D               offset;
    VkExtent3D               extent;
    VkDeviceMemory           memory;
    VkDeviceSize             memoryOffset;
    VkSparseMemoryBindFlags flags;
} VkSparseImageMemoryBind;
typedef struct VkSparseBufferMemoryBindInfo {
    VkBuffer   buffer;
    uint32_t                 bindCount;
    const  VkSparseMemoryBind *  pBinds;
} VkSparseBufferMemoryBindInfo;
typedef struct VkSparseImageOpaqueMemoryBindInfo {
    VkImage   image;
    uint32_t                 bindCount;
    const  VkSparseMemoryBind *  pBinds;
} VkSparseImageOpaqueMemoryBindInfo;
typedef struct VkSparseImageMemoryBindInfo {
    VkImage   image;
    uint32_t                 bindCount;
    const  VkSparseImageMemoryBind *  pBinds;
} VkSparseImageMemoryBindInfo;
typedef struct VkBindSparseInfo {
    VkStructureType   sType;
    const  void *             pNext;
    uint32_t                 waitSemaphoreCount;
    const  VkSemaphore *      pWaitSemaphores;
    uint32_t                 bufferBindCount;
    const  VkSparseBufferMemoryBindInfo *  pBufferBinds;
    uint32_t                 imageOpaqueBindCount;
    const  VkSparseImageOpaqueMemoryBindInfo *  pImageOpaqueBinds;
    uint32_t                 imageBindCount;
    const  VkSparseImageMemoryBindInfo *  pImageBinds;
    uint32_t                 signalSemaphoreCount;
    const  VkSemaphore *      pSignalSemaphores;
} VkBindSparseInfo;
typedef struct VkImageCopy {
    VkImageSubresourceLayers   srcSubresource;
    VkOffset3D               srcOffset;
    VkImageSubresourceLayers   dstSubresource;
    VkOffset3D               dstOffset;
    VkExtent3D               extent;
} VkImageCopy;
typedef struct VkImageBlit {
    VkImageSubresourceLayers   srcSubresource;
    VkOffset3D               srcOffsets [2];
    VkImageSubresourceLayers   dstSubresource;
    VkOffset3D               dstOffsets [2];
} VkImageBlit;
typedef struct VkBufferImageCopy {
    VkDeviceSize             bufferOffset;
    uint32_t                 bufferRowLength;
    uint32_t                 bufferImageHeight;
    VkImageSubresourceLayers   imageSubresource;
    VkOffset3D               imageOffset;
    VkExtent3D               imageExtent;
} VkBufferImageCopy;
typedef struct VkImageResolve {
    VkImageSubresourceLayers   srcSubresource;
    VkOffset3D               srcOffset;
    VkImageSubresourceLayers   dstSubresource;
    VkOffset3D               dstOffset;
    VkExtent3D               extent;
} VkImageResolve;
typedef struct VkShaderModuleCreateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkShaderModuleCreateFlags   flags;
    size_t                   codeSize;
    const  uint32_t *             pCode;
} VkShaderModuleCreateInfo;
typedef struct VkDescriptorSetLayoutBinding {
    uint32_t                 binding;
    VkDescriptorType         descriptorType;
    uint32_t   descriptorCount;
    VkShaderStageFlags       stageFlags;
    const  VkSampler *        pImmutableSamplers;
} VkDescriptorSetLayoutBinding;
typedef struct VkDescriptorSetLayoutCreateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkDescriptorSetLayoutCreateFlags      flags;
    uint32_t                 bindingCount;
    const  VkDescriptorSetLayoutBinding *  pBindings;
} VkDescriptorSetLayoutCreateInfo;
typedef struct VkDescriptorPoolCreateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkDescriptorPoolCreateFlags    flags;
    uint32_t                 maxSets;
    uint32_t                 poolSizeCount;
    const  VkDescriptorPoolSize *  pPoolSizes;
} VkDescriptorPoolCreateInfo;
typedef struct VkPipelineShaderStageCreateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkPipelineShaderStageCreateFlags      flags;
    VkShaderStageFlagBits    stage;
    VkShaderModule           module;
    const  char *             pName;
    const  VkSpecializationInfo *  pSpecializationInfo;
} VkPipelineShaderStageCreateInfo;
typedef struct VkComputePipelineCreateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkPipelineCreateFlags    flags;
    VkPipelineShaderStageCreateInfo   stage;
    VkPipelineLayout         layout;
    VkPipeline        basePipelineHandle;
    int32_t                  basePipelineIndex;
} VkComputePipelineCreateInfo;
typedef struct VkPipelineVertexInputStateCreateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkPipelineVertexInputStateCreateFlags      flags;
    uint32_t                 vertexBindingDescriptionCount;
    const  VkVertexInputBindingDescription *  pVertexBindingDescriptions;
    uint32_t                 vertexAttributeDescriptionCount;
    const  VkVertexInputAttributeDescription *  pVertexAttributeDescriptions;
} VkPipelineVertexInputStateCreateInfo;
typedef struct VkPipelineInputAssemblyStateCreateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkPipelineInputAssemblyStateCreateFlags      flags;
    VkPrimitiveTopology      topology;
    VkBool32                 primitiveRestartEnable;
} VkPipelineInputAssemblyStateCreateInfo;
typedef struct VkPipelineTessellationStateCreateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkPipelineTessellationStateCreateFlags      flags;
    uint32_t                 patchControlPoints;
} VkPipelineTessellationStateCreateInfo;
typedef struct VkPipelineViewportStateCreateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkPipelineViewportStateCreateFlags      flags;
    uint32_t                 viewportCount;
    const  VkViewport *       pViewports;
    uint32_t                 scissorCount;
    const  VkRect2D *         pScissors;
} VkPipelineViewportStateCreateInfo;
typedef struct VkPipelineRasterizationStateCreateInfo {
    VkStructureType   sType;
    const  void *  pNext;
    VkPipelineRasterizationStateCreateFlags      flags;
    VkBool32                 depthClampEnable;
    VkBool32                 rasterizerDiscardEnable;
    VkPolygonMode            polygonMode;
    VkCullModeFlags          cullMode;
    VkFrontFace              frontFace;
    VkBool32                 depthBiasEnable;
    float                    depthBiasConstantFactor;
    float                    depthBiasClamp;
    float                    depthBiasSlopeFactor;
    float                    lineWidth;
} VkPipelineRasterizationStateCreateInfo;
typedef struct VkPipelineMultisampleStateCreateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkPipelineMultisampleStateCreateFlags      flags;
    VkSampleCountFlagBits    rasterizationSamples;
    VkBool32                 sampleShadingEnable;
    float                    minSampleShading;
    const  VkSampleMask *     pSampleMask;
    VkBool32                 alphaToCoverageEnable;
    VkBool32                 alphaToOneEnable;
} VkPipelineMultisampleStateCreateInfo;
typedef struct VkPipelineColorBlendAttachmentState {
    VkBool32                 blendEnable;
    VkBlendFactor            srcColorBlendFactor;
    VkBlendFactor            dstColorBlendFactor;
    VkBlendOp                colorBlendOp;
    VkBlendFactor            srcAlphaBlendFactor;
    VkBlendFactor            dstAlphaBlendFactor;
    VkBlendOp                alphaBlendOp;
    VkColorComponentFlags    colorWriteMask;
} VkPipelineColorBlendAttachmentState;
typedef struct VkPipelineColorBlendStateCreateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkPipelineColorBlendStateCreateFlags      flags;
    VkBool32                 logicOpEnable;
    VkLogicOp                logicOp;
    uint32_t                 attachmentCount;
    const  VkPipelineColorBlendAttachmentState *  pAttachments;
    float                    blendConstants [4];
} VkPipelineColorBlendStateCreateInfo;
typedef struct VkPipelineDynamicStateCreateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkPipelineDynamicStateCreateFlags      flags;
    uint32_t                 dynamicStateCount;
    const  VkDynamicState *   pDynamicStates;
} VkPipelineDynamicStateCreateInfo;
typedef struct VkPipelineDepthStencilStateCreateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkPipelineDepthStencilStateCreateFlags      flags;
    VkBool32                 depthTestEnable;
    VkBool32                 depthWriteEnable;
    VkCompareOp              depthCompareOp;
    VkBool32                 depthBoundsTestEnable;
    VkBool32                 stencilTestEnable;
    VkStencilOpState         front;
    VkStencilOpState         back;
    float                    minDepthBounds;
    float                    maxDepthBounds;
} VkPipelineDepthStencilStateCreateInfo;
typedef struct VkGraphicsPipelineCreateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkPipelineCreateFlags    flags;
    uint32_t                 stageCount;
    const  VkPipelineShaderStageCreateInfo *  pStages;
    const  VkPipelineVertexInputStateCreateInfo *  pVertexInputState;
    const  VkPipelineInputAssemblyStateCreateInfo *  pInputAssemblyState;
    const  VkPipelineTessellationStateCreateInfo *  pTessellationState;
    const  VkPipelineViewportStateCreateInfo *  pViewportState;
    const  VkPipelineRasterizationStateCreateInfo *  pRasterizationState;
    const  VkPipelineMultisampleStateCreateInfo *  pMultisampleState;
    const  VkPipelineDepthStencilStateCreateInfo *  pDepthStencilState;
    const  VkPipelineColorBlendStateCreateInfo *  pColorBlendState;
    const  VkPipelineDynamicStateCreateInfo *  pDynamicState;
    VkPipelineLayout         layout;
    VkRenderPass             renderPass;
    uint32_t                 subpass;
    VkPipeline        basePipelineHandle;
    int32_t                  basePipelineIndex;
} VkGraphicsPipelineCreateInfo;
typedef struct VkPipelineCacheCreateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkPipelineCacheCreateFlags      flags;
    size_t                   initialDataSize;
    const  void *             pInitialData;
} VkPipelineCacheCreateInfo;
typedef struct VkPushConstantRange {
    VkShaderStageFlags       stageFlags;
    uint32_t                 offset;
    uint32_t                 size;
} VkPushConstantRange;
typedef struct VkPipelineLayoutCreateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkPipelineLayoutCreateFlags      flags;
    uint32_t                 setLayoutCount;
    const  VkDescriptorSetLayout *  pSetLayouts;
    uint32_t                 pushConstantRangeCount;
    const  VkPushConstantRange *  pPushConstantRanges;
} VkPipelineLayoutCreateInfo;
typedef struct VkSamplerCreateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkSamplerCreateFlags     flags;
    VkFilter                 magFilter;
    VkFilter                 minFilter;
    VkSamplerMipmapMode      mipmapMode;
    VkSamplerAddressMode     addressModeU;
    VkSamplerAddressMode     addressModeV;
    VkSamplerAddressMode     addressModeW;
    float                    mipLodBias;
    VkBool32                 anisotropyEnable;
    float                    maxAnisotropy;
    VkBool32                 compareEnable;
    VkCompareOp              compareOp;
    float                    minLod;
    float                    maxLod;
    VkBorderColor            borderColor;
    VkBool32                 unnormalizedCoordinates;
} VkSamplerCreateInfo;
typedef struct VkCommandPoolCreateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkCommandPoolCreateFlags     flags;
    uint32_t                 queueFamilyIndex;
} VkCommandPoolCreateInfo;
typedef struct VkCommandBufferInheritanceInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkRenderPass      renderPass;
    uint32_t                 subpass;
    VkFramebuffer     framebuffer;
    VkBool32                 occlusionQueryEnable;
    VkQueryControlFlags      queryFlags;
    VkQueryPipelineStatisticFlags   pipelineStatistics;
} VkCommandBufferInheritanceInfo;
typedef struct VkCommandBufferBeginInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkCommandBufferUsageFlags    flags;
    const  VkCommandBufferInheritanceInfo *        pInheritanceInfo;
} VkCommandBufferBeginInfo;
typedef struct VkRenderPassBeginInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkRenderPass             renderPass;
    VkFramebuffer            framebuffer;
    VkRect2D                 renderArea;
    uint32_t                 clearValueCount;
    const  VkClearValue *     pClearValues;
} VkRenderPassBeginInfo;
typedef struct VkClearAttachment {
    VkImageAspectFlags       aspectMask;
    uint32_t                 colorAttachment;
    VkClearValue             clearValue;
} VkClearAttachment;
typedef struct VkAttachmentDescription {
    VkAttachmentDescriptionFlags   flags;
    VkFormat                 format;
    VkSampleCountFlagBits    samples;
    VkAttachmentLoadOp       loadOp;
    VkAttachmentStoreOp      storeOp;
    VkAttachmentLoadOp       stencilLoadOp;
    VkAttachmentStoreOp      stencilStoreOp;
    VkImageLayout            initialLayout;
    VkImageLayout            finalLayout;
} VkAttachmentDescription;
typedef struct VkSubpassDescription {
    VkSubpassDescriptionFlags   flags;
    VkPipelineBindPoint      pipelineBindPoint;
    uint32_t                 inputAttachmentCount;
    const  VkAttachmentReference *  pInputAttachments;
    uint32_t                 colorAttachmentCount;
    const  VkAttachmentReference *  pColorAttachments;
    const  VkAttachmentReference *  pResolveAttachments;
    const  VkAttachmentReference *  pDepthStencilAttachment;
    uint32_t                 preserveAttachmentCount;
    const  uint32_t *  pPreserveAttachments;
} VkSubpassDescription;
typedef struct VkSubpassDependency {
    uint32_t                 srcSubpass;
    uint32_t                 dstSubpass;
    VkPipelineStageFlags     srcStageMask;
    VkPipelineStageFlags     dstStageMask;
    VkAccessFlags            srcAccessMask;
    VkAccessFlags            dstAccessMask;
    VkDependencyFlags        dependencyFlags;
} VkSubpassDependency;
typedef struct VkRenderPassCreateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkRenderPassCreateFlags      flags;
    uint32_t     attachmentCount;
    const  VkAttachmentDescription *  pAttachments;
    uint32_t                 subpassCount;
    const  VkSubpassDescription *  pSubpasses;
    uint32_t         dependencyCount;
    const  VkSubpassDependency *  pDependencies;
} VkRenderPassCreateInfo;
typedef struct VkEventCreateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkEventCreateFlags       flags;
} VkEventCreateInfo;
typedef struct VkFenceCreateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkFenceCreateFlags       flags;
} VkFenceCreateInfo;
typedef struct VkPhysicalDeviceFeatures {
    VkBool32                 robustBufferAccess;
    VkBool32                 fullDrawIndexUint32;
    VkBool32                 imageCubeArray;
    VkBool32                 independentBlend;
    VkBool32                 geometryShader;
    VkBool32                 tessellationShader;
    VkBool32                 sampleRateShading;
    VkBool32                 dualSrcBlend;
    VkBool32                 logicOp;
    VkBool32                 multiDrawIndirect;
    VkBool32                 drawIndirectFirstInstance;
    VkBool32                 depthClamp;
    VkBool32                 depthBiasClamp;
    VkBool32                 fillModeNonSolid;
    VkBool32                 depthBounds;
    VkBool32                 wideLines;
    VkBool32                 largePoints;
    VkBool32                 alphaToOne;
    VkBool32                 multiViewport;
    VkBool32                 samplerAnisotropy;
    VkBool32                 textureCompressionETC2;
    VkBool32                 textureCompressionASTC_LDR;
    VkBool32                 textureCompressionBC;
    VkBool32                 occlusionQueryPrecise;
    VkBool32                 pipelineStatisticsQuery;
    VkBool32                 vertexPipelineStoresAndAtomics;
    VkBool32                 fragmentStoresAndAtomics;
    VkBool32                 shaderTessellationAndGeometryPointSize;
    VkBool32                 shaderImageGatherExtended;
    VkBool32                 shaderStorageImageExtendedFormats;
    VkBool32                 shaderStorageImageMultisample;
    VkBool32                 shaderStorageImageReadWithoutFormat;
    VkBool32                 shaderStorageImageWriteWithoutFormat;
    VkBool32                 shaderUniformBufferArrayDynamicIndexing;
    VkBool32                 shaderSampledImageArrayDynamicIndexing;
    VkBool32                 shaderStorageBufferArrayDynamicIndexing;
    VkBool32                 shaderStorageImageArrayDynamicIndexing;
    VkBool32                 shaderClipDistance;
    VkBool32                 shaderCullDistance;
    VkBool32                 shaderFloat64;
    VkBool32                 shaderInt64;
    VkBool32                 shaderInt16;
    VkBool32                 shaderResourceResidency;
    VkBool32                 shaderResourceMinLod;
    VkBool32                 sparseBinding;
    VkBool32                 sparseResidencyBuffer;
    VkBool32                 sparseResidencyImage2D;
    VkBool32                 sparseResidencyImage3D;
    VkBool32                 sparseResidency2Samples;
    VkBool32                 sparseResidency4Samples;
    VkBool32                 sparseResidency8Samples;
    VkBool32                 sparseResidency16Samples;
    VkBool32                 sparseResidencyAliased;
    VkBool32                 variableMultisampleRate;
    VkBool32                 inheritedQueries;
} VkPhysicalDeviceFeatures;
typedef struct VkPhysicalDeviceSparseProperties {
    VkBool32                 residencyStandard2DBlockShape;
    VkBool32                 residencyStandard2DMultisampleBlockShape;
    VkBool32                 residencyStandard3DBlockShape;
    VkBool32                 residencyAlignedMipSize;
    VkBool32                 residencyNonResidentStrict;
} VkPhysicalDeviceSparseProperties;
typedef struct VkPhysicalDeviceLimits {
    uint32_t                 maxImageDimension1D;
    uint32_t                 maxImageDimension2D;
    uint32_t                 maxImageDimension3D;
    uint32_t                 maxImageDimensionCube;
    uint32_t                 maxImageArrayLayers;
    uint32_t                 maxTexelBufferElements;
    uint32_t                 maxUniformBufferRange;
    uint32_t                 maxStorageBufferRange;
    uint32_t                 maxPushConstantsSize;
    uint32_t                 maxMemoryAllocationCount;
    uint32_t                 maxSamplerAllocationCount;
    VkDeviceSize             bufferImageGranularity;
    VkDeviceSize             sparseAddressSpaceSize;
    uint32_t                 maxBoundDescriptorSets;
    uint32_t                 maxPerStageDescriptorSamplers;
    uint32_t                 maxPerStageDescriptorUniformBuffers;
    uint32_t                 maxPerStageDescriptorStorageBuffers;
    uint32_t                 maxPerStageDescriptorSampledImages;
    uint32_t                 maxPerStageDescriptorStorageImages;
    uint32_t                 maxPerStageDescriptorInputAttachments;
    uint32_t                 maxPerStageResources;
    uint32_t                 maxDescriptorSetSamplers;
    uint32_t                 maxDescriptorSetUniformBuffers;
    uint32_t                 maxDescriptorSetUniformBuffersDynamic;
    uint32_t                 maxDescriptorSetStorageBuffers;
    uint32_t                 maxDescriptorSetStorageBuffersDynamic;
    uint32_t                 maxDescriptorSetSampledImages;
    uint32_t                 maxDescriptorSetStorageImages;
    uint32_t                 maxDescriptorSetInputAttachments;
    uint32_t                 maxVertexInputAttributes;
    uint32_t                 maxVertexInputBindings;
    uint32_t                 maxVertexInputAttributeOffset;
    uint32_t                 maxVertexInputBindingStride;
    uint32_t                 maxVertexOutputComponents;
    uint32_t                 maxTessellationGenerationLevel;
    uint32_t                 maxTessellationPatchSize;
    uint32_t                 maxTessellationControlPerVertexInputComponents;
    uint32_t                 maxTessellationControlPerVertexOutputComponents;
    uint32_t                 maxTessellationControlPerPatchOutputComponents;
    uint32_t                 maxTessellationControlTotalOutputComponents;
    uint32_t                 maxTessellationEvaluationInputComponents;
    uint32_t                 maxTessellationEvaluationOutputComponents;
    uint32_t                 maxGeometryShaderInvocations;
    uint32_t                 maxGeometryInputComponents;
    uint32_t                 maxGeometryOutputComponents;
    uint32_t                 maxGeometryOutputVertices;
    uint32_t                 maxGeometryTotalOutputComponents;
    uint32_t                 maxFragmentInputComponents;
    uint32_t                 maxFragmentOutputAttachments;
    uint32_t                 maxFragmentDualSrcAttachments;
    uint32_t                 maxFragmentCombinedOutputResources;
    uint32_t                 maxComputeSharedMemorySize;
    uint32_t                 maxComputeWorkGroupCount [3];
    uint32_t                 maxComputeWorkGroupInvocations;
    uint32_t                 maxComputeWorkGroupSize [3];
    uint32_t                 subPixelPrecisionBits;
    uint32_t                 subTexelPrecisionBits;
    uint32_t                 mipmapPrecisionBits;
    uint32_t                 maxDrawIndexedIndexValue;
    uint32_t                 maxDrawIndirectCount;
    float                    maxSamplerLodBias;
    float                    maxSamplerAnisotropy;
    uint32_t                 maxViewports;
    uint32_t                 maxViewportDimensions [2];
    float                    viewportBoundsRange [2];
    uint32_t                 viewportSubPixelBits;
    size_t                   minMemoryMapAlignment;
    VkDeviceSize             minTexelBufferOffsetAlignment;
    VkDeviceSize             minUniformBufferOffsetAlignment;
    VkDeviceSize             minStorageBufferOffsetAlignment;
    int32_t                  minTexelOffset;
    uint32_t                 maxTexelOffset;
    int32_t                  minTexelGatherOffset;
    uint32_t                 maxTexelGatherOffset;
    float                    minInterpolationOffset;
    float                    maxInterpolationOffset;
    uint32_t                 subPixelInterpolationOffsetBits;
    uint32_t                 maxFramebufferWidth;
    uint32_t                 maxFramebufferHeight;
    uint32_t                 maxFramebufferLayers;
    VkSampleCountFlags       framebufferColorSampleCounts;
    VkSampleCountFlags       framebufferDepthSampleCounts;
    VkSampleCountFlags       framebufferStencilSampleCounts;
    VkSampleCountFlags       framebufferNoAttachmentsSampleCounts;
    uint32_t                 maxColorAttachments;
    VkSampleCountFlags       sampledImageColorSampleCounts;
    VkSampleCountFlags       sampledImageIntegerSampleCounts;
    VkSampleCountFlags       sampledImageDepthSampleCounts;
    VkSampleCountFlags       sampledImageStencilSampleCounts;
    VkSampleCountFlags       storageImageSampleCounts;
    uint32_t                 maxSampleMaskWords;
    VkBool32                 timestampComputeAndGraphics;
    float                    timestampPeriod;
    uint32_t                 maxClipDistances;
    uint32_t                 maxCullDistances;
    uint32_t                 maxCombinedClipAndCullDistances;
    uint32_t                 discreteQueuePriorities;
    float                    pointSizeRange [2];
    float                    lineWidthRange [2];
    float                    pointSizeGranularity;
    float                    lineWidthGranularity;
    VkBool32                 strictLines;
    VkBool32                 standardSampleLocations;
    VkDeviceSize             optimalBufferCopyOffsetAlignment;
    VkDeviceSize             optimalBufferCopyRowPitchAlignment;
    VkDeviceSize             nonCoherentAtomSize;
} VkPhysicalDeviceLimits;
typedef struct VkSemaphoreCreateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkSemaphoreCreateFlags   flags;
} VkSemaphoreCreateInfo;
typedef struct VkQueryPoolCreateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkQueryPoolCreateFlags   flags;
    VkQueryType              queryType;
    uint32_t                 queryCount;
    VkQueryPipelineStatisticFlags   pipelineStatistics;
} VkQueryPoolCreateInfo;
typedef struct VkFramebufferCreateInfo {
    VkStructureType   sType;
    const  void *             pNext;
    VkFramebufferCreateFlags      flags;
    VkRenderPass             renderPass;
    uint32_t                 attachmentCount;
    const  VkImageView *      pAttachments;
    uint32_t                 width;
    uint32_t                 height;
    uint32_t                 layers;
} VkFramebufferCreateInfo;
typedef struct VkSubmitInfo {
    VkStructureType   sType;
    const  void *  pNext;
    uint32_t         waitSemaphoreCount;
    const  VkSemaphore *      pWaitSemaphores;
    const  VkPipelineStageFlags *            pWaitDstStageMask;
    uint32_t         commandBufferCount;
    const  VkCommandBuffer *      pCommandBuffers;
    uint32_t         signalSemaphoreCount;
    const  VkSemaphore *      pSignalSemaphores;
} VkSubmitInfo;
typedef struct VkSurfaceCapabilitiesKHR {
    uint32_t                           minImageCount;
    uint32_t                           maxImageCount;
    VkExtent2D                         currentExtent;
    VkExtent2D                         minImageExtent;
    VkExtent2D                         maxImageExtent;
    uint32_t                           maxImageArrayLayers;
    VkSurfaceTransformFlagsKHR         supportedTransforms;
    VkSurfaceTransformFlagBitsKHR      currentTransform;
    VkCompositeAlphaFlagsKHR           supportedCompositeAlpha;
    VkImageUsageFlags                  supportedUsageFlags;
} VkSurfaceCapabilitiesKHR;
typedef struct VkSwapchainCreateInfoKHR {
    VkStructureType   sType;
    const  void *                       pNext;
    VkSwapchainCreateFlagsKHR          flags;
    VkSurfaceKHR                       surface;
    uint32_t                           minImageCount;
    VkFormat                           imageFormat;
    VkColorSpaceKHR                    imageColorSpace;
    VkExtent2D                         imageExtent;
    uint32_t                           imageArrayLayers;
    VkImageUsageFlags                  imageUsage;
    VkSharingMode                      imageSharingMode;
    uint32_t           queueFamilyIndexCount;
    const  uint32_t *                   pQueueFamilyIndices;
    VkSurfaceTransformFlagBitsKHR      preTransform;
    VkCompositeAlphaFlagBitsKHR        compositeAlpha;
    VkPresentModeKHR                   presentMode;
    VkBool32                           clipped;
    VkSwapchainKHR     oldSwapchain;
} VkSwapchainCreateInfoKHR;
typedef struct VkDebugReportCallbackCreateInfoEXT {
    VkStructureType   sType;
    const  void *                       pNext;
    VkDebugReportFlagsEXT              flags;
    PFN_vkDebugReportCallbackEXT       pfnCallback;
    void *             pUserData;
} VkDebugReportCallbackCreateInfoEXT;
typedef struct VkPhysicalDeviceFeatures2 {
    VkStructureType   sType;
    void *                             pNext;
    VkPhysicalDeviceFeatures           features;
} VkPhysicalDeviceFeatures2;
typedef struct VkFormatProperties2 {
    VkStructureType   sType;
    void *                             pNext;
    VkFormatProperties                 formatProperties;
} VkFormatProperties2;
typedef struct VkImageFormatProperties2 {
    VkStructureType   sType;
    void *  pNext;
    VkImageFormatProperties            imageFormatProperties;
} VkImageFormatProperties2;
typedef struct VkPhysicalDeviceImageFormatInfo2 {
    VkStructureType   sType;
    const  void *  pNext;
    VkFormat                           format;
    VkImageType                        type;
    VkImageTiling                      tiling;
    VkImageUsageFlags                  usage;
    VkImageCreateFlags   flags;
} VkPhysicalDeviceImageFormatInfo2;
typedef struct VkQueueFamilyProperties2 {
    VkStructureType   sType;
    void *                             pNext;
    VkQueueFamilyProperties            queueFamilyProperties;
} VkQueueFamilyProperties2;
typedef struct VkSparseImageFormatProperties2 {
    VkStructureType   sType;
    void *                             pNext;
    VkSparseImageFormatProperties      properties;
} VkSparseImageFormatProperties2;
typedef struct VkPhysicalDeviceSparseImageFormatInfo2 {
    VkStructureType   sType;
    const  void *                       pNext;
    VkFormat                           format;
    VkImageType                        type;
    VkSampleCountFlagBits              samples;
    VkImageUsageFlags                  usage;
    VkImageTiling                      tiling;
} VkPhysicalDeviceSparseImageFormatInfo2;
typedef struct VkPhysicalDeviceVariablePointersFeatures {
    VkStructureType   sType;
    void *                             pNext;
    VkBool32                           variablePointersStorageBuffer;
    VkBool32                           variablePointers;
} VkPhysicalDeviceVariablePointersFeatures;
typedef struct VkPhysicalDeviceVariablePointerFeatures  VkPhysicalDeviceVariablePointerFeatures;
typedef struct VkExternalMemoryProperties {
    VkExternalMemoryFeatureFlags    externalMemoryFeatures;
    VkExternalMemoryHandleTypeFlags   exportFromImportedHandleTypes;
    VkExternalMemoryHandleTypeFlags   compatibleHandleTypes;
} VkExternalMemoryProperties;
typedef struct VkExternalImageFormatProperties {
    VkStructureType   sType;
    void *                             pNext;
    VkExternalMemoryProperties   externalMemoryProperties;
} VkExternalImageFormatProperties;
typedef struct VkPhysicalDeviceExternalBufferInfo {
    VkStructureType   sType;
    const  void *                       pNext;
    VkBufferCreateFlags   flags;
    VkBufferUsageFlags                 usage;
    VkExternalMemoryHandleTypeFlagBits   handleType;
} VkPhysicalDeviceExternalBufferInfo;
typedef struct VkExternalBufferProperties {
    VkStructureType   sType;
    void *                             pNext;
    VkExternalMemoryProperties      externalMemoryProperties;
} VkExternalBufferProperties;
typedef struct VkPhysicalDeviceIDProperties {
    VkStructureType   sType;
    void *                             pNext;
    uint8_t                            deviceUUID [ VK_UUID_SIZE ];
    uint8_t                            driverUUID [ VK_UUID_SIZE ];
    uint8_t                            deviceLUID [ VK_LUID_SIZE ];
    uint32_t                           deviceNodeMask;
    VkBool32                           deviceLUIDValid;
} VkPhysicalDeviceIDProperties;
typedef struct VkExternalMemoryImageCreateInfo {
    VkStructureType   sType;
    const  void *                       pNext;
    VkExternalMemoryHandleTypeFlags   handleTypes;
} VkExternalMemoryImageCreateInfo;
typedef struct VkExternalMemoryBufferCreateInfo {
    VkStructureType   sType;
    const  void *                       pNext;
    VkExternalMemoryHandleTypeFlags   handleTypes;
} VkExternalMemoryBufferCreateInfo;
typedef struct VkExportMemoryAllocateInfo {
    VkStructureType   sType;
    const  void *                       pNext;
    VkExternalMemoryHandleTypeFlags   handleTypes;
} VkExportMemoryAllocateInfo;
typedef struct VkExternalSemaphoreProperties {
    VkStructureType   sType;
    void *                             pNext;
    VkExternalSemaphoreHandleTypeFlags   exportFromImportedHandleTypes;
    VkExternalSemaphoreHandleTypeFlags   compatibleHandleTypes;
    VkExternalSemaphoreFeatureFlags   externalSemaphoreFeatures;
} VkExternalSemaphoreProperties;
typedef struct VkExportSemaphoreCreateInfo {
    VkStructureType   sType;
    const  void *                       pNext;
    VkExternalSemaphoreHandleTypeFlags   handleTypes;
} VkExportSemaphoreCreateInfo;
typedef struct VkExternalFenceProperties {
    VkStructureType   sType;
    void *                             pNext;
    VkExternalFenceHandleTypeFlags   exportFromImportedHandleTypes;
    VkExternalFenceHandleTypeFlags   compatibleHandleTypes;
    VkExternalFenceFeatureFlags   externalFenceFeatures;
} VkExternalFenceProperties;
typedef struct VkExportFenceCreateInfo {
    VkStructureType   sType;
    const  void *                       pNext;
    VkExternalFenceHandleTypeFlags   handleTypes;
} VkExportFenceCreateInfo;
typedef struct VkPhysicalDeviceMultiviewFeatures {
    VkStructureType   sType;
    void *                             pNext;
    VkBool32                           multiview;
    VkBool32                           multiviewGeometryShader;
    VkBool32                           multiviewTessellationShader;
} VkPhysicalDeviceMultiviewFeatures;
typedef struct VkPhysicalDeviceGroupProperties {
    VkStructureType   sType;
    void *                             pNext;
    uint32_t                           physicalDeviceCount;
    VkPhysicalDevice                   physicalDevices [ VK_MAX_DEVICE_GROUP_SIZE ];
    VkBool32                           subsetAllocation;
} VkPhysicalDeviceGroupProperties;
typedef struct VkMemoryAllocateFlagsInfo {
    VkStructureType   sType;
    const  void *                       pNext;
    VkMemoryAllocateFlags   flags;
    uint32_t                           deviceMask;
} VkMemoryAllocateFlagsInfo;
typedef struct VkBindBufferMemoryInfo {
    VkStructureType   sType;
    const  void *                       pNext;
    VkBuffer                           buffer;
    VkDeviceMemory                     memory;
    VkDeviceSize                       memoryOffset;
} VkBindBufferMemoryInfo;
typedef struct VkBindImageMemoryInfo {
    VkStructureType   sType;
    const  void *                       pNext;
    VkImage                            image;
    VkDeviceMemory                     memory;
    VkDeviceSize                       memoryOffset;
} VkBindImageMemoryInfo;
typedef struct VkDeviceGroupPresentCapabilitiesKHR {
    VkStructureType   sType;
    const  void *                       pNext;
    uint32_t                           presentMask [ VK_MAX_DEVICE_GROUP_SIZE ];
    VkDeviceGroupPresentModeFlagsKHR   modes;
} VkDeviceGroupPresentCapabilitiesKHR;
typedef struct VkDeviceGroupSwapchainCreateInfoKHR {
    VkStructureType   sType;
    const  void *                       pNext;
    VkDeviceGroupPresentModeFlagsKHR                           modes;
} VkDeviceGroupSwapchainCreateInfoKHR;
typedef struct VkDescriptorUpdateTemplateCreateInfo {
    VkStructureType   sType;
    const  void *                                pNext;
    VkDescriptorUpdateTemplateCreateFlags      flags;
    uint32_t                   descriptorUpdateEntryCount;
    const  VkDescriptorUpdateTemplateEntry *  pDescriptorUpdateEntries;
    VkDescriptorUpdateTemplateType   templateType;
    VkDescriptorSetLayout   descriptorSetLayout;
    VkPipelineBindPoint   pipelineBindPoint;
    VkPipelineLayout pipelineLayout;
    uint32_t   set;
} VkDescriptorUpdateTemplateCreateInfo;
typedef struct VkInputAttachmentAspectReference {
    uint32_t                          subpass;
    uint32_t                          inputAttachmentIndex;
    VkImageAspectFlags                aspectMask;
} VkInputAttachmentAspectReference;
typedef struct VkRenderPassInputAttachmentAspectCreateInfo {
    VkStructureType   sType;
    const  void *                      pNext;
    uint32_t                          aspectReferenceCount;
    const  VkInputAttachmentAspectReference *  pAspectReferences;
} VkRenderPassInputAttachmentAspectCreateInfo;
typedef struct VkPhysicalDevice16BitStorageFeatures {
    VkStructureType   sType;
    void *       pNext;
    VkBool32                           storageBuffer16BitAccess;
    VkBool32                           uniformAndStorageBuffer16BitAccess;
    VkBool32                           storagePushConstant16;
    VkBool32                           storageInputOutput16;
} VkPhysicalDevice16BitStorageFeatures;
typedef struct VkPhysicalDeviceSubgroupProperties {
    VkStructureType   sType;
    void *                    pNext;
    uint32_t                        subgroupSize;
    VkShaderStageFlags              supportedStages;
    VkSubgroupFeatureFlags          supportedOperations;
    VkBool32   quadOperationsInAllStages;
} VkPhysicalDeviceSubgroupProperties;
typedef struct VkMemoryRequirements2 {
    VkStructureType   sType;
    void *  pNext;
    VkMemoryRequirements                                                   memoryRequirements;
} VkMemoryRequirements2;
typedef struct VkMemoryRequirements2KHR  VkMemoryRequirements2KHR;
typedef struct VkSparseImageMemoryRequirements2 {
    VkStructureType   sType;
    void *                                        pNext;
    VkSparseImageMemoryRequirements                                        memoryRequirements;
} VkSparseImageMemoryRequirements2;
typedef struct VkMemoryDedicatedRequirements {
    VkStructureType   sType;
    void *                             pNext;
    VkBool32                           prefersDedicatedAllocation;
    VkBool32                           requiresDedicatedAllocation;
} VkMemoryDedicatedRequirements;
typedef struct VkImageViewUsageCreateInfo {
    VkStructureType   sType;
    const  void *  pNext;
    VkImageUsageFlags   usage;
} VkImageViewUsageCreateInfo;
typedef struct VkSamplerYcbcrConversionCreateInfo {
    VkStructureType   sType;
    const  void *                       pNext;
    VkFormat                           format;
    VkSamplerYcbcrModelConversion   ycbcrModel;
    VkSamplerYcbcrRange             ycbcrRange;
    VkComponentMapping                 components;
    VkChromaLocation                xChromaOffset;
    VkChromaLocation                yChromaOffset;
    VkFilter                           chromaFilter;
    VkBool32                           forceExplicitReconstruction;
} VkSamplerYcbcrConversionCreateInfo;
typedef struct VkPhysicalDeviceSamplerYcbcrConversionFeatures {
    VkStructureType   sType;
    void *       pNext;
    VkBool32                           samplerYcbcrConversion;
} VkPhysicalDeviceSamplerYcbcrConversionFeatures;
typedef struct VkProtectedSubmitInfo {
    VkStructureType   sType;
    const  void *                      pNext;
    VkBool32                          protectedSubmit;
} VkProtectedSubmitInfo;
typedef struct VkPhysicalDeviceProtectedMemoryFeatures {
    VkStructureType   sType;
    void *                                pNext;
    VkBool32                              protectedMemory;
} VkPhysicalDeviceProtectedMemoryFeatures;
typedef struct VkPhysicalDeviceProtectedMemoryProperties {
    VkStructureType   sType;
    void *                                pNext;
    VkBool32                              protectedNoFault;
} VkPhysicalDeviceProtectedMemoryProperties;
typedef struct VkDeviceQueueInfo2 {
    VkStructureType   sType;
    const  void *                          pNext;
    VkDeviceQueueCreateFlags              flags;
    uint32_t                              queueFamilyIndex;
    uint32_t                              queueIndex;
} VkDeviceQueueInfo2;
typedef struct VkPhysicalDeviceMaintenance3Properties {
    VkStructureType   sType;
    void *                             pNext;
    uint32_t                           maxPerSetDescriptors;
    VkDeviceSize                       maxMemoryAllocationSize;
} VkPhysicalDeviceMaintenance3Properties;
typedef struct VkDescriptorSetLayoutSupport {
    VkStructureType   sType;
    void *             pNext;
    VkBool32           supported;
} VkDescriptorSetLayoutSupport;
typedef struct VkPhysicalDeviceShaderDrawParametersFeatures {
    VkStructureType   sType;
    void *                             pNext;
    VkBool32                           shaderDrawParameters;
} VkPhysicalDeviceShaderDrawParametersFeatures;
typedef struct VkPhysicalDeviceShaderDrawParameterFeatures  VkPhysicalDeviceShaderDrawParameterFeatures;
typedef struct VkPhysicalDeviceProperties {
    uint32_t         apiVersion;
    uint32_t         driverVersion;
    uint32_t         vendorID;
    uint32_t         deviceID;
    VkPhysicalDeviceType   deviceType;
    char             deviceName [ VK_MAX_PHYSICAL_DEVICE_NAME_SIZE ];
    uint8_t          pipelineCacheUUID [ VK_UUID_SIZE ];
    VkPhysicalDeviceLimits   limits;
    VkPhysicalDeviceSparseProperties   sparseProperties;
} VkPhysicalDeviceProperties;
typedef struct VkDeviceCreateInfo {
    VkStructureType   sType;
    const  void *      pNext;
    VkDeviceCreateFlags      flags;
    uint32_t          queueCreateInfoCount;
    const  VkDeviceQueueCreateInfo *  pQueueCreateInfos;
    uint32_t                 enabledLayerCount;
    const  char * const*       ppEnabledLayerNames;
    uint32_t                 enabledExtensionCount;
    const  char * const*       ppEnabledExtensionNames;
    const  VkPhysicalDeviceFeatures *  pEnabledFeatures;
} VkDeviceCreateInfo;
typedef struct VkPhysicalDeviceMemoryProperties {
    uint32_t                 memoryTypeCount;
    VkMemoryType             memoryTypes [ VK_MAX_MEMORY_TYPES ];
    uint32_t                 memoryHeapCount;
    VkMemoryHeap             memoryHeaps [ VK_MAX_MEMORY_HEAPS ];
} VkPhysicalDeviceMemoryProperties;
typedef struct VkPhysicalDeviceProperties2 {
    VkStructureType   sType;
    void *                             pNext;
    VkPhysicalDeviceProperties         properties;
} VkPhysicalDeviceProperties2;
typedef struct VkPhysicalDeviceMemoryProperties2 {
    VkStructureType   sType;
    void *                             pNext;
    VkPhysicalDeviceMemoryProperties   memoryProperties;
} VkPhysicalDeviceMemoryProperties2;


#define VK_VERSION_1_0 1
GLAD_API_CALL int GLAD_VK_VERSION_1_0;
#define VK_VERSION_1_1 1
GLAD_API_CALL int GLAD_VK_VERSION_1_1;
#define VK_EXT_debug_report 1
GLAD_API_CALL int GLAD_VK_EXT_debug_report;
#define VK_KHR_surface 1
GLAD_API_CALL int GLAD_VK_KHR_surface;
#define VK_KHR_swapchain 1
GLAD_API_CALL int GLAD_VK_KHR_swapchain;


typedef VkResult (GLAD_API_PTR *PFN_vkAcquireNextImage2KHR)(VkDevice   device, const  VkAcquireNextImageInfoKHR *  pAcquireInfo, uint32_t *  pImageIndex);
typedef VkResult (GLAD_API_PTR *PFN_vkAcquireNextImageKHR)(VkDevice   device, VkSwapchainKHR   swapchain, uint64_t   timeout, VkSemaphore   semaphore, VkFence   fence, uint32_t *  pImageIndex);
typedef VkResult (GLAD_API_PTR *PFN_vkAllocateCommandBuffers)(VkDevice   device, const  VkCommandBufferAllocateInfo *  pAllocateInfo, VkCommandBuffer *  pCommandBuffers);
typedef VkResult (GLAD_API_PTR *PFN_vkAllocateDescriptorSets)(VkDevice   device, const  VkDescriptorSetAllocateInfo *  pAllocateInfo, VkDescriptorSet *  pDescriptorSets);
typedef VkResult (GLAD_API_PTR *PFN_vkAllocateMemory)(VkDevice   device, const  VkMemoryAllocateInfo *  pAllocateInfo, const  VkAllocationCallbacks *  pAllocator, VkDeviceMemory *  pMemory);
typedef VkResult (GLAD_API_PTR *PFN_vkBeginCommandBuffer)(VkCommandBuffer   commandBuffer, const  VkCommandBufferBeginInfo *  pBeginInfo);
typedef VkResult (GLAD_API_PTR *PFN_vkBindBufferMemory)(VkDevice   device, VkBuffer   buffer, VkDeviceMemory   memory, VkDeviceSize   memoryOffset);
typedef VkResult (GLAD_API_PTR *PFN_vkBindBufferMemory2)(VkDevice   device, uint32_t   bindInfoCount, const  VkBindBufferMemoryInfo *  pBindInfos);
typedef VkResult (GLAD_API_PTR *PFN_vkBindImageMemory)(VkDevice   device, VkImage   image, VkDeviceMemory   memory, VkDeviceSize   memoryOffset);
typedef VkResult (GLAD_API_PTR *PFN_vkBindImageMemory2)(VkDevice   device, uint32_t   bindInfoCount, const  VkBindImageMemoryInfo *  pBindInfos);
typedef void (GLAD_API_PTR *PFN_vkCmdBeginQuery)(VkCommandBuffer   commandBuffer, VkQueryPool   queryPool, uint32_t   query, VkQueryControlFlags   flags);
typedef void (GLAD_API_PTR *PFN_vkCmdBeginRenderPass)(VkCommandBuffer   commandBuffer, const  VkRenderPassBeginInfo *  pRenderPassBegin, VkSubpassContents   contents);
typedef void (GLAD_API_PTR *PFN_vkCmdBindDescriptorSets)(VkCommandBuffer   commandBuffer, VkPipelineBindPoint   pipelineBindPoint, VkPipelineLayout   layout, uint32_t   firstSet, uint32_t   descriptorSetCount, const  VkDescriptorSet *  pDescriptorSets, uint32_t   dynamicOffsetCount, const  uint32_t *  pDynamicOffsets);
typedef void (GLAD_API_PTR *PFN_vkCmdBindIndexBuffer)(VkCommandBuffer   commandBuffer, VkBuffer   buffer, VkDeviceSize   offset, VkIndexType   indexType);
typedef void (GLAD_API_PTR *PFN_vkCmdBindPipeline)(VkCommandBuffer   commandBuffer, VkPipelineBindPoint   pipelineBindPoint, VkPipeline   pipeline);
typedef void (GLAD_API_PTR *PFN_vkCmdBindVertexBuffers)(VkCommandBuffer   commandBuffer, uint32_t   firstBinding, uint32_t   bindingCount, const  VkBuffer *  pBuffers, const  VkDeviceSize *  pOffsets);
typedef void (GLAD_API_PTR *PFN_vkCmdBlitImage)(VkCommandBuffer   commandBuffer, VkImage   srcImage, VkImageLayout   srcImageLayout, VkImage   dstImage, VkImageLayout   dstImageLayout, uint32_t   regionCount, const  VkImageBlit *  pRegions, VkFilter   filter);
typedef void (GLAD_API_PTR *PFN_vkCmdClearAttachments)(VkCommandBuffer   commandBuffer, uint32_t   attachmentCount, const  VkClearAttachment *  pAttachments, uint32_t   rectCount, const  VkClearRect *  pRects);
typedef void (GLAD_API_PTR *PFN_vkCmdClearColorImage)(VkCommandBuffer   commandBuffer, VkImage   image, VkImageLayout   imageLayout, const  VkClearColorValue *  pColor, uint32_t   rangeCount, const  VkImageSubresourceRange *  pRanges);
typedef void (GLAD_API_PTR *PFN_vkCmdClearDepthStencilImage)(VkCommandBuffer   commandBuffer, VkImage   image, VkImageLayout   imageLayout, const  VkClearDepthStencilValue *  pDepthStencil, uint32_t   rangeCount, const  VkImageSubresourceRange *  pRanges);
typedef void (GLAD_API_PTR *PFN_vkCmdCopyBuffer)(VkCommandBuffer   commandBuffer, VkBuffer   srcBuffer, VkBuffer   dstBuffer, uint32_t   regionCount, const  VkBufferCopy *  pRegions);
typedef void (GLAD_API_PTR *PFN_vkCmdCopyBufferToImage)(VkCommandBuffer   commandBuffer, VkBuffer   srcBuffer, VkImage   dstImage, VkImageLayout   dstImageLayout, uint32_t   regionCount, const  VkBufferImageCopy *  pRegions);
typedef void (GLAD_API_PTR *PFN_vkCmdCopyImage)(VkCommandBuffer   commandBuffer, VkImage   srcImage, VkImageLayout   srcImageLayout, VkImage   dstImage, VkImageLayout   dstImageLayout, uint32_t   regionCount, const  VkImageCopy *  pRegions);
typedef void (GLAD_API_PTR *PFN_vkCmdCopyImageToBuffer)(VkCommandBuffer   commandBuffer, VkImage   srcImage, VkImageLayout   srcImageLayout, VkBuffer   dstBuffer, uint32_t   regionCount, const  VkBufferImageCopy *  pRegions);
typedef void (GLAD_API_PTR *PFN_vkCmdCopyQueryPoolResults)(VkCommandBuffer   commandBuffer, VkQueryPool   queryPool, uint32_t   firstQuery, uint32_t   queryCount, VkBuffer   dstBuffer, VkDeviceSize   dstOffset, VkDeviceSize   stride, VkQueryResultFlags   flags);
typedef void (GLAD_API_PTR *PFN_vkCmdDispatch)(VkCommandBuffer   commandBuffer, uint32_t   groupCountX, uint32_t   groupCountY, uint32_t   groupCountZ);
typedef void (GLAD_API_PTR *PFN_vkCmdDispatchBase)(VkCommandBuffer   commandBuffer, uint32_t   baseGroupX, uint32_t   baseGroupY, uint32_t   baseGroupZ, uint32_t   groupCountX, uint32_t   groupCountY, uint32_t   groupCountZ);
typedef void (GLAD_API_PTR *PFN_vkCmdDispatchIndirect)(VkCommandBuffer   commandBuffer, VkBuffer   buffer, VkDeviceSize   offset);
typedef void (GLAD_API_PTR *PFN_vkCmdDraw)(VkCommandBuffer   commandBuffer, uint32_t   vertexCount, uint32_t   instanceCount, uint32_t   firstVertex, uint32_t   firstInstance);
typedef void (GLAD_API_PTR *PFN_vkCmdDrawIndexed)(VkCommandBuffer   commandBuffer, uint32_t   indexCount, uint32_t   instanceCount, uint32_t   firstIndex, int32_t   vertexOffset, uint32_t   firstInstance);
typedef void (GLAD_API_PTR *PFN_vkCmdDrawIndexedIndirect)(VkCommandBuffer   commandBuffer, VkBuffer   buffer, VkDeviceSize   offset, uint32_t   drawCount, uint32_t   stride);
typedef void (GLAD_API_PTR *PFN_vkCmdDrawIndirect)(VkCommandBuffer   commandBuffer, VkBuffer   buffer, VkDeviceSize   offset, uint32_t   drawCount, uint32_t   stride);
typedef void (GLAD_API_PTR *PFN_vkCmdEndQuery)(VkCommandBuffer   commandBuffer, VkQueryPool   queryPool, uint32_t   query);
typedef void (GLAD_API_PTR *PFN_vkCmdEndRenderPass)(VkCommandBuffer   commandBuffer);
typedef void (GLAD_API_PTR *PFN_vkCmdExecuteCommands)(VkCommandBuffer   commandBuffer, uint32_t   commandBufferCount, const  VkCommandBuffer *  pCommandBuffers);
typedef void (GLAD_API_PTR *PFN_vkCmdFillBuffer)(VkCommandBuffer   commandBuffer, VkBuffer   dstBuffer, VkDeviceSize   dstOffset, VkDeviceSize   size, uint32_t   data);
typedef void (GLAD_API_PTR *PFN_vkCmdNextSubpass)(VkCommandBuffer   commandBuffer, VkSubpassContents   contents);
typedef void (GLAD_API_PTR *PFN_vkCmdPipelineBarrier)(VkCommandBuffer   commandBuffer, VkPipelineStageFlags   srcStageMask, VkPipelineStageFlags   dstStageMask, VkDependencyFlags   dependencyFlags, uint32_t   memoryBarrierCount, const  VkMemoryBarrier *  pMemoryBarriers, uint32_t   bufferMemoryBarrierCount, const  VkBufferMemoryBarrier *  pBufferMemoryBarriers, uint32_t   imageMemoryBarrierCount, const  VkImageMemoryBarrier *  pImageMemoryBarriers);
typedef void (GLAD_API_PTR *PFN_vkCmdPushConstants)(VkCommandBuffer   commandBuffer, VkPipelineLayout   layout, VkShaderStageFlags   stageFlags, uint32_t   offset, uint32_t   size, const  void *  pValues);
typedef void (GLAD_API_PTR *PFN_vkCmdResetEvent)(VkCommandBuffer   commandBuffer, VkEvent   event, VkPipelineStageFlags   stageMask);
typedef void (GLAD_API_PTR *PFN_vkCmdResetQueryPool)(VkCommandBuffer   commandBuffer, VkQueryPool   queryPool, uint32_t   firstQuery, uint32_t   queryCount);
typedef void (GLAD_API_PTR *PFN_vkCmdResolveImage)(VkCommandBuffer   commandBuffer, VkImage   srcImage, VkImageLayout   srcImageLayout, VkImage   dstImage, VkImageLayout   dstImageLayout, uint32_t   regionCount, const  VkImageResolve *  pRegions);
typedef void (GLAD_API_PTR *PFN_vkCmdSetBlendConstants)(VkCommandBuffer   commandBuffer, const  float   blendConstants [4]);
typedef void (GLAD_API_PTR *PFN_vkCmdSetDepthBias)(VkCommandBuffer   commandBuffer, float   depthBiasConstantFactor, float   depthBiasClamp, float   depthBiasSlopeFactor);
typedef void (GLAD_API_PTR *PFN_vkCmdSetDepthBounds)(VkCommandBuffer   commandBuffer, float   minDepthBounds, float   maxDepthBounds);
typedef void (GLAD_API_PTR *PFN_vkCmdSetDeviceMask)(VkCommandBuffer   commandBuffer, uint32_t   deviceMask);
typedef void (GLAD_API_PTR *PFN_vkCmdSetEvent)(VkCommandBuffer   commandBuffer, VkEvent   event, VkPipelineStageFlags   stageMask);
typedef void (GLAD_API_PTR *PFN_vkCmdSetLineWidth)(VkCommandBuffer   commandBuffer, float   lineWidth);
typedef void (GLAD_API_PTR *PFN_vkCmdSetScissor)(VkCommandBuffer   commandBuffer, uint32_t   firstScissor, uint32_t   scissorCount, const  VkRect2D *  pScissors);
typedef void (GLAD_API_PTR *PFN_vkCmdSetStencilCompareMask)(VkCommandBuffer   commandBuffer, VkStencilFaceFlags   faceMask, uint32_t   compareMask);
typedef void (GLAD_API_PTR *PFN_vkCmdSetStencilReference)(VkCommandBuffer   commandBuffer, VkStencilFaceFlags   faceMask, uint32_t   reference);
typedef void (GLAD_API_PTR *PFN_vkCmdSetStencilWriteMask)(VkCommandBuffer   commandBuffer, VkStencilFaceFlags   faceMask, uint32_t   writeMask);
typedef void (GLAD_API_PTR *PFN_vkCmdSetViewport)(VkCommandBuffer   commandBuffer, uint32_t   firstViewport, uint32_t   viewportCount, const  VkViewport *  pViewports);
typedef void (GLAD_API_PTR *PFN_vkCmdUpdateBuffer)(VkCommandBuffer   commandBuffer, VkBuffer   dstBuffer, VkDeviceSize   dstOffset, VkDeviceSize   dataSize, const  void *  pData);
typedef void (GLAD_API_PTR *PFN_vkCmdWaitEvents)(VkCommandBuffer   commandBuffer, uint32_t   eventCount, const  VkEvent *  pEvents, VkPipelineStageFlags   srcStageMask, VkPipelineStageFlags   dstStageMask, uint32_t   memoryBarrierCount, const  VkMemoryBarrier *  pMemoryBarriers, uint32_t   bufferMemoryBarrierCount, const  VkBufferMemoryBarrier *  pBufferMemoryBarriers, uint32_t   imageMemoryBarrierCount, const  VkImageMemoryBarrier *  pImageMemoryBarriers);
typedef void (GLAD_API_PTR *PFN_vkCmdWriteTimestamp)(VkCommandBuffer   commandBuffer, VkPipelineStageFlagBits   pipelineStage, VkQueryPool   queryPool, uint32_t   query);
typedef VkResult (GLAD_API_PTR *PFN_vkCreateBuffer)(VkDevice   device, const  VkBufferCreateInfo *  pCreateInfo, const  VkAllocationCallbacks *  pAllocator, VkBuffer *  pBuffer);
typedef VkResult (GLAD_API_PTR *PFN_vkCreateBufferView)(VkDevice   device, const  VkBufferViewCreateInfo *  pCreateInfo, const  VkAllocationCallbacks *  pAllocator, VkBufferView *  pView);
typedef VkResult (GLAD_API_PTR *PFN_vkCreateCommandPool)(VkDevice   device, const  VkCommandPoolCreateInfo *  pCreateInfo, const  VkAllocationCallbacks *  pAllocator, VkCommandPool *  pCommandPool);
typedef VkResult (GLAD_API_PTR *PFN_vkCreateComputePipelines)(VkDevice   device, VkPipelineCache   pipelineCache, uint32_t   createInfoCount, const  VkComputePipelineCreateInfo *  pCreateInfos, const  VkAllocationCallbacks *  pAllocator, VkPipeline *  pPipelines);
typedef VkResult (GLAD_API_PTR *PFN_vkCreateDebugReportCallbackEXT)(VkInstance   instance, const  VkDebugReportCallbackCreateInfoEXT *  pCreateInfo, const  VkAllocationCallbacks *  pAllocator, VkDebugReportCallbackEXT *  pCallback);
typedef VkResult (GLAD_API_PTR *PFN_vkCreateDescriptorPool)(VkDevice   device, const  VkDescriptorPoolCreateInfo *  pCreateInfo, const  VkAllocationCallbacks *  pAllocator, VkDescriptorPool *  pDescriptorPool);
typedef VkResult (GLAD_API_PTR *PFN_vkCreateDescriptorSetLayout)(VkDevice   device, const  VkDescriptorSetLayoutCreateInfo *  pCreateInfo, const  VkAllocationCallbacks *  pAllocator, VkDescriptorSetLayout *  pSetLayout);
typedef VkResult (GLAD_API_PTR *PFN_vkCreateDescriptorUpdateTemplate)(VkDevice   device, const  VkDescriptorUpdateTemplateCreateInfo *  pCreateInfo, const  VkAllocationCallbacks *  pAllocator, VkDescriptorUpdateTemplate *  pDescriptorUpdateTemplate);
typedef VkResult (GLAD_API_PTR *PFN_vkCreateDevice)(VkPhysicalDevice   physicalDevice, const  VkDeviceCreateInfo *  pCreateInfo, const  VkAllocationCallbacks *  pAllocator, VkDevice *  pDevice);
typedef VkResult (GLAD_API_PTR *PFN_vkCreateEvent)(VkDevice   device, const  VkEventCreateInfo *  pCreateInfo, const  VkAllocationCallbacks *  pAllocator, VkEvent *  pEvent);
typedef VkResult (GLAD_API_PTR *PFN_vkCreateFence)(VkDevice   device, const  VkFenceCreateInfo *  pCreateInfo, const  VkAllocationCallbacks *  pAllocator, VkFence *  pFence);
typedef VkResult (GLAD_API_PTR *PFN_vkCreateFramebuffer)(VkDevice   device, const  VkFramebufferCreateInfo *  pCreateInfo, const  VkAllocationCallbacks *  pAllocator, VkFramebuffer *  pFramebuffer);
typedef VkResult (GLAD_API_PTR *PFN_vkCreateGraphicsPipelines)(VkDevice   device, VkPipelineCache   pipelineCache, uint32_t   createInfoCount, const  VkGraphicsPipelineCreateInfo *  pCreateInfos, const  VkAllocationCallbacks *  pAllocator, VkPipeline *  pPipelines);
typedef VkResult (GLAD_API_PTR *PFN_vkCreateImage)(VkDevice   device, const  VkImageCreateInfo *  pCreateInfo, const  VkAllocationCallbacks *  pAllocator, VkImage *  pImage);
typedef VkResult (GLAD_API_PTR *PFN_vkCreateImageView)(VkDevice   device, const  VkImageViewCreateInfo *  pCreateInfo, const  VkAllocationCallbacks *  pAllocator, VkImageView *  pView);
typedef VkResult (GLAD_API_PTR *PFN_vkCreateInstance)(const  VkInstanceCreateInfo *  pCreateInfo, const  VkAllocationCallbacks *  pAllocator, VkInstance *  pInstance);
typedef VkResult (GLAD_API_PTR *PFN_vkCreatePipelineCache)(VkDevice   device, const  VkPipelineCacheCreateInfo *  pCreateInfo, const  VkAllocationCallbacks *  pAllocator, VkPipelineCache *  pPipelineCache);
typedef VkResult (GLAD_API_PTR *PFN_vkCreatePipelineLayout)(VkDevice   device, const  VkPipelineLayoutCreateInfo *  pCreateInfo, const  VkAllocationCallbacks *  pAllocator, VkPipelineLayout *  pPipelineLayout);
typedef VkResult (GLAD_API_PTR *PFN_vkCreateQueryPool)(VkDevice   device, const  VkQueryPoolCreateInfo *  pCreateInfo, const  VkAllocationCallbacks *  pAllocator, VkQueryPool *  pQueryPool);
typedef VkResult (GLAD_API_PTR *PFN_vkCreateRenderPass)(VkDevice   device, const  VkRenderPassCreateInfo *  pCreateInfo, const  VkAllocationCallbacks *  pAllocator, VkRenderPass *  pRenderPass);
typedef VkResult (GLAD_API_PTR *PFN_vkCreateSampler)(VkDevice   device, const  VkSamplerCreateInfo *  pCreateInfo, const  VkAllocationCallbacks *  pAllocator, VkSampler *  pSampler);
typedef VkResult (GLAD_API_PTR *PFN_vkCreateSamplerYcbcrConversion)(VkDevice   device, const  VkSamplerYcbcrConversionCreateInfo *  pCreateInfo, const  VkAllocationCallbacks *  pAllocator, VkSamplerYcbcrConversion *  pYcbcrConversion);
typedef VkResult (GLAD_API_PTR *PFN_vkCreateSemaphore)(VkDevice   device, const  VkSemaphoreCreateInfo *  pCreateInfo, const  VkAllocationCallbacks *  pAllocator, VkSemaphore *  pSemaphore);
typedef VkResult (GLAD_API_PTR *PFN_vkCreateShaderModule)(VkDevice   device, const  VkShaderModuleCreateInfo *  pCreateInfo, const  VkAllocationCallbacks *  pAllocator, VkShaderModule *  pShaderModule);
typedef VkResult (GLAD_API_PTR *PFN_vkCreateSwapchainKHR)(VkDevice   device, const  VkSwapchainCreateInfoKHR *  pCreateInfo, const  VkAllocationCallbacks *  pAllocator, VkSwapchainKHR *  pSwapchain);
typedef void (GLAD_API_PTR *PFN_vkDebugReportMessageEXT)(VkInstance   instance, VkDebugReportFlagsEXT   flags, VkDebugReportObjectTypeEXT   objectType, uint64_t   object, size_t   location, int32_t   messageCode, const  char *  pLayerPrefix, const  char *  pMessage);
typedef void (GLAD_API_PTR *PFN_vkDestroyBuffer)(VkDevice   device, VkBuffer   buffer, const  VkAllocationCallbacks *  pAllocator);
typedef void (GLAD_API_PTR *PFN_vkDestroyBufferView)(VkDevice   device, VkBufferView   bufferView, const  VkAllocationCallbacks *  pAllocator);
typedef void (GLAD_API_PTR *PFN_vkDestroyCommandPool)(VkDevice   device, VkCommandPool   commandPool, const  VkAllocationCallbacks *  pAllocator);
typedef void (GLAD_API_PTR *PFN_vkDestroyDebugReportCallbackEXT)(VkInstance   instance, VkDebugReportCallbackEXT   callback, const  VkAllocationCallbacks *  pAllocator);
typedef void (GLAD_API_PTR *PFN_vkDestroyDescriptorPool)(VkDevice   device, VkDescriptorPool   descriptorPool, const  VkAllocationCallbacks *  pAllocator);
typedef void (GLAD_API_PTR *PFN_vkDestroyDescriptorSetLayout)(VkDevice   device, VkDescriptorSetLayout   descriptorSetLayout, const  VkAllocationCallbacks *  pAllocator);
typedef void (GLAD_API_PTR *PFN_vkDestroyDescriptorUpdateTemplate)(VkDevice   device, VkDescriptorUpdateTemplate   descriptorUpdateTemplate, const  VkAllocationCallbacks *  pAllocator);
typedef void (GLAD_API_PTR *PFN_vkDestroyDevice)(VkDevice   device, const  VkAllocationCallbacks *  pAllocator);
typedef void (GLAD_API_PTR *PFN_vkDestroyEvent)(VkDevice   device, VkEvent   event, const  VkAllocationCallbacks *  pAllocator);
typedef void (GLAD_API_PTR *PFN_vkDestroyFence)(VkDevice   device, VkFence   fence, const  VkAllocationCallbacks *  pAllocator);
typedef void (GLAD_API_PTR *PFN_vkDestroyFramebuffer)(VkDevice   device, VkFramebuffer   framebuffer, const  VkAllocationCallbacks *  pAllocator);
typedef void (GLAD_API_PTR *PFN_vkDestroyImage)(VkDevice   device, VkImage   image, const  VkAllocationCallbacks *  pAllocator);
typedef void (GLAD_API_PTR *PFN_vkDestroyImageView)(VkDevice   device, VkImageView   imageView, const  VkAllocationCallbacks *  pAllocator);
typedef void (GLAD_API_PTR *PFN_vkDestroyInstance)(VkInstance   instance, const  VkAllocationCallbacks *  pAllocator);
typedef void (GLAD_API_PTR *PFN_vkDestroyPipeline)(VkDevice   device, VkPipeline   pipeline, const  VkAllocationCallbacks *  pAllocator);
typedef void (GLAD_API_PTR *PFN_vkDestroyPipelineCache)(VkDevice   device, VkPipelineCache   pipelineCache, const  VkAllocationCallbacks *  pAllocator);
typedef void (GLAD_API_PTR *PFN_vkDestroyPipelineLayout)(VkDevice   device, VkPipelineLayout   pipelineLayout, const  VkAllocationCallbacks *  pAllocator);
typedef void (GLAD_API_PTR *PFN_vkDestroyQueryPool)(VkDevice   device, VkQueryPool   queryPool, const  VkAllocationCallbacks *  pAllocator);
typedef void (GLAD_API_PTR *PFN_vkDestroyRenderPass)(VkDevice   device, VkRenderPass   renderPass, const  VkAllocationCallbacks *  pAllocator);
typedef void (GLAD_API_PTR *PFN_vkDestroySampler)(VkDevice   device, VkSampler   sampler, const  VkAllocationCallbacks *  pAllocator);
typedef void (GLAD_API_PTR *PFN_vkDestroySamplerYcbcrConversion)(VkDevice   device, VkSamplerYcbcrConversion   ycbcrConversion, const  VkAllocationCallbacks *  pAllocator);
typedef void (GLAD_API_PTR *PFN_vkDestroySemaphore)(VkDevice   device, VkSemaphore   semaphore, const  VkAllocationCallbacks *  pAllocator);
typedef void (GLAD_API_PTR *PFN_vkDestroyShaderModule)(VkDevice   device, VkShaderModule   shaderModule, const  VkAllocationCallbacks *  pAllocator);
typedef void (GLAD_API_PTR *PFN_vkDestroySurfaceKHR)(VkInstance   instance, VkSurfaceKHR   surface, const  VkAllocationCallbacks *  pAllocator);
typedef void (GLAD_API_PTR *PFN_vkDestroySwapchainKHR)(VkDevice   device, VkSwapchainKHR   swapchain, const  VkAllocationCallbacks *  pAllocator);
typedef VkResult (GLAD_API_PTR *PFN_vkDeviceWaitIdle)(VkDevice   device);
typedef VkResult (GLAD_API_PTR *PFN_vkEndCommandBuffer)(VkCommandBuffer   commandBuffer);
typedef VkResult (GLAD_API_PTR *PFN_vkEnumerateDeviceExtensionProperties)(VkPhysicalDevice   physicalDevice, const  char *  pLayerName, uint32_t *  pPropertyCount, VkExtensionProperties *  pProperties);
typedef VkResult (GLAD_API_PTR *PFN_vkEnumerateDeviceLayerProperties)(VkPhysicalDevice   physicalDevice, uint32_t *  pPropertyCount, VkLayerProperties *  pProperties);
typedef VkResult (GLAD_API_PTR *PFN_vkEnumerateInstanceExtensionProperties)(const  char *  pLayerName, uint32_t *  pPropertyCount, VkExtensionProperties *  pProperties);
typedef VkResult (GLAD_API_PTR *PFN_vkEnumerateInstanceLayerProperties)(uint32_t *  pPropertyCount, VkLayerProperties *  pProperties);
typedef VkResult (GLAD_API_PTR *PFN_vkEnumerateInstanceVersion)(uint32_t *  pApiVersion);
typedef VkResult (GLAD_API_PTR *PFN_vkEnumeratePhysicalDeviceGroups)(VkInstance   instance, uint32_t *  pPhysicalDeviceGroupCount, VkPhysicalDeviceGroupProperties *  pPhysicalDeviceGroupProperties);
typedef VkResult (GLAD_API_PTR *PFN_vkEnumeratePhysicalDevices)(VkInstance   instance, uint32_t *  pPhysicalDeviceCount, VkPhysicalDevice *  pPhysicalDevices);
typedef VkResult (GLAD_API_PTR *PFN_vkFlushMappedMemoryRanges)(VkDevice   device, uint32_t   memoryRangeCount, const  VkMappedMemoryRange *  pMemoryRanges);
typedef void (GLAD_API_PTR *PFN_vkFreeCommandBuffers)(VkDevice   device, VkCommandPool   commandPool, uint32_t   commandBufferCount, const  VkCommandBuffer *  pCommandBuffers);
typedef VkResult (GLAD_API_PTR *PFN_vkFreeDescriptorSets)(VkDevice   device, VkDescriptorPool   descriptorPool, uint32_t   descriptorSetCount, const  VkDescriptorSet *  pDescriptorSets);
typedef void (GLAD_API_PTR *PFN_vkFreeMemory)(VkDevice   device, VkDeviceMemory   memory, const  VkAllocationCallbacks *  pAllocator);
typedef void (GLAD_API_PTR *PFN_vkGetBufferMemoryRequirements)(VkDevice   device, VkBuffer   buffer, VkMemoryRequirements *  pMemoryRequirements);
typedef void (GLAD_API_PTR *PFN_vkGetBufferMemoryRequirements2)(VkDevice   device, const  VkBufferMemoryRequirementsInfo2 *  pInfo, VkMemoryRequirements2 *  pMemoryRequirements);
typedef void (GLAD_API_PTR *PFN_vkGetDescriptorSetLayoutSupport)(VkDevice   device, const  VkDescriptorSetLayoutCreateInfo *  pCreateInfo, VkDescriptorSetLayoutSupport *  pSupport);
typedef void (GLAD_API_PTR *PFN_vkGetDeviceGroupPeerMemoryFeatures)(VkDevice   device, uint32_t   heapIndex, uint32_t   localDeviceIndex, uint32_t   remoteDeviceIndex, VkPeerMemoryFeatureFlags *  pPeerMemoryFeatures);
typedef VkResult (GLAD_API_PTR *PFN_vkGetDeviceGroupPresentCapabilitiesKHR)(VkDevice   device, VkDeviceGroupPresentCapabilitiesKHR *  pDeviceGroupPresentCapabilities);
typedef VkResult (GLAD_API_PTR *PFN_vkGetDeviceGroupSurfacePresentModesKHR)(VkDevice   device, VkSurfaceKHR   surface, VkDeviceGroupPresentModeFlagsKHR *  pModes);
typedef void (GLAD_API_PTR *PFN_vkGetDeviceMemoryCommitment)(VkDevice   device, VkDeviceMemory   memory, VkDeviceSize *  pCommittedMemoryInBytes);
typedef PFN_vkVoidFunction (GLAD_API_PTR *PFN_vkGetDeviceProcAddr)(VkDevice   device, const  char *  pName);
typedef void (GLAD_API_PTR *PFN_vkGetDeviceQueue)(VkDevice   device, uint32_t   queueFamilyIndex, uint32_t   queueIndex, VkQueue *  pQueue);
typedef void (GLAD_API_PTR *PFN_vkGetDeviceQueue2)(VkDevice   device, const  VkDeviceQueueInfo2 *  pQueueInfo, VkQueue *  pQueue);
typedef VkResult (GLAD_API_PTR *PFN_vkGetEventStatus)(VkDevice   device, VkEvent   event);
typedef VkResult (GLAD_API_PTR *PFN_vkGetFenceStatus)(VkDevice   device, VkFence   fence);
typedef void (GLAD_API_PTR *PFN_vkGetImageMemoryRequirements)(VkDevice   device, VkImage   image, VkMemoryRequirements *  pMemoryRequirements);
typedef void (GLAD_API_PTR *PFN_vkGetImageMemoryRequirements2)(VkDevice   device, const  VkImageMemoryRequirementsInfo2 *  pInfo, VkMemoryRequirements2 *  pMemoryRequirements);
typedef void (GLAD_API_PTR *PFN_vkGetImageSparseMemoryRequirements)(VkDevice   device, VkImage   image, uint32_t *  pSparseMemoryRequirementCount, VkSparseImageMemoryRequirements *  pSparseMemoryRequirements);
typedef void (GLAD_API_PTR *PFN_vkGetImageSparseMemoryRequirements2)(VkDevice   device, const  VkImageSparseMemoryRequirementsInfo2 *  pInfo, uint32_t *  pSparseMemoryRequirementCount, VkSparseImageMemoryRequirements2 *  pSparseMemoryRequirements);
typedef void (GLAD_API_PTR *PFN_vkGetImageSubresourceLayout)(VkDevice   device, VkImage   image, const  VkImageSubresource *  pSubresource, VkSubresourceLayout *  pLayout);
typedef PFN_vkVoidFunction (GLAD_API_PTR *PFN_vkGetInstanceProcAddr)(VkInstance   instance, const  char *  pName);
typedef void (GLAD_API_PTR *PFN_vkGetPhysicalDeviceExternalBufferProperties)(VkPhysicalDevice   physicalDevice, const  VkPhysicalDeviceExternalBufferInfo *  pExternalBufferInfo, VkExternalBufferProperties *  pExternalBufferProperties);
typedef void (GLAD_API_PTR *PFN_vkGetPhysicalDeviceExternalFenceProperties)(VkPhysicalDevice   physicalDevice, const  VkPhysicalDeviceExternalFenceInfo *  pExternalFenceInfo, VkExternalFenceProperties *  pExternalFenceProperties);
typedef void (GLAD_API_PTR *PFN_vkGetPhysicalDeviceExternalSemaphoreProperties)(VkPhysicalDevice   physicalDevice, const  VkPhysicalDeviceExternalSemaphoreInfo *  pExternalSemaphoreInfo, VkExternalSemaphoreProperties *  pExternalSemaphoreProperties);
typedef void (GLAD_API_PTR *PFN_vkGetPhysicalDeviceFeatures)(VkPhysicalDevice   physicalDevice, VkPhysicalDeviceFeatures *  pFeatures);
typedef void (GLAD_API_PTR *PFN_vkGetPhysicalDeviceFeatures2)(VkPhysicalDevice   physicalDevice, VkPhysicalDeviceFeatures2 *  pFeatures);
typedef void (GLAD_API_PTR *PFN_vkGetPhysicalDeviceFormatProperties)(VkPhysicalDevice   physicalDevice, VkFormat   format, VkFormatProperties *  pFormatProperties);
typedef void (GLAD_API_PTR *PFN_vkGetPhysicalDeviceFormatProperties2)(VkPhysicalDevice   physicalDevice, VkFormat   format, VkFormatProperties2 *  pFormatProperties);
typedef VkResult (GLAD_API_PTR *PFN_vkGetPhysicalDeviceImageFormatProperties)(VkPhysicalDevice   physicalDevice, VkFormat   format, VkImageType   type, VkImageTiling   tiling, VkImageUsageFlags   usage, VkImageCreateFlags   flags, VkImageFormatProperties *  pImageFormatProperties);
typedef VkResult (GLAD_API_PTR *PFN_vkGetPhysicalDeviceImageFormatProperties2)(VkPhysicalDevice   physicalDevice, const  VkPhysicalDeviceImageFormatInfo2 *  pImageFormatInfo, VkImageFormatProperties2 *  pImageFormatProperties);
typedef void (GLAD_API_PTR *PFN_vkGetPhysicalDeviceMemoryProperties)(VkPhysicalDevice   physicalDevice, VkPhysicalDeviceMemoryProperties *  pMemoryProperties);
typedef void (GLAD_API_PTR *PFN_vkGetPhysicalDeviceMemoryProperties2)(VkPhysicalDevice   physicalDevice, VkPhysicalDeviceMemoryProperties2 *  pMemoryProperties);
typedef VkResult (GLAD_API_PTR *PFN_vkGetPhysicalDevicePresentRectanglesKHR)(VkPhysicalDevice   physicalDevice, VkSurfaceKHR   surface, uint32_t *  pRectCount, VkRect2D *  pRects);
typedef void (GLAD_API_PTR *PFN_vkGetPhysicalDeviceProperties)(VkPhysicalDevice   physicalDevice, VkPhysicalDeviceProperties *  pProperties);
typedef void (GLAD_API_PTR *PFN_vkGetPhysicalDeviceProperties2)(VkPhysicalDevice   physicalDevice, VkPhysicalDeviceProperties2 *  pProperties);
typedef void (GLAD_API_PTR *PFN_vkGetPhysicalDeviceQueueFamilyProperties)(VkPhysicalDevice   physicalDevice, uint32_t *  pQueueFamilyPropertyCount, VkQueueFamilyProperties *  pQueueFamilyProperties);
typedef void (GLAD_API_PTR *PFN_vkGetPhysicalDeviceQueueFamilyProperties2)(VkPhysicalDevice   physicalDevice, uint32_t *  pQueueFamilyPropertyCount, VkQueueFamilyProperties2 *  pQueueFamilyProperties);
typedef void (GLAD_API_PTR *PFN_vkGetPhysicalDeviceSparseImageFormatProperties)(VkPhysicalDevice   physicalDevice, VkFormat   format, VkImageType   type, VkSampleCountFlagBits   samples, VkImageUsageFlags   usage, VkImageTiling   tiling, uint32_t *  pPropertyCount, VkSparseImageFormatProperties *  pProperties);
typedef void (GLAD_API_PTR *PFN_vkGetPhysicalDeviceSparseImageFormatProperties2)(VkPhysicalDevice   physicalDevice, const  VkPhysicalDeviceSparseImageFormatInfo2 *  pFormatInfo, uint32_t *  pPropertyCount, VkSparseImageFormatProperties2 *  pProperties);
typedef VkResult (GLAD_API_PTR *PFN_vkGetPhysicalDeviceSurfaceCapabilitiesKHR)(VkPhysicalDevice   physicalDevice, VkSurfaceKHR   surface, VkSurfaceCapabilitiesKHR *  pSurfaceCapabilities);
typedef VkResult (GLAD_API_PTR *PFN_vkGetPhysicalDeviceSurfaceFormatsKHR)(VkPhysicalDevice   physicalDevice, VkSurfaceKHR   surface, uint32_t *  pSurfaceFormatCount, VkSurfaceFormatKHR *  pSurfaceFormats);
typedef VkResult (GLAD_API_PTR *PFN_vkGetPhysicalDeviceSurfacePresentModesKHR)(VkPhysicalDevice   physicalDevice, VkSurfaceKHR   surface, uint32_t *  pPresentModeCount, VkPresentModeKHR *  pPresentModes);
typedef VkResult (GLAD_API_PTR *PFN_vkGetPhysicalDeviceSurfaceSupportKHR)(VkPhysicalDevice   physicalDevice, uint32_t   queueFamilyIndex, VkSurfaceKHR   surface, VkBool32 *  pSupported);
typedef VkResult (GLAD_API_PTR *PFN_vkGetPipelineCacheData)(VkDevice   device, VkPipelineCache   pipelineCache, size_t *  pDataSize, void *  pData);
typedef VkResult (GLAD_API_PTR *PFN_vkGetQueryPoolResults)(VkDevice   device, VkQueryPool   queryPool, uint32_t   firstQuery, uint32_t   queryCount, size_t   dataSize, void *  pData, VkDeviceSize   stride, VkQueryResultFlags   flags);
typedef void (GLAD_API_PTR *PFN_vkGetRenderAreaGranularity)(VkDevice   device, VkRenderPass   renderPass, VkExtent2D *  pGranularity);
typedef VkResult (GLAD_API_PTR *PFN_vkGetSwapchainImagesKHR)(VkDevice   device, VkSwapchainKHR   swapchain, uint32_t *  pSwapchainImageCount, VkImage *  pSwapchainImages);
typedef VkResult (GLAD_API_PTR *PFN_vkInvalidateMappedMemoryRanges)(VkDevice   device, uint32_t   memoryRangeCount, const  VkMappedMemoryRange *  pMemoryRanges);
typedef VkResult (GLAD_API_PTR *PFN_vkMapMemory)(VkDevice   device, VkDeviceMemory   memory, VkDeviceSize   offset, VkDeviceSize   size, VkMemoryMapFlags   flags, void **  ppData);
typedef VkResult (GLAD_API_PTR *PFN_vkMergePipelineCaches)(VkDevice   device, VkPipelineCache   dstCache, uint32_t   srcCacheCount, const  VkPipelineCache *  pSrcCaches);
typedef VkResult (GLAD_API_PTR *PFN_vkQueueBindSparse)(VkQueue   queue, uint32_t   bindInfoCount, const  VkBindSparseInfo *  pBindInfo, VkFence   fence);
typedef VkResult (GLAD_API_PTR *PFN_vkQueuePresentKHR)(VkQueue   queue, const  VkPresentInfoKHR *  pPresentInfo);
typedef VkResult (GLAD_API_PTR *PFN_vkQueueSubmit)(VkQueue   queue, uint32_t   submitCount, const  VkSubmitInfo *  pSubmits, VkFence   fence);
typedef VkResult (GLAD_API_PTR *PFN_vkQueueWaitIdle)(VkQueue   queue);
typedef VkResult (GLAD_API_PTR *PFN_vkResetCommandBuffer)(VkCommandBuffer   commandBuffer, VkCommandBufferResetFlags   flags);
typedef VkResult (GLAD_API_PTR *PFN_vkResetCommandPool)(VkDevice   device, VkCommandPool   commandPool, VkCommandPoolResetFlags   flags);
typedef VkResult (GLAD_API_PTR *PFN_vkResetDescriptorPool)(VkDevice   device, VkDescriptorPool   descriptorPool, VkDescriptorPoolResetFlags   flags);
typedef VkResult (GLAD_API_PTR *PFN_vkResetEvent)(VkDevice   device, VkEvent   event);
typedef VkResult (GLAD_API_PTR *PFN_vkResetFences)(VkDevice   device, uint32_t   fenceCount, const  VkFence *  pFences);
typedef VkResult (GLAD_API_PTR *PFN_vkSetEvent)(VkDevice   device, VkEvent   event);
typedef void (GLAD_API_PTR *PFN_vkTrimCommandPool)(VkDevice   device, VkCommandPool   commandPool, VkCommandPoolTrimFlags   flags);
typedef void (GLAD_API_PTR *PFN_vkUnmapMemory)(VkDevice   device, VkDeviceMemory   memory);
typedef void (GLAD_API_PTR *PFN_vkUpdateDescriptorSetWithTemplate)(VkDevice   device, VkDescriptorSet   descriptorSet, VkDescriptorUpdateTemplate   descriptorUpdateTemplate, const  void *  pData);
typedef void (GLAD_API_PTR *PFN_vkUpdateDescriptorSets)(VkDevice   device, uint32_t   descriptorWriteCount, const  VkWriteDescriptorSet *  pDescriptorWrites, uint32_t   descriptorCopyCount, const  VkCopyDescriptorSet *  pDescriptorCopies);
typedef VkResult (GLAD_API_PTR *PFN_vkWaitForFences)(VkDevice   device, uint32_t   fenceCount, const  VkFence *  pFences, VkBool32   waitAll, uint64_t   timeout);

GLAD_API_CALL PFN_vkAcquireNextImage2KHR glad_vkAcquireNextImage2KHR;
#define vkAcquireNextImage2KHR glad_vkAcquireNextImage2KHR
GLAD_API_CALL PFN_vkAcquireNextImageKHR glad_vkAcquireNextImageKHR;
#define vkAcquireNextImageKHR glad_vkAcquireNextImageKHR
GLAD_API_CALL PFN_vkAllocateCommandBuffers glad_vkAllocateCommandBuffers;
#define vkAllocateCommandBuffers glad_vkAllocateCommandBuffers
GLAD_API_CALL PFN_vkAllocateDescriptorSets glad_vkAllocateDescriptorSets;
#define vkAllocateDescriptorSets glad_vkAllocateDescriptorSets
GLAD_API_CALL PFN_vkAllocateMemory glad_vkAllocateMemory;
#define vkAllocateMemory glad_vkAllocateMemory
GLAD_API_CALL PFN_vkBeginCommandBuffer glad_vkBeginCommandBuffer;
#define vkBeginCommandBuffer glad_vkBeginCommandBuffer
GLAD_API_CALL PFN_vkBindBufferMemory glad_vkBindBufferMemory;
#define vkBindBufferMemory glad_vkBindBufferMemory
GLAD_API_CALL PFN_vkBindBufferMemory2 glad_vkBindBufferMemory2;
#define vkBindBufferMemory2 glad_vkBindBufferMemory2
GLAD_API_CALL PFN_vkBindImageMemory glad_vkBindImageMemory;
#define vkBindImageMemory glad_vkBindImageMemory
GLAD_API_CALL PFN_vkBindImageMemory2 glad_vkBindImageMemory2;
#define vkBindImageMemory2 glad_vkBindImageMemory2
GLAD_API_CALL PFN_vkCmdBeginQuery glad_vkCmdBeginQuery;
#define vkCmdBeginQuery glad_vkCmdBeginQuery
GLAD_API_CALL PFN_vkCmdBeginRenderPass glad_vkCmdBeginRenderPass;
#define vkCmdBeginRenderPass glad_vkCmdBeginRenderPass
GLAD_API_CALL PFN_vkCmdBindDescriptorSets glad_vkCmdBindDescriptorSets;
#define vkCmdBindDescriptorSets glad_vkCmdBindDescriptorSets
GLAD_API_CALL PFN_vkCmdBindIndexBuffer glad_vkCmdBindIndexBuffer;
#define vkCmdBindIndexBuffer glad_vkCmdBindIndexBuffer
GLAD_API_CALL PFN_vkCmdBindPipeline glad_vkCmdBindPipeline;
#define vkCmdBindPipeline glad_vkCmdBindPipeline
GLAD_API_CALL PFN_vkCmdBindVertexBuffers glad_vkCmdBindVertexBuffers;
#define vkCmdBindVertexBuffers glad_vkCmdBindVertexBuffers
GLAD_API_CALL PFN_vkCmdBlitImage glad_vkCmdBlitImage;
#define vkCmdBlitImage glad_vkCmdBlitImage
GLAD_API_CALL PFN_vkCmdClearAttachments glad_vkCmdClearAttachments;
#define vkCmdClearAttachments glad_vkCmdClearAttachments
GLAD_API_CALL PFN_vkCmdClearColorImage glad_vkCmdClearColorImage;
#define vkCmdClearColorImage glad_vkCmdClearColorImage
GLAD_API_CALL PFN_vkCmdClearDepthStencilImage glad_vkCmdClearDepthStencilImage;
#define vkCmdClearDepthStencilImage glad_vkCmdClearDepthStencilImage
GLAD_API_CALL PFN_vkCmdCopyBuffer glad_vkCmdCopyBuffer;
#define vkCmdCopyBuffer glad_vkCmdCopyBuffer
GLAD_API_CALL PFN_vkCmdCopyBufferToImage glad_vkCmdCopyBufferToImage;
#define vkCmdCopyBufferToImage glad_vkCmdCopyBufferToImage
GLAD_API_CALL PFN_vkCmdCopyImage glad_vkCmdCopyImage;
#define vkCmdCopyImage glad_vkCmdCopyImage
GLAD_API_CALL PFN_vkCmdCopyImageToBuffer glad_vkCmdCopyImageToBuffer;
#define vkCmdCopyImageToBuffer glad_vkCmdCopyImageToBuffer
GLAD_API_CALL PFN_vkCmdCopyQueryPoolResults glad_vkCmdCopyQueryPoolResults;
#define vkCmdCopyQueryPoolResults glad_vkCmdCopyQueryPoolResults
GLAD_API_CALL PFN_vkCmdDispatch glad_vkCmdDispatch;
#define vkCmdDispatch glad_vkCmdDispatch
GLAD_API_CALL PFN_vkCmdDispatchBase glad_vkCmdDispatchBase;
#define vkCmdDispatchBase glad_vkCmdDispatchBase
GLAD_API_CALL PFN_vkCmdDispatchIndirect glad_vkCmdDispatchIndirect;
#define vkCmdDispatchIndirect glad_vkCmdDispatchIndirect
GLAD_API_CALL PFN_vkCmdDraw glad_vkCmdDraw;
#define vkCmdDraw glad_vkCmdDraw
GLAD_API_CALL PFN_vkCmdDrawIndexed glad_vkCmdDrawIndexed;
#define vkCmdDrawIndexed glad_vkCmdDrawIndexed
GLAD_API_CALL PFN_vkCmdDrawIndexedIndirect glad_vkCmdDrawIndexedIndirect;
#define vkCmdDrawIndexedIndirect glad_vkCmdDrawIndexedIndirect
GLAD_API_CALL PFN_vkCmdDrawIndirect glad_vkCmdDrawIndirect;
#define vkCmdDrawIndirect glad_vkCmdDrawIndirect
GLAD_API_CALL PFN_vkCmdEndQuery glad_vkCmdEndQuery;
#define vkCmdEndQuery glad_vkCmdEndQuery
GLAD_API_CALL PFN_vkCmdEndRenderPass glad_vkCmdEndRenderPass;
#define vkCmdEndRenderPass glad_vkCmdEndRenderPass
GLAD_API_CALL PFN_vkCmdExecuteCommands glad_vkCmdExecuteCommands;
#define vkCmdExecuteCommands glad_vkCmdExecuteCommands
GLAD_API_CALL PFN_vkCmdFillBuffer glad_vkCmdFillBuffer;
#define vkCmdFillBuffer glad_vkCmdFillBuffer
GLAD_API_CALL PFN_vkCmdNextSubpass glad_vkCmdNextSubpass;
#define vkCmdNextSubpass glad_vkCmdNextSubpass
GLAD_API_CALL PFN_vkCmdPipelineBarrier glad_vkCmdPipelineBarrier;
#define vkCmdPipelineBarrier glad_vkCmdPipelineBarrier
GLAD_API_CALL PFN_vkCmdPushConstants glad_vkCmdPushConstants;
#define vkCmdPushConstants glad_vkCmdPushConstants
GLAD_API_CALL PFN_vkCmdResetEvent glad_vkCmdResetEvent;
#define vkCmdResetEvent glad_vkCmdResetEvent
GLAD_API_CALL PFN_vkCmdResetQueryPool glad_vkCmdResetQueryPool;
#define vkCmdResetQueryPool glad_vkCmdResetQueryPool
GLAD_API_CALL PFN_vkCmdResolveImage glad_vkCmdResolveImage;
#define vkCmdResolveImage glad_vkCmdResolveImage
GLAD_API_CALL PFN_vkCmdSetBlendConstants glad_vkCmdSetBlendConstants;
#define vkCmdSetBlendConstants glad_vkCmdSetBlendConstants
GLAD_API_CALL PFN_vkCmdSetDepthBias glad_vkCmdSetDepthBias;
#define vkCmdSetDepthBias glad_vkCmdSetDepthBias
GLAD_API_CALL PFN_vkCmdSetDepthBounds glad_vkCmdSetDepthBounds;
#define vkCmdSetDepthBounds glad_vkCmdSetDepthBounds
GLAD_API_CALL PFN_vkCmdSetDeviceMask glad_vkCmdSetDeviceMask;
#define vkCmdSetDeviceMask glad_vkCmdSetDeviceMask
GLAD_API_CALL PFN_vkCmdSetEvent glad_vkCmdSetEvent;
#define vkCmdSetEvent glad_vkCmdSetEvent
GLAD_API_CALL PFN_vkCmdSetLineWidth glad_vkCmdSetLineWidth;
#define vkCmdSetLineWidth glad_vkCmdSetLineWidth
GLAD_API_CALL PFN_vkCmdSetScissor glad_vkCmdSetScissor;
#define vkCmdSetScissor glad_vkCmdSetScissor
GLAD_API_CALL PFN_vkCmdSetStencilCompareMask glad_vkCmdSetStencilCompareMask;
#define vkCmdSetStencilCompareMask glad_vkCmdSetStencilCompareMask
GLAD_API_CALL PFN_vkCmdSetStencilReference glad_vkCmdSetStencilReference;
#define vkCmdSetStencilReference glad_vkCmdSetStencilReference
GLAD_API_CALL PFN_vkCmdSetStencilWriteMask glad_vkCmdSetStencilWriteMask;
#define vkCmdSetStencilWriteMask glad_vkCmdSetStencilWriteMask
GLAD_API_CALL PFN_vkCmdSetViewport glad_vkCmdSetViewport;
#define vkCmdSetViewport glad_vkCmdSetViewport
GLAD_API_CALL PFN_vkCmdUpdateBuffer glad_vkCmdUpdateBuffer;
#define vkCmdUpdateBuffer glad_vkCmdUpdateBuffer
GLAD_API_CALL PFN_vkCmdWaitEvents glad_vkCmdWaitEvents;
#define vkCmdWaitEvents glad_vkCmdWaitEvents
GLAD_API_CALL PFN_vkCmdWriteTimestamp glad_vkCmdWriteTimestamp;
#define vkCmdWriteTimestamp glad_vkCmdWriteTimestamp
GLAD_API_CALL PFN_vkCreateBuffer glad_vkCreateBuffer;
#define vkCreateBuffer glad_vkCreateBuffer
GLAD_API_CALL PFN_vkCreateBufferView glad_vkCreateBufferView;
#define vkCreateBufferView glad_vkCreateBufferView
GLAD_API_CALL PFN_vkCreateCommandPool glad_vkCreateCommandPool;
#define vkCreateCommandPool glad_vkCreateCommandPool
GLAD_API_CALL PFN_vkCreateComputePipelines glad_vkCreateComputePipelines;
#define vkCreateComputePipelines glad_vkCreateComputePipelines
GLAD_API_CALL PFN_vkCreateDebugReportCallbackEXT glad_vkCreateDebugReportCallbackEXT;
#define vkCreateDebugReportCallbackEXT glad_vkCreateDebugReportCallbackEXT
GLAD_API_CALL PFN_vkCreateDescriptorPool glad_vkCreateDescriptorPool;
#define vkCreateDescriptorPool glad_vkCreateDescriptorPool
GLAD_API_CALL PFN_vkCreateDescriptorSetLayout glad_vkCreateDescriptorSetLayout;
#define vkCreateDescriptorSetLayout glad_vkCreateDescriptorSetLayout
GLAD_API_CALL PFN_vkCreateDescriptorUpdateTemplate glad_vkCreateDescriptorUpdateTemplate;
#define vkCreateDescriptorUpdateTemplate glad_vkCreateDescriptorUpdateTemplate
GLAD_API_CALL PFN_vkCreateDevice glad_vkCreateDevice;
#define vkCreateDevice glad_vkCreateDevice
GLAD_API_CALL PFN_vkCreateEvent glad_vkCreateEvent;
#define vkCreateEvent glad_vkCreateEvent
GLAD_API_CALL PFN_vkCreateFence glad_vkCreateFence;
#define vkCreateFence glad_vkCreateFence
GLAD_API_CALL PFN_vkCreateFramebuffer glad_vkCreateFramebuffer;
#define vkCreateFramebuffer glad_vkCreateFramebuffer
GLAD_API_CALL PFN_vkCreateGraphicsPipelines glad_vkCreateGraphicsPipelines;
#define vkCreateGraphicsPipelines glad_vkCreateGraphicsPipelines
GLAD_API_CALL PFN_vkCreateImage glad_vkCreateImage;
#define vkCreateImage glad_vkCreateImage
GLAD_API_CALL PFN_vkCreateImageView glad_vkCreateImageView;
#define vkCreateImageView glad_vkCreateImageView
GLAD_API_CALL PFN_vkCreateInstance glad_vkCreateInstance;
#define vkCreateInstance glad_vkCreateInstance
GLAD_API_CALL PFN_vkCreatePipelineCache glad_vkCreatePipelineCache;
#define vkCreatePipelineCache glad_vkCreatePipelineCache
GLAD_API_CALL PFN_vkCreatePipelineLayout glad_vkCreatePipelineLayout;
#define vkCreatePipelineLayout glad_vkCreatePipelineLayout
GLAD_API_CALL PFN_vkCreateQueryPool glad_vkCreateQueryPool;
#define vkCreateQueryPool glad_vkCreateQueryPool
GLAD_API_CALL PFN_vkCreateRenderPass glad_vkCreateRenderPass;
#define vkCreateRenderPass glad_vkCreateRenderPass
GLAD_API_CALL PFN_vkCreateSampler glad_vkCreateSampler;
#define vkCreateSampler glad_vkCreateSampler
GLAD_API_CALL PFN_vkCreateSamplerYcbcrConversion glad_vkCreateSamplerYcbcrConversion;
#define vkCreateSamplerYcbcrConversion glad_vkCreateSamplerYcbcrConversion
GLAD_API_CALL PFN_vkCreateSemaphore glad_vkCreateSemaphore;
#define vkCreateSemaphore glad_vkCreateSemaphore
GLAD_API_CALL PFN_vkCreateShaderModule glad_vkCreateShaderModule;
#define vkCreateShaderModule glad_vkCreateShaderModule
GLAD_API_CALL PFN_vkCreateSwapchainKHR glad_vkCreateSwapchainKHR;
#define vkCreateSwapchainKHR glad_vkCreateSwapchainKHR
GLAD_API_CALL PFN_vkDebugReportMessageEXT glad_vkDebugReportMessageEXT;
#define vkDebugReportMessageEXT glad_vkDebugReportMessageEXT
GLAD_API_CALL PFN_vkDestroyBuffer glad_vkDestroyBuffer;
#define vkDestroyBuffer glad_vkDestroyBuffer
GLAD_API_CALL PFN_vkDestroyBufferView glad_vkDestroyBufferView;
#define vkDestroyBufferView glad_vkDestroyBufferView
GLAD_API_CALL PFN_vkDestroyCommandPool glad_vkDestroyCommandPool;
#define vkDestroyCommandPool glad_vkDestroyCommandPool
GLAD_API_CALL PFN_vkDestroyDebugReportCallbackEXT glad_vkDestroyDebugReportCallbackEXT;
#define vkDestroyDebugReportCallbackEXT glad_vkDestroyDebugReportCallbackEXT
GLAD_API_CALL PFN_vkDestroyDescriptorPool glad_vkDestroyDescriptorPool;
#define vkDestroyDescriptorPool glad_vkDestroyDescriptorPool
GLAD_API_CALL PFN_vkDestroyDescriptorSetLayout glad_vkDestroyDescriptorSetLayout;
#define vkDestroyDescriptorSetLayout glad_vkDestroyDescriptorSetLayout
GLAD_API_CALL PFN_vkDestroyDescriptorUpdateTemplate glad_vkDestroyDescriptorUpdateTemplate;
#define vkDestroyDescriptorUpdateTemplate glad_vkDestroyDescriptorUpdateTemplate
GLAD_API_CALL PFN_vkDestroyDevice glad_vkDestroyDevice;
#define vkDestroyDevice glad_vkDestroyDevice
GLAD_API_CALL PFN_vkDestroyEvent glad_vkDestroyEvent;
#define vkDestroyEvent glad_vkDestroyEvent
GLAD_API_CALL PFN_vkDestroyFence glad_vkDestroyFence;
#define vkDestroyFence glad_vkDestroyFence
GLAD_API_CALL PFN_vkDestroyFramebuffer glad_vkDestroyFramebuffer;
#define vkDestroyFramebuffer glad_vkDestroyFramebuffer
GLAD_API_CALL PFN_vkDestroyImage glad_vkDestroyImage;
#define vkDestroyImage glad_vkDestroyImage
GLAD_API_CALL PFN_vkDestroyImageView glad_vkDestroyImageView;
#define vkDestroyImageView glad_vkDestroyImageView
GLAD_API_CALL PFN_vkDestroyInstance glad_vkDestroyInstance;
#define vkDestroyInstance glad_vkDestroyInstance
GLAD_API_CALL PFN_vkDestroyPipeline glad_vkDestroyPipeline;
#define vkDestroyPipeline glad_vkDestroyPipeline
GLAD_API_CALL PFN_vkDestroyPipelineCache glad_vkDestroyPipelineCache;
#define vkDestroyPipelineCache glad_vkDestroyPipelineCache
GLAD_API_CALL PFN_vkDestroyPipelineLayout glad_vkDestroyPipelineLayout;
#define vkDestroyPipelineLayout glad_vkDestroyPipelineLayout
GLAD_API_CALL PFN_vkDestroyQueryPool glad_vkDestroyQueryPool;
#define vkDestroyQueryPool glad_vkDestroyQueryPool
GLAD_API_CALL PFN_vkDestroyRenderPass glad_vkDestroyRenderPass;
#define vkDestroyRenderPass glad_vkDestroyRenderPass
GLAD_API_CALL PFN_vkDestroySampler glad_vkDestroySampler;
#define vkDestroySampler glad_vkDestroySampler
GLAD_API_CALL PFN_vkDestroySamplerYcbcrConversion glad_vkDestroySamplerYcbcrConversion;
#define vkDestroySamplerYcbcrConversion glad_vkDestroySamplerYcbcrConversion
GLAD_API_CALL PFN_vkDestroySemaphore glad_vkDestroySemaphore;
#define vkDestroySemaphore glad_vkDestroySemaphore
GLAD_API_CALL PFN_vkDestroyShaderModule glad_vkDestroyShaderModule;
#define vkDestroyShaderModule glad_vkDestroyShaderModule
GLAD_API_CALL PFN_vkDestroySurfaceKHR glad_vkDestroySurfaceKHR;
#define vkDestroySurfaceKHR glad_vkDestroySurfaceKHR
GLAD_API_CALL PFN_vkDestroySwapchainKHR glad_vkDestroySwapchainKHR;
#define vkDestroySwapchainKHR glad_vkDestroySwapchainKHR
GLAD_API_CALL PFN_vkDeviceWaitIdle glad_vkDeviceWaitIdle;
#define vkDeviceWaitIdle glad_vkDeviceWaitIdle
GLAD_API_CALL PFN_vkEndCommandBuffer glad_vkEndCommandBuffer;
#define vkEndCommandBuffer glad_vkEndCommandBuffer
GLAD_API_CALL PFN_vkEnumerateDeviceExtensionProperties glad_vkEnumerateDeviceExtensionProperties;
#define vkEnumerateDeviceExtensionProperties glad_vkEnumerateDeviceExtensionProperties
GLAD_API_CALL PFN_vkEnumerateDeviceLayerProperties glad_vkEnumerateDeviceLayerProperties;
#define vkEnumerateDeviceLayerProperties glad_vkEnumerateDeviceLayerProperties
GLAD_API_CALL PFN_vkEnumerateInstanceExtensionProperties glad_vkEnumerateInstanceExtensionProperties;
#define vkEnumerateInstanceExtensionProperties glad_vkEnumerateInstanceExtensionProperties
GLAD_API_CALL PFN_vkEnumerateInstanceLayerProperties glad_vkEnumerateInstanceLayerProperties;
#define vkEnumerateInstanceLayerProperties glad_vkEnumerateInstanceLayerProperties
GLAD_API_CALL PFN_vkEnumerateInstanceVersion glad_vkEnumerateInstanceVersion;
#define vkEnumerateInstanceVersion glad_vkEnumerateInstanceVersion
GLAD_API_CALL PFN_vkEnumeratePhysicalDeviceGroups glad_vkEnumeratePhysicalDeviceGroups;
#define vkEnumeratePhysicalDeviceGroups glad_vkEnumeratePhysicalDeviceGroups
GLAD_API_CALL PFN_vkEnumeratePhysicalDevices glad_vkEnumeratePhysicalDevices;
#define vkEnumeratePhysicalDevices glad_vkEnumeratePhysicalDevices
GLAD_API_CALL PFN_vkFlushMappedMemoryRanges glad_vkFlushMappedMemoryRanges;
#define vkFlushMappedMemoryRanges glad_vkFlushMappedMemoryRanges
GLAD_API_CALL PFN_vkFreeCommandBuffers glad_vkFreeCommandBuffers;
#define vkFreeCommandBuffers glad_vkFreeCommandBuffers
GLAD_API_CALL PFN_vkFreeDescriptorSets glad_vkFreeDescriptorSets;
#define vkFreeDescriptorSets glad_vkFreeDescriptorSets
GLAD_API_CALL PFN_vkFreeMemory glad_vkFreeMemory;
#define vkFreeMemory glad_vkFreeMemory
GLAD_API_CALL PFN_vkGetBufferMemoryRequirements glad_vkGetBufferMemoryRequirements;
#define vkGetBufferMemoryRequirements glad_vkGetBufferMemoryRequirements
GLAD_API_CALL PFN_vkGetBufferMemoryRequirements2 glad_vkGetBufferMemoryRequirements2;
#define vkGetBufferMemoryRequirements2 glad_vkGetBufferMemoryRequirements2
GLAD_API_CALL PFN_vkGetDescriptorSetLayoutSupport glad_vkGetDescriptorSetLayoutSupport;
#define vkGetDescriptorSetLayoutSupport glad_vkGetDescriptorSetLayoutSupport
GLAD_API_CALL PFN_vkGetDeviceGroupPeerMemoryFeatures glad_vkGetDeviceGroupPeerMemoryFeatures;
#define vkGetDeviceGroupPeerMemoryFeatures glad_vkGetDeviceGroupPeerMemoryFeatures
GLAD_API_CALL PFN_vkGetDeviceGroupPresentCapabilitiesKHR glad_vkGetDeviceGroupPresentCapabilitiesKHR;
#define vkGetDeviceGroupPresentCapabilitiesKHR glad_vkGetDeviceGroupPresentCapabilitiesKHR
GLAD_API_CALL PFN_vkGetDeviceGroupSurfacePresentModesKHR glad_vkGetDeviceGroupSurfacePresentModesKHR;
#define vkGetDeviceGroupSurfacePresentModesKHR glad_vkGetDeviceGroupSurfacePresentModesKHR
GLAD_API_CALL PFN_vkGetDeviceMemoryCommitment glad_vkGetDeviceMemoryCommitment;
#define vkGetDeviceMemoryCommitment glad_vkGetDeviceMemoryCommitment
GLAD_API_CALL PFN_vkGetDeviceProcAddr glad_vkGetDeviceProcAddr;
#define vkGetDeviceProcAddr glad_vkGetDeviceProcAddr
GLAD_API_CALL PFN_vkGetDeviceQueue glad_vkGetDeviceQueue;
#define vkGetDeviceQueue glad_vkGetDeviceQueue
GLAD_API_CALL PFN_vkGetDeviceQueue2 glad_vkGetDeviceQueue2;
#define vkGetDeviceQueue2 glad_vkGetDeviceQueue2
GLAD_API_CALL PFN_vkGetEventStatus glad_vkGetEventStatus;
#define vkGetEventStatus glad_vkGetEventStatus
GLAD_API_CALL PFN_vkGetFenceStatus glad_vkGetFenceStatus;
#define vkGetFenceStatus glad_vkGetFenceStatus
GLAD_API_CALL PFN_vkGetImageMemoryRequirements glad_vkGetImageMemoryRequirements;
#define vkGetImageMemoryRequirements glad_vkGetImageMemoryRequirements
GLAD_API_CALL PFN_vkGetImageMemoryRequirements2 glad_vkGetImageMemoryRequirements2;
#define vkGetImageMemoryRequirements2 glad_vkGetImageMemoryRequirements2
GLAD_API_CALL PFN_vkGetImageSparseMemoryRequirements glad_vkGetImageSparseMemoryRequirements;
#define vkGetImageSparseMemoryRequirements glad_vkGetImageSparseMemoryRequirements
GLAD_API_CALL PFN_vkGetImageSparseMemoryRequirements2 glad_vkGetImageSparseMemoryRequirements2;
#define vkGetImageSparseMemoryRequirements2 glad_vkGetImageSparseMemoryRequirements2
GLAD_API_CALL PFN_vkGetImageSubresourceLayout glad_vkGetImageSubresourceLayout;
#define vkGetImageSubresourceLayout glad_vkGetImageSubresourceLayout
GLAD_API_CALL PFN_vkGetInstanceProcAddr glad_vkGetInstanceProcAddr;
#define vkGetInstanceProcAddr glad_vkGetInstanceProcAddr
GLAD_API_CALL PFN_vkGetPhysicalDeviceExternalBufferProperties glad_vkGetPhysicalDeviceExternalBufferProperties;
#define vkGetPhysicalDeviceExternalBufferProperties glad_vkGetPhysicalDeviceExternalBufferProperties
GLAD_API_CALL PFN_vkGetPhysicalDeviceExternalFenceProperties glad_vkGetPhysicalDeviceExternalFenceProperties;
#define vkGetPhysicalDeviceExternalFenceProperties glad_vkGetPhysicalDeviceExternalFenceProperties
GLAD_API_CALL PFN_vkGetPhysicalDeviceExternalSemaphoreProperties glad_vkGetPhysicalDeviceExternalSemaphoreProperties;
#define vkGetPhysicalDeviceExternalSemaphoreProperties glad_vkGetPhysicalDeviceExternalSemaphoreProperties
GLAD_API_CALL PFN_vkGetPhysicalDeviceFeatures glad_vkGetPhysicalDeviceFeatures;
#define vkGetPhysicalDeviceFeatures glad_vkGetPhysicalDeviceFeatures
GLAD_API_CALL PFN_vkGetPhysicalDeviceFeatures2 glad_vkGetPhysicalDeviceFeatures2;
#define vkGetPhysicalDeviceFeatures2 glad_vkGetPhysicalDeviceFeatures2
GLAD_API_CALL PFN_vkGetPhysicalDeviceFormatProperties glad_vkGetPhysicalDeviceFormatProperties;
#define vkGetPhysicalDeviceFormatProperties glad_vkGetPhysicalDeviceFormatProperties
GLAD_API_CALL PFN_vkGetPhysicalDeviceFormatProperties2 glad_vkGetPhysicalDeviceFormatProperties2;
#define vkGetPhysicalDeviceFormatProperties2 glad_vkGetPhysicalDeviceFormatProperties2
GLAD_API_CALL PFN_vkGetPhysicalDeviceImageFormatProperties glad_vkGetPhysicalDeviceImageFormatProperties;
#define vkGetPhysicalDeviceImageFormatProperties glad_vkGetPhysicalDeviceImageFormatProperties
GLAD_API_CALL PFN_vkGetPhysicalDeviceImageFormatProperties2 glad_vkGetPhysicalDeviceImageFormatProperties2;
#define vkGetPhysicalDeviceImageFormatProperties2 glad_vkGetPhysicalDeviceImageFormatProperties2
GLAD_API_CALL PFN_vkGetPhysicalDeviceMemoryProperties glad_vkGetPhysicalDeviceMemoryProperties;
#define vkGetPhysicalDeviceMemoryProperties glad_vkGetPhysicalDeviceMemoryProperties
GLAD_API_CALL PFN_vkGetPhysicalDeviceMemoryProperties2 glad_vkGetPhysicalDeviceMemoryProperties2;
#define vkGetPhysicalDeviceMemoryProperties2 glad_vkGetPhysicalDeviceMemoryProperties2
GLAD_API_CALL PFN_vkGetPhysicalDevicePresentRectanglesKHR glad_vkGetPhysicalDevicePresentRectanglesKHR;
#define vkGetPhysicalDevicePresentRectanglesKHR glad_vkGetPhysicalDevicePresentRectanglesKHR
GLAD_API_CALL PFN_vkGetPhysicalDeviceProperties glad_vkGetPhysicalDeviceProperties;
#define vkGetPhysicalDeviceProperties glad_vkGetPhysicalDeviceProperties
GLAD_API_CALL PFN_vkGetPhysicalDeviceProperties2 glad_vkGetPhysicalDeviceProperties2;
#define vkGetPhysicalDeviceProperties2 glad_vkGetPhysicalDeviceProperties2
GLAD_API_CALL PFN_vkGetPhysicalDeviceQueueFamilyProperties glad_vkGetPhysicalDeviceQueueFamilyProperties;
#define vkGetPhysicalDeviceQueueFamilyProperties glad_vkGetPhysicalDeviceQueueFamilyProperties
GLAD_API_CALL PFN_vkGetPhysicalDeviceQueueFamilyProperties2 glad_vkGetPhysicalDeviceQueueFamilyProperties2;
#define vkGetPhysicalDeviceQueueFamilyProperties2 glad_vkGetPhysicalDeviceQueueFamilyProperties2
GLAD_API_CALL PFN_vkGetPhysicalDeviceSparseImageFormatProperties glad_vkGetPhysicalDeviceSparseImageFormatProperties;
#define vkGetPhysicalDeviceSparseImageFormatProperties glad_vkGetPhysicalDeviceSparseImageFormatProperties
GLAD_API_CALL PFN_vkGetPhysicalDeviceSparseImageFormatProperties2 glad_vkGetPhysicalDeviceSparseImageFormatProperties2;
#define vkGetPhysicalDeviceSparseImageFormatProperties2 glad_vkGetPhysicalDeviceSparseImageFormatProperties2
GLAD_API_CALL PFN_vkGetPhysicalDeviceSurfaceCapabilitiesKHR glad_vkGetPhysicalDeviceSurfaceCapabilitiesKHR;
#define vkGetPhysicalDeviceSurfaceCapabilitiesKHR glad_vkGetPhysicalDeviceSurfaceCapabilitiesKHR
GLAD_API_CALL PFN_vkGetPhysicalDeviceSurfaceFormatsKHR glad_vkGetPhysicalDeviceSurfaceFormatsKHR;
#define vkGetPhysicalDeviceSurfaceFormatsKHR glad_vkGetPhysicalDeviceSurfaceFormatsKHR
GLAD_API_CALL PFN_vkGetPhysicalDeviceSurfacePresentModesKHR glad_vkGetPhysicalDeviceSurfacePresentModesKHR;
#define vkGetPhysicalDeviceSurfacePresentModesKHR glad_vkGetPhysicalDeviceSurfacePresentModesKHR
GLAD_API_CALL PFN_vkGetPhysicalDeviceSurfaceSupportKHR glad_vkGetPhysicalDeviceSurfaceSupportKHR;
#define vkGetPhysicalDeviceSurfaceSupportKHR glad_vkGetPhysicalDeviceSurfaceSupportKHR
GLAD_API_CALL PFN_vkGetPipelineCacheData glad_vkGetPipelineCacheData;
#define vkGetPipelineCacheData glad_vkGetPipelineCacheData
GLAD_API_CALL PFN_vkGetQueryPoolResults glad_vkGetQueryPoolResults;
#define vkGetQueryPoolResults glad_vkGetQueryPoolResults
GLAD_API_CALL PFN_vkGetRenderAreaGranularity glad_vkGetRenderAreaGranularity;
#define vkGetRenderAreaGranularity glad_vkGetRenderAreaGranularity
GLAD_API_CALL PFN_vkGetSwapchainImagesKHR glad_vkGetSwapchainImagesKHR;
#define vkGetSwapchainImagesKHR glad_vkGetSwapchainImagesKHR
GLAD_API_CALL PFN_vkInvalidateMappedMemoryRanges glad_vkInvalidateMappedMemoryRanges;
#define vkInvalidateMappedMemoryRanges glad_vkInvalidateMappedMemoryRanges
GLAD_API_CALL PFN_vkMapMemory glad_vkMapMemory;
#define vkMapMemory glad_vkMapMemory
GLAD_API_CALL PFN_vkMergePipelineCaches glad_vkMergePipelineCaches;
#define vkMergePipelineCaches glad_vkMergePipelineCaches
GLAD_API_CALL PFN_vkQueueBindSparse glad_vkQueueBindSparse;
#define vkQueueBindSparse glad_vkQueueBindSparse
GLAD_API_CALL PFN_vkQueuePresentKHR glad_vkQueuePresentKHR;
#define vkQueuePresentKHR glad_vkQueuePresentKHR
GLAD_API_CALL PFN_vkQueueSubmit glad_vkQueueSubmit;
#define vkQueueSubmit glad_vkQueueSubmit
GLAD_API_CALL PFN_vkQueueWaitIdle glad_vkQueueWaitIdle;
#define vkQueueWaitIdle glad_vkQueueWaitIdle
GLAD_API_CALL PFN_vkResetCommandBuffer glad_vkResetCommandBuffer;
#define vkResetCommandBuffer glad_vkResetCommandBuffer
GLAD_API_CALL PFN_vkResetCommandPool glad_vkResetCommandPool;
#define vkResetCommandPool glad_vkResetCommandPool
GLAD_API_CALL PFN_vkResetDescriptorPool glad_vkResetDescriptorPool;
#define vkResetDescriptorPool glad_vkResetDescriptorPool
GLAD_API_CALL PFN_vkResetEvent glad_vkResetEvent;
#define vkResetEvent glad_vkResetEvent
GLAD_API_CALL PFN_vkResetFences glad_vkResetFences;
#define vkResetFences glad_vkResetFences
GLAD_API_CALL PFN_vkSetEvent glad_vkSetEvent;
#define vkSetEvent glad_vkSetEvent
GLAD_API_CALL PFN_vkTrimCommandPool glad_vkTrimCommandPool;
#define vkTrimCommandPool glad_vkTrimCommandPool
GLAD_API_CALL PFN_vkUnmapMemory glad_vkUnmapMemory;
#define vkUnmapMemory glad_vkUnmapMemory
GLAD_API_CALL PFN_vkUpdateDescriptorSetWithTemplate glad_vkUpdateDescriptorSetWithTemplate;
#define vkUpdateDescriptorSetWithTemplate glad_vkUpdateDescriptorSetWithTemplate
GLAD_API_CALL PFN_vkUpdateDescriptorSets glad_vkUpdateDescriptorSets;
#define vkUpdateDescriptorSets glad_vkUpdateDescriptorSets
GLAD_API_CALL PFN_vkWaitForFences glad_vkWaitForFences;
#define vkWaitForFences glad_vkWaitForFences


GLAD_API_CALL int gladLoadVulkanUserPtr( VkPhysicalDevice physical_device, GLADuserptrloadfunc load, void *userptr);
GLAD_API_CALL int gladLoadVulkan( VkPhysicalDevice physical_device, GLADloadfunc load);






#ifdef __cplusplus
}
#endif
#endif
