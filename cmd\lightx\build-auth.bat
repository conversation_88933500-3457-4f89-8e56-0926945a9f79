@echo off
setlocal enabledelayedexpansion

REM Lightx Auth Build Script (Windows)
REM Support building normal and restricted versions

if "%1"=="" goto show_help
if "%1"=="help" goto show_help
if "%1"=="--help" goto show_help
if "%1"=="-h" goto show_help

if "%1"=="normal" goto build_normal
if "%1"=="restricted" goto build_restricted
if "%1"=="test" goto build_test
if "%1"=="clean" goto clean_build

echo Error: Unknown option '%1'
echo.
goto show_help

:show_help
echo Lightx Auth Build Script (Windows)
echo.
echo Usage:
echo   %0 [option]
echo.
echo Options:
echo   normal              Build normal version (no restrictions)
echo   restricted [days]   Build restricted version (default 30 days)
echo   test [minutes]      Build test version (minute-level expiry)
echo   clean              Clean build files
echo   help               Show this help
echo.
echo Examples:
echo   %0 normal                    # Build normal version
echo   %0 restricted               # Build 30-day restricted version
echo   %0 restricted 7             # Build 7-day restricted version
echo   %0 test 10                  # Build 10-minute test version
echo.
goto end

:clean_build
echo Cleaning build files...
if exist .\lightx.exe del .\lightx.exe
if exist .\lightx-restricted.exe del .\lightx-restricted.exe
if exist .\lightx-test.exe del .\lightx-test.exe
echo Clean completed
goto end

:build_normal
echo Building normal version (no restrictions)...

REM Get version info
for /f "tokens=*" %%i in ('git describe --tags --always 2^>nul') do set VERSION=%%i
if "!VERSION!"=="" set VERSION=dev

REM Get build time in correct format (YYYY-MM-DD HH:MM:SS)
REM Use PowerShell to get properly formatted time
for /f "delims=" %%i in ('powershell -command "Get-Date -Format 'yyyy-MM-dd HH:mm:ss'"') do set BUILD_TIME=%%i

REM Build with optimization flags
set BUILD_CMD=go build -ldflags "-s -w -X 'main.Version=!VERSION!' -X 'main.BuildTime=!BUILD_TIME!'" -trimpath -buildmode=pie  -o .\lightx.exe
echo Executing build command:
echo   !BUILD_CMD!
echo.

go build -ldflags "-s -w -X 'main.Version=!VERSION!' -X 'main.BuildTime=!BUILD_TIME!'" -trimpath -buildmode=pie  -o .\lightx.exe

if !errorlevel! equ 0 (
    echo Success: Normal version built - .\lightx.exe
    echo   Version: !VERSION!
    echo   Build time: !BUILD_TIME!
) else (
    echo Error: Build failed
    exit /b 1
)
goto end

:build_restricted
set DAYS=%2
if "%DAYS%"=="" set DAYS=30

echo Building restricted version (%DAYS% days validity)...

REM Get version info
for /f "tokens=*" %%i in ('git describe --tags --always 2^>nul') do set VERSION=%%i
if "!VERSION!"=="" set VERSION=dev

REM Get build time in correct format (YYYY-MM-DD HH:MM:SS)
REM Use PowerShell to get properly formatted time
for /f "delims=" %%i in ('powershell -command "Get-Date -Format 'yyyy-MM-dd HH:mm:ss'"') do set BUILD_TIME=%%i

REM Build with auth tags and optimization flags
set BUILD_CMD=go build -tags auth -ldflags "-s -w -X 'main.Version=!VERSION!' -X 'main.BuildTime=!BUILD_TIME!' -X 'Lightx/internal/common.ValidDaysStr=%DAYS%' -X 'Lightx/internal/common.BuildTimestamp=!BUILD_TIME!'" -trimpath -buildmode=pie  -o .\lightx-restricted.exe
echo Executing build command:
echo   !BUILD_CMD!
echo.

go build -tags auth -ldflags "-s -w -X 'main.Version=!VERSION!' -X 'main.BuildTime=!BUILD_TIME!' -X 'Lightx/internal/common.ValidDaysStr=%DAYS%' -X 'Lightx/internal/common.BuildTimestamp=!BUILD_TIME!'" -trimpath -buildmode=pie  -o .\lightx-restricted.exe

if !errorlevel! equ 0 (
    echo Success: Restricted version built - .\lightx-restricted.exe
    echo   Version: !VERSION!
    echo   Build time: !BUILD_TIME!
    echo   Validity: %DAYS% days
    echo.
    echo Note: This version includes time limits and security validation
) else (
    echo Error: Build failed
    exit /b 1
)
goto end

:build_test
set MINUTES=%2
if "%MINUTES%"=="" set MINUTES=10

echo Building test version (%MINUTES% minutes validity)...

REM Get version info
for /f "tokens=*" %%i in ('git describe --tags --always 2^>nul') do set VERSION=%%i
if "!VERSION!"=="" set VERSION=dev-test

REM Get build time in correct format (YYYY-MM-DD HH:MM:SS)
REM Use PowerShell to get properly formatted time
for /f "delims=" %%i in ('powershell -command "Get-Date -Format 'yyyy-MM-dd HH:mm:ss'"') do set BUILD_TIME=%%i

REM Build with auth tags and optimization flags
set BUILD_CMD=go build -tags auth -ldflags "-s -w -X 'main.Version=!VERSION!' -X 'main.BuildTime=!BUILD_TIME!' -X 'Lightx/internal/common.ValidMinutesStr=%MINUTES%' -X 'Lightx/internal/common.BuildTimestamp=!BUILD_TIME!'" -trimpath -buildmode=pie  -o .\lightx-test.exe
echo Executing build command:
echo   !BUILD_CMD!
echo.

go build -tags auth -ldflags "-s -w -X 'main.Version=!VERSION!' -X 'main.BuildTime=!BUILD_TIME!' -X 'Lightx/internal/common.ValidMinutesStr=%MINUTES%' -X 'Lightx/internal/common.BuildTimestamp=!BUILD_TIME!'" -trimpath -buildmode=pie  -o .\lightx-test.exe

if !errorlevel! equ 0 (
    echo Success: Test version built - .\lightx-test.exe
    echo   Version: !VERSION!
    echo   Build time: !BUILD_TIME!
    echo   Validity: %MINUTES% minutes
    echo.
    echo Note: This version is for testing only, expires in %MINUTES% minutes
) else (
    echo Error: Build failed
    exit /b 1
)
goto end

:end
endlocal
