package portscan

import (
	"Lightx/internal/common"
	"Lightx/lib/structs"
	"Lightx/pkg/clients"
	"Lightx/pkg/color"
	"Lightx/pkg/webscan"
	"Lightx/pkg/webscan/report"
	"context"
	"fmt"
	"math/rand"
	"os"
	"sync"
	"sync/atomic"
	"time"

	"github.com/panjf2000/ants/v2"
	"github.com/projectdiscovery/gologger"
)

var ExitFunc = false

// 默认配置参数
const (
	DefaultTCPThreads     = 1000 // TCP扫描线程数
	DefaultTCPTimeout     = 5    // TCP扫描超时时间(秒)
	DefaultPortsThreshold = 1000 // 端口阈值，超过此值认为可能是防火墙
)

// 黑名单IP映射，记录端口数量超出阈值的IP
var BlackList map[string]struct{}
var BlackListLock sync.Mutex

// 通用默认端口列表
var CommonPorts = []int{9440, 2379, 18848, 18083, 28848, 38848, 48848, 58848, 1883, 12222, 32222, 10022, 20022, 13389, 23389, 33389, 43389, 53389, 33890, 33891, 3307, 33060, 13306, 23306, 6380, 16379, 26379, 27019, 2443, 15060, 25060, 21, 22, 23, 53, 69, 80, 81, 88, 89, 135, 161, 445, 139, 137, 143, 389, 443, 512, 513, 514, 548, 873, 1433, 1521, 2181, 3306, 3389, 3690, 4848, 5000, 5001, 5432, 5632, 5900, 5901, 5902, 6379, 7000, 7001, 7002, 8000, 8001, 8007, 8008, 8009, 8069, 8080, 8081, 8088, 8089, 8090, 8091, 9060, 9090, 9091, 9200, 9300, 10000, 11211, 27017, 27018, 50000, 1080, 888, 1158, 2100, 2424, 2601, 2604, 3128, 5984, 7080, 8010, 8082, 8083, 8084, 8085, 8086, 8087, 8222, 8443, 8686, 8888, 9000, 9001, 9002, 9003, 9004, 9005, 9006, 9007, 9008, 9009, 9010, 9043, 9080, 9081, 9418, 9999, 50030, 50060, 50070, 82, 83, 84, 85, 86, 87, 7003, 7004, 7005, 7006, 7007, 7008, 7009, 7010, 7070, 7071, 7072, 7073, 7074, 7075, 7076, 7077, 7078, 7079, 8002, 8003, 8004, 8005, 8006, 8200, 90, 801, 8011, 8100, 8012, 8070, 99, 7777, 8028, 808, 38888, 8181, 800, 18080, 8099, 8899, 8360, 8300, 8800, 8180, 3505, 8053, 1000, 8989, 28017, 49166, 3000, 41516, 880, 8484, 6677, 8016, 7200, 9085, 5555, 8280, 1980, 8161, 7890, 8060, 6080, 8880, 8020, 889, 8881, 38501, 1010, 93, 6666, 100, 6789, 7060, 8018, 8022, 3050, 8787, 2000, 10001, 8013, 6888, 8040, 10021, 2011, 6006, 4000, 8055, 4430, 1723, 6060, 7788, 8066, 9898, 6001, 8801, 10040, 9998, 803, 6688, 10080, 8050, 7011, 40310, 18090, 802, 10003, 8014, 2080, 7288, 8044, 9992, 8889, 5644, 8886, 9500, 58031, 9020, 8015, 8887, 8021, 8700, 91, 9900, 9191, 3312, 8186, 8735, 8380, 1234, 38080, 9088, 9988, 2110, 21245, 3333, 2046, 9061, 2375, 9011, 8061, 8093, 9876, 8030, 8282, 60465, 2222, 98, 1100, 18081, 70, 8383, 5155, 92, 8188, 2517, 8062, 11324, 2008, 9231, 999, 28214, 16080, 8092, 8987, 8038, 809, 2010, 8983, 7700, 3535, 7921, 9093, 11080, 6778, 805, 9083, 8073, 10002, 114, 2012, 701, 8810, 8400, 9099, 8098, 8808, 20000, 8065, 8822, 15000, 9901, 11158, 1107, 28099, 12345, 2006, 9527, 51106, 688, 25006, 8045, 8023, 8029, 9997, 7048, 8580, 8585, 2001, 8035, 10088, 20022, 4001, 2013, 20808, 8095, 106, 3580, 7742, 8119, 6868, 32766, 50075, 7272, 3380, 3220, 7801, 5256, 5255, 10086, 1300, 5200, 8096, 6198, 6889, 3503, 6088, 9991, 806, 5050, 8183, 8688, 1001, 58080, 1182, 9025, 8112, 7776, 7321, 235, 8077, 8500, 11347, 7081, 8877, 8480, 9182, 58000, 8026, 11001, 10089, 5888, 8196, 8078, 9995, 2014, 5656, 8019, 5003, 8481, 6002, 9889, 9015, 8866, 8182, 8057, 8399, 10010, 8308, 511, 12881, 4016, 8042, 1039, 28080, 5678, 7500, 8051, 18801, 15018, 15888, 38443, 8123, 8144, 94, 9070, 1800, 9112, 8990, 3456, 2051, 9098, 444, 9131, 97, 7100, 7711, 7180, 11000, 8037, 6988, 122, 8885, 14007, 8184, 7012, 8079, 9888, 9301, 59999, 49705, 1979, 8900, 5080, 5013, 1550, 8844, 4850, 206, 5156, 8813, 3030, 1790, 8802, 9012, 5544, 3721, 8980, 10009, 8043, 8390, 7943, 8381, 8056, 7111, 1500, 7088, 5881, 9437, 5655, 8102, 6000, 65486, 4443, 10025, 8024, 8333, 8666, 103, 8, 9666, 8999, 9111, 8071, 9092, 522, 11381, 20806, 8041, 1085, 8864, 7900, 1700, 8036, 8032, 8033, 8111, 60022, 955, 3080, 8788, 7443, 8192, 6969, 9909, 5002, 9990, 188, 8910, 9022, 10004, 866, 8582, 4300, 9101, 6879, 8891, 4567, 4440, 10051, 10068, 50080, 8341, 30001, 6890, 8168, 8955, 16788, 8190, 18060, 7041, 42424, 8848, 15693, 2521, 19010, 18103, 6010, 8898, 9910, 9190, 9082, 8260, 8445, 1680, 8890, 8649, 30082, 3013, 30000, 2480, 7202, 9704, 5233, 8991, 11366, 7888, 8780, 7129, 6600, 9443, 47088, 7791, 18888, 50045, 15672, 9089, 2585, 60, 9494, 31945, 2060, 8610, 8860, 58060, 6118, 2348, 8097, 38000, 18880, 13382, 6611, 8064, 7101, 5081, 7380, 7942, 10016, 8027, 2093, 403, 9014, 8133, 6886, 95, 8058, 9201, 6443, 5966, 27000, 7017, 6680, 8401, 9036, 8988, 8806, 6180, 421, 423, 57880, 7778, 18881, 812, 15004, 9110, 8213, 8868, 1213, 8193, 8956, 1108, 778, 65000, 7020, 1122, 9031, 17000, 8039, 8600, 50090, 1863, 8191, 65, 6587, 8136, 9507, 132, 200, 2070, 308, 5811, 3465, 8680, 7999, 7084, 18082, 3938, 18001, 9595, 442, 4433, 7171, 9084, 7567, 811, 1128, 6003, 2125, 6090, 10007, 7022, 1949, 6565, 65001, 1301, 19244, 10087, 8025, 5098, 21080, 1200, 15801, 1005, 22343, 7086, 8601, 6259, 7102, 10333, 211, 10082, 18085, 180, 40000, 7021, 7702, 66, 38086, 666, 6603, 1212, 65493, 96, 9053, 7031, 23454, 30088, 6226, 8660, 6170, 8972, 9981, 48080, 9086, 10118, 40069, 28780, 20153, 20021, 20151, 58898, 10066, 1818, 9914, 55351, 8343, 18000, 6546, 3880, 8902, 22222, 19045, 5561, 7979, 5203, 8879, 50240, 49960, 2007, 1722, 8913, 8912, 9504, 8103, 8567, 1666, 8720, 8197, 3012, 8220, 9039, 5898, 925, 38517, 8382, 6842, 8895, 2808, 447, 3600, 3606, 9095, 45177, 19101, 171, 133, 8189, 7108, 10154, 47078, 6800, 8122, 381, 1443, 15580, 23352, 3443, 1180, 268, 2382, 43651, 10099, 65533, 7018, 60010, 60101, 6699, 2005, 18002, 2009, 59777, 591, 1933, 9013, 8477, 9696, 9030, 2015, 7925, 6510, 18803, 280, 5601, 2901, 2301, 5201, 302, 610, 8031, 5552, 8809, 6869, 9212, 17095, 20001, 8781, 25024, 5280, 7909, 17003, 1088, 7117, 20052, 1900, 10038, 30551, 9980, 9180, 59009, 28280, 7028, 61999, 7915, 8384, 9918, 9919, 55858, 7215, 77, 9845, 20140, 8288, 7856, 1982, 1123, 17777, 8839, 208, 2886, 877, 6101, 5100, 804, 983, 5600, 8402, 5887, 8322, 770, 13333, 7330, 3216, 31188, 47583, 8710, 22580, 1042, 2020, 34440, 20, 7703, 65055, 8997, 6543, 6388, 8283, 7201, 4040, 61081, 12001, 3588, 7123, 2490, 4389, 1313, 19080, 9050, 6920, 299, 20046, 8892, 9302, 7899, 30058, 7094, 6801, 321, 1356, 12333, 11362, 11372, 6602, 7709, 45149, 3668, 517, 9912, 9096, 8130, 7050, 7713, 40080, 8104, 13988, 18264, 8799, 55070, 23458, 8176, 9517, 9541, 9542, 9512, 8905, 11660, 1025, 44445, 44401, 17173, 436, 560, 733, 968, 602, 3133, 3398, 16580, 8488, 8901, 8512, 10443, 9113, 9119, 6606, 22080, 5560, 7, 5757, 1600, 8250, 10024, 10200, 333, 73, 7547, 8054, 6372, 223, 3737, 9800, 9019, 8067, 45692, 15400, 15698, 9038, 37006, 2086, 1002, 9188, 8094, 8201, 8202, 30030, 2663, 9105, 10017, 4503, 1104, 8893, 40001, 27779, 3010, 7083, 5010, 5501, 309, 1389, 10070, 10069, 10056, 3094, 10057, 10078, 10050, 10060, 10098, 4180, 10777, 270, 6365, 9801, 1046, 7140, 1004, 9198, 8465, 8548, 108, 30015, 8153, 1020, 50100, 8391, 34899, 7090, 6100, 8777, 8298, 8281, 7023, 3377, 9100}

// ParsePortRange 解析端口范围字符串，例如："80,443,8080-8090"
func ParsePortRange(portRange string, excludePortRange string) []int {
	// 如果端口范围为空，使用默认端口列表
	if portRange == "" {
		portRange = "default"
	}

	// 获取包含的端口列表
	var includePorts []int
	// 尝试从全局端口列表配置中获取预定义的端口列表
	if common.GlobalPortListConfig != nil {
		if ports, exists := common.GlobalPortListConfig.PortMap[portRange]; exists {
			includePorts = ports
		}
	}

	// 如果是"default"但没有找到预定义列表，使用CommonPorts
	if portRange == "default" && len(includePorts) == 0 {
		includePorts = CommonPorts
	}

	// 如果不是预定义列表，则解析端口范围
	if len(includePorts) == 0 {
		includePorts = ParsePort(portRange)
	}

	// 如果没有指定排除端口，直接返回包含的端口列表
	if excludePortRange == "" {
		return includePorts
	}

	// 解析排除的端口列表
	excludePorts := ParsePort(excludePortRange)

	// 如果没有排除的端口，直接返回包含的端口列表
	if len(excludePorts) == 0 {
		return includePorts
	}

	// 创建排除端口的映射，用于快速查找
	excludeMap := make(map[int]struct{})
	for _, port := range excludePorts {
		excludeMap[port] = struct{}{}
	}

	// 过滤掉排除的端口
	var resultPorts []int
	for _, port := range includePorts {
		if _, excluded := excludeMap[port]; !excluded {
			resultPorts = append(resultPorts, port)
		}
	}

	return resultPorts
}

// Address 表示IP地址和端口的组合
type Address struct {
	IP   string
	Port int
}

// 初始化黑名单
func init() {
	BlackList = make(map[string]struct{})
}

// ScanIP 对单个IP进行端口扫描
func ScanIP(ip string, ports []int, workers, timeout int, proxy clients.Proxy, logFile string, enableFingerprint bool) []PortResult {
	// 如果IP在黑名单中，跳过扫描
	BlackListLock.Lock()
	_, inBlack := BlackList[ip]
	BlackListLock.Unlock()
	if inBlack {
		gologger.Warning().Msgf("IP %s 在黑名单中，跳过扫描", ip)
		return nil
	}

	addresses := make(chan Address, len(ports))
	results := make([]PortResult, 0)
	resultChan := make(chan PortResult, workers) // 增加缓冲区大小，避免阻塞
	done := make(chan struct{})
	ipPortCount := make(map[string]int) // 记录IP开放端口数量

	// 添加进度统计变量
	totalTasks := len(ports)
	var scannedPorts int32
	var foundPorts int32
	var failedPorts int32
	var progressMutex sync.Mutex // 进度显示互斥锁

	// 启动接收结果的协程
	go func() {
		for pr := range resultChan {
			if pr.Status {
				// 使用FormatPortOutput函数格式化输出
				message := FormatPortOutput(pr) + "\n"

				// 更新找到的端口数
				atomic.AddInt32(&foundPorts, 1)

				// 清除当前行，输出端口信息，然后恢复进度条
				progressMutex.Lock()
				current := atomic.LoadInt32(&scannedPorts)
				failed := atomic.LoadInt32(&failedPorts)
				found := atomic.LoadInt32(&foundPorts)

				// 清除当前进度行
				fmt.Print("\r                                                                               \r")

				// 输出端口信息
				fmt.Print(message)

				// 重新显示进度条（不包含换行，以便下次更新）
				fmt.Printf("\r[%sINF%s] 端口扫描进度: [%d / %d] 成功: \033[32m%d\033[0m 失败: \033[31m%d\033[0m",
					color.ColorBlue, color.ColorReset, current, totalTasks, found, failed)
				progressMutex.Unlock()

				// 记录到日志文件
				webscan.LogToFile(logFile, message)
				results = append(results, pr)

				// 检查端口数量是否超过阈值
				count, ok := ipPortCount[pr.IP]
				if ok {
					if count > DefaultPortsThreshold {
						BlackListLock.Lock()
						_, inBlack := BlackList[pr.IP]
						if !inBlack {
							BlackList[pr.IP] = struct{}{}
							gologger.Error().Msgf("%s 端口数量超出阈值,放弃扫描", pr.IP)
						}
						BlackListLock.Unlock()
					}
					ipPortCount[pr.IP] = count + 1
				} else {
					ipPortCount[pr.IP] = 1
				}
			}
		}
		close(done)
	}()

	// 准备端口扫描任务
	for _, port := range ports {
		addresses <- Address{IP: ip, Port: port}
	}
	close(addresses)

	// 启动进度条更新协程
	go func() {
		for {
			time.Sleep(200 * time.Millisecond)
			current := atomic.LoadInt32(&scannedPorts)
			found := atomic.LoadInt32(&foundPorts)
			failed := atomic.LoadInt32(&failedPorts)

			progressMutex.Lock()
			fmt.Printf("\r[%sINF%s] 端口扫描进度: [%d / %d] 成功: \033[32m%d\033[0m 失败: \033[31m%d\033[0m",
				color.ColorBlue, color.ColorReset, current, totalTasks, found, failed)
			progressMutex.Unlock()

			if current >= int32(totalTasks) {
				// 添加最后的换行符，避免后续日志追加到同一行
				fmt.Println()
				break
			}
		}
	}()

	// 执行端口扫描
	var wg sync.WaitGroup
	var id int32

	portScan := func(add Address) {
		defer func() {
			if r := recover(); r != nil {
				gologger.Error().Msgf("端口扫描发生错误: %v", r)
			}
			wg.Done()
		}()

		if ExitFunc {
			return
		}

		// 检查IP是否在黑名单中
		BlackListLock.Lock()
		_, inBlack := BlackList[add.IP]
		BlackListLock.Unlock()
		if inBlack {
			atomic.AddInt32(&scannedPorts, 1)
			return
		}

		pr := ConnectWithFingerprint(add.IP, add.Port, timeout, proxy, enableFingerprint)
		atomic.AddInt32(&id, 1)
		atomic.AddInt32(&scannedPorts, 1)

		if !pr.Status {
			atomic.AddInt32(&failedPorts, 1)
		} else {
			pr.IP = add.IP
			pr.Port = add.Port
			resultChan <- pr
		}
	}

	threadPool, _ := ants.NewPoolWithFunc(workers, func(ipaddr interface{}) {
		ipa := ipaddr.(Address)
		portScan(ipa)
	})
	defer threadPool.Release()

	for add := range addresses {
		if ExitFunc {
			return results
		}

		// 检查IP是否在黑名单中
		BlackListLock.Lock()
		_, inBlack := BlackList[add.IP]
		BlackListLock.Unlock()
		if inBlack {
			continue
		}

		wg.Add(1)
		threadPool.Invoke(add)
	}

	wg.Wait()
	close(resultChan)
	<-done

	return results
}

// 多IP并发端口扫描
func MultiIPScan(ips []string, ports []int, workers, timeout int, proxy clients.Proxy, logFile string, randomPort bool, enableFingerprint bool) []PortResult {
	// 如果需要随机化端口顺序
	if randomPort {
		r := rand.New(rand.NewSource(time.Now().UnixNano()))
		r.Shuffle(len(ports), func(i, j int) {
			ports[i], ports[j] = ports[j], ports[i]
		})
	}

	// 创建任务通道
	totalTasks := len(ips) * len(ports)
	addresses := make(chan Address, totalTasks)
	results := make([]PortResult, 0)
	resultChan := make(chan PortResult, workers) // 增加缓冲区大小，避免阻塞
	done := make(chan struct{})
	ipPortCount := make(map[string]int) // 记录IP开放端口数量

	// 添加进度统计变量
	var scannedPorts int32
	var foundPorts int32
	var failedPorts int32
	var progressMutex sync.Mutex // 进度显示互斥锁

	// 启动接收结果的协程
	go func() {
		for pr := range resultChan {
			if pr.Status {
				// 使用FormatPortOutput函数格式化输出
				message := FormatPortOutput(pr) + "\n"

				// 更新找到的端口数
				atomic.AddInt32(&foundPorts, 1)

				// 清除当前行，输出端口信息，然后恢复进度条
				progressMutex.Lock()
				current := atomic.LoadInt32(&scannedPorts)
				failed := atomic.LoadInt32(&failedPorts)
				found := atomic.LoadInt32(&foundPorts)

				// 清除当前进度行
				fmt.Print("\r                                                                               \r")

				// 输出端口信息
				fmt.Print(message)

				// 重新显示进度条（不包含换行，以便下次更新）
				fmt.Printf("\r[%sINF%s] 端口扫描进度: [%d / %d] 成功: \033[32m%d\033[0m 失败: \033[31m%d\033[0m",
					color.ColorBlue, color.ColorReset, current, totalTasks, found, failed)
				progressMutex.Unlock()

				// 记录到日志文件
				webscan.LogToFile(logFile, message)
				results = append(results, pr)

				// 检查端口数量是否超过阈值
				count, ok := ipPortCount[pr.IP]
				if ok {
					if count > DefaultPortsThreshold {
						BlackListLock.Lock()
						_, inBlack := BlackList[pr.IP]
						if !inBlack {
							BlackList[pr.IP] = struct{}{}
							gologger.Error().Msgf("%s 端口数量超出阈值,放弃扫描", pr.IP)
						}
						BlackListLock.Unlock()
					}
					ipPortCount[pr.IP] = count + 1
				} else {
					ipPortCount[pr.IP] = 1
				}
			}
		}
		close(done)
	}()

	// 准备所有IP的所有端口扫描任务
	for _, ip := range ips {
		for _, port := range ports {
			addresses <- Address{IP: ip, Port: port}
		}
	}
	close(addresses)

	// 启动进度条更新协程
	go func() {
		for {
			time.Sleep(200 * time.Millisecond)
			current := atomic.LoadInt32(&scannedPorts)
			found := atomic.LoadInt32(&foundPorts)
			failed := atomic.LoadInt32(&failedPorts)

			progressMutex.Lock()
			fmt.Printf("\r[%sINF%s] 端口扫描进度: [%d / %d] 成功: \033[32m%d\033[0m 失败: \033[31m%d\033[0m",
				color.ColorBlue, color.ColorReset, current, totalTasks, found, failed)
			progressMutex.Unlock()

			if current >= int32(totalTasks) {
				// 添加最后的换行符，避免后续日志追加到同一行
				fmt.Println()
				break
			}
		}
	}()

	// 执行端口扫描
	var wg sync.WaitGroup
	var id int32

	portScan := func(add Address) {
		defer func() {
			if r := recover(); r != nil {
				gologger.Error().Msgf("端口扫描发生错误: %v", r)
			}
			wg.Done()
		}()

		if ExitFunc {
			return
		}

		// 检查IP是否在黑名单中
		BlackListLock.Lock()
		_, inBlack := BlackList[add.IP]
		BlackListLock.Unlock()
		if inBlack {
			atomic.AddInt32(&scannedPorts, 1)
			return
		}

		pr := ConnectWithFingerprint(add.IP, add.Port, timeout, proxy, enableFingerprint)
		atomic.AddInt32(&id, 1)
		atomic.AddInt32(&scannedPorts, 1)

		if !pr.Status {
			atomic.AddInt32(&failedPorts, 1)
		} else {
			pr.IP = add.IP
			pr.Port = add.Port
			resultChan <- pr
		}
	}

	threadPool, _ := ants.NewPoolWithFunc(workers, func(ipaddr interface{}) {
		ipa := ipaddr.(Address)
		portScan(ipa)
	})
	defer threadPool.Release()

	for add := range addresses {
		if ExitFunc {
			return results
		}

		// 检查IP是否在黑名单中
		BlackListLock.Lock()
		_, inBlack := BlackList[add.IP]
		BlackListLock.Unlock()
		if inBlack {
			continue
		}

		wg.Add(1)
		threadPool.Invoke(add)
	}

	wg.Wait()
	close(resultChan)
	<-done

	return results
}

func TcpScan(ctx context.Context, addresses <-chan Address, workers, timeout int, proxy clients.Proxy, logFile string, enableFingerprint bool) []PortResult {
	var id int32
	var results []PortResult
	single := make(chan struct{})
	retChan := make(chan PortResult, workers) // 增加缓冲区大小，避免阻塞
	var wg sync.WaitGroup
	openPorts := make(map[string]bool)  // 记录开放的端口
	ipPortCount := make(map[string]int) // 记录IP开放端口数量

	// 统计任务总数
	var totalTasks int32

	// 添加进度统计变量
	var scannedPorts int32
	var foundPorts int32
	var failedPorts int32
	var progressMutex sync.Mutex // 进度显示互斥锁

	go func() {
		for pr := range retChan {
			if pr.Status {
				// 使用FormatPortOutput函数格式化输出
				message := FormatPortOutput(pr) + "\n"

				// 更新找到的端口数
				atomic.AddInt32(&foundPorts, 1)

				// 清除当前行，输出端口信息，然后恢复进度条
				progressMutex.Lock()
				current := atomic.LoadInt32(&scannedPorts)
				failed := atomic.LoadInt32(&failedPorts)
				found := atomic.LoadInt32(&foundPorts)
				total := atomic.LoadInt32(&totalTasks)

				// 清除当前进度行
				fmt.Print("\r                                                                               \r")

				// 输出端口信息
				fmt.Print(message)

				// 重新显示进度条（不包含换行，以便下次更新）
				fmt.Printf("\r[%sINF%s] 端口扫描进度: [%d / %d] 成功: \033[32m%d\033[0m 失败: \033[31m%d\033[0m",
					color.ColorBlue, color.ColorReset, current, total, found, failed)
				progressMutex.Unlock()

				// 记录到日志文件
				webscan.LogToFile(logFile, message)
				results = append(results, pr)

				// 检查端口数量是否超过阈值
				count, ok := ipPortCount[pr.IP]
				if ok {
					if count > DefaultPortsThreshold {
						BlackListLock.Lock()
						_, inBlack := BlackList[pr.IP]
						if !inBlack {
							BlackList[pr.IP] = struct{}{}
							gologger.Error().Msgf("%s 端口数量超出阈值,放弃扫描", pr.IP)
						}
						BlackListLock.Unlock()
					}
					ipPortCount[pr.IP] = count + 1
				} else {
					ipPortCount[pr.IP] = 1
				}
			}
		}
		close(single)
	}()

	// 复制地址通道以获取任务总数(因为addresses是只读通道)
	addrCopy := make([]Address, 0, 1000)
	for add := range addresses {
		addrCopy = append(addrCopy, add)
		atomic.AddInt32(&totalTasks, 1)
	}

	// 创建新的地址通道
	addrChan := make(chan Address, len(addrCopy))
	for _, add := range addrCopy {
		addrChan <- add
	}
	close(addrChan)

	// 启动进度条更新协程
	go func() {
		for {
			time.Sleep(200 * time.Millisecond)
			current := atomic.LoadInt32(&scannedPorts)
			found := atomic.LoadInt32(&foundPorts)
			failed := atomic.LoadInt32(&failedPorts)
			total := atomic.LoadInt32(&totalTasks)

			if total > 0 {
				progressMutex.Lock()
				fmt.Printf("\r[%sINF%s] 端口扫描进度: [%d / %d] 成功: \033[32m%d\033[0m 失败: \033[31m%d\033[0m",
					color.ColorBlue, color.ColorReset, current, total, found, failed)
				progressMutex.Unlock()
			}

			if current >= total && total > 0 {
				// 添加最后的换行符，避免后续日志追加到同一行
				fmt.Println()
				break
			}
		}
	}()

	// port scan func
	portScan := func(add Address) {
		defer func() {
			if r := recover(); r != nil {
				gologger.Error().Msgf("端口扫描发生错误: %v", r)
			}
			wg.Done()
		}()

		if ExitFunc {
			return
		}

		// 检查IP是否在黑名单中
		BlackListLock.Lock()
		_, inBlack := BlackList[add.IP]
		BlackListLock.Unlock()
		if inBlack {
			atomic.AddInt32(&scannedPorts, 1)
			return
		}

		pr := ConnectWithFingerprint(add.IP, add.Port, timeout, proxy, enableFingerprint)
		// 检查1-10端口的开放情况
		if pr.Port >= 1 && pr.Port <= 20 && pr.Status {
			openPorts[pr.IP] = true // 记录该IP有开放端口
		} else {
			// 如果该IP在1-10端口有开放，后续端口必须识别到服务
			if openPorts[pr.IP] && !pr.Status {
				atomic.AddInt32(&scannedPorts, 1)
				atomic.AddInt32(&failedPorts, 1)
				return // 如果没有识别到服务，则不返回
			}
		}

		atomic.AddInt32(&id, 1)
		atomic.AddInt32(&scannedPorts, 1)

		if !pr.Status {
			atomic.AddInt32(&failedPorts, 1)
		} else {
			pr.IP = add.IP
			pr.Port = add.Port
			retChan <- pr
		}
	}

	threadPool, _ := ants.NewPoolWithFunc(workers, func(ipaddr interface{}) {
		ipa := ipaddr.(Address)
		portScan(ipa)
	})
	defer threadPool.Release()

	for add := range addrChan {
		if ExitFunc {
			return results
		}

		// 检查IP是否在黑名单中
		BlackListLock.Lock()
		_, inBlack := BlackList[add.IP]
		BlackListLock.Unlock()
		if inBlack {
			continue
		}

		wg.Add(1)
		threadPool.Invoke(add)
	}

	wg.Wait()
	close(retChan)
	<-single

	return results
}

// ClearProgress 清除进度显示
func ClearProgress() {
	fmt.Print("\r                                                                               \r")
}

// ScanAndOutputResult 执行完整的端口扫描并输出结果
func ScanAndOutputResult(ips []string, portRange string, excludePortRange string, workers, timeout int, proxy string, logFile string, randomPort bool, portsThreshold int, reportPath string, enableFingerprint bool) []PortResult {
	// 使用默认参数
	if workers <= 0 {
		workers = DefaultTCPThreads
		gologger.Info().Msgf("使用默认TCP扫描线程数: %d", DefaultTCPThreads)
	}

	if timeout <= 0 {
		timeout = DefaultTCPTimeout
		gologger.Info().Msgf("使用默认TCP扫描超时时间: %d秒", DefaultTCPTimeout)
	}

	if portsThreshold <= 0 {
		portsThreshold = DefaultPortsThreshold
		gologger.Info().Msgf("使用默认端口阈值: %d", DefaultPortsThreshold)
	}

	gologger.Info().Msg("端口扫描开始，使用的端口范围: " + portRange)
	if excludePortRange != "" {
		gologger.Info().Msg("排除的端口范围: " + excludePortRange)
	}
	gologger.Info().Msgf("TCP扫描线程数: %d, 超时时间: %d秒, 端口阈值: %d", workers, timeout, portsThreshold)

	// 解析代理配置
	proxyConfig := ParseProxyURL(proxy)

	// 解析端口范围
	ports := ParsePortRange(portRange, excludePortRange)

	// 使用多IP并发扫描
	gologger.Info().Msgf("开始扫描 %d 个IP的 %d 个端口，共 %d 个任务", len(ips), len(ports), len(ips)*len(ports))
	if randomPort {
		gologger.Info().Msg("已启用端口随机化扫描")
	}

	// 输出指纹识别状态
	if enableFingerprint {
		gologger.Info().Msg("已启用服务指纹识别")
	} else {
		gologger.Info().Msg("已禁用服务指纹识别，使用端口默认服务映射")
	}

	// 使用新的多IP并发扫描方法
	results := MultiIPScan(ips, ports, workers, timeout, proxyConfig, logFile, randomPort, enableFingerprint)

	// 过滤掉可能是防火墙的IP
	filteredResults := FilterFirewallResults(results, portsThreshold)

	// 如果指定了报告路径，将结果写入报告
	if reportPath != "" {
		// 转换为报告所需的结构体
		portScanResults := ConvertToPortScanResults(filteredResults)

		// 检查报告文件是否存在，如果不存在则先初始化
		if _, err := os.Stat(reportPath); os.IsNotExist(err) {
			// 初始化报告文件，忽略可能的错误
			report.InitReport(reportPath, nil)
		}

		// 尝试更新报告，即使初始化失败也继续执行
		report.UpdatePortScanResults(reportPath, portScanResults)
	}

	// 清除可能残留的进度显示
	ClearProgress()

	// 输出结果
	gologger.Info().Msgf("端口扫描完成，共发现 %d 个开放端口", len(filteredResults))

	return filteredResults
}

// FilterFirewallResults 过滤掉可能是防火墙的IP的结果
func FilterFirewallResults(results []PortResult, portsThreshold int) []PortResult {
	// 统计每个IP的端口数量
	ipPortCount := make(map[string]int)
	for _, result := range results {
		ipPortCount[result.IP]++
	}

	// 过滤结果
	var filteredResults []PortResult
	for _, result := range results {
		// 如果IP的端口数量超过阈值，认为可能是防火墙，不返回结果
		if ipPortCount[result.IP] <= portsThreshold {
			filteredResults = append(filteredResults, result)
		} else {
			gologger.Warning().Msgf("IP %s 的端口数量 %d 超过阈值 %d，可能是防火墙，已过滤",
				result.IP, ipPortCount[result.IP], portsThreshold)
		}
	}

	return filteredResults
}

// ConvertToPortScanResults 将PortResult数组转换为structs.PortScanResult数组
func ConvertToPortScanResults(results []PortResult) []structs.PortScanResult {
	portScanResults := make([]structs.PortScanResult, 0, len(results))

	for _, result := range results {
		portScanResult := structs.PortScanResult{
			IP:        result.IP,
			Port:      result.Port,
			Server:    result.Server,
			Banner:    result.Banner,
			Product:   result.Product,
			Version:   result.Version,
			HttpTitle: result.HttpTitle,
		}
		portScanResults = append(portScanResults, portScanResult)
	}

	return portScanResults
}

// GetAvailablePortLists 获取所有可用的预定义端口列表名称
func GetAvailablePortLists() []string {
	if common.GlobalPortListConfig == nil {
		return []string{"default"}
	}

	lists := make([]string, 0, len(common.GlobalPortListConfig.PortMap))
	for name := range common.GlobalPortListConfig.PortMap {
		lists = append(lists, name)
	}
	return lists
}

// GetPortListInfo 获取指定端口列表的详细信息
func GetPortListInfo(listName string, excludePortRange string) ([]int, bool) {
	var ports []int
	var exists bool

	if common.GlobalPortListConfig == nil {
		if listName == "default" {
			ports = CommonPorts
			exists = true
		}
	} else {
		ports, exists = common.GlobalPortListConfig.PortMap[listName]
	}

	// 如果没有找到端口列表或者没有排除端口，直接返回
	if !exists || excludePortRange == "" {
		return ports, exists
	}

	// 解析排除的端口列表
	excludePorts := ParsePort(excludePortRange)

	// 如果没有排除的端口，直接返回
	if len(excludePorts) == 0 {
		return ports, exists
	}

	// 创建排除端口的映射，用于快速查找
	excludeMap := make(map[int]struct{})
	for _, port := range excludePorts {
		excludeMap[port] = struct{}{}
	}

	// 过滤掉排除的端口
	var resultPorts []int
	for _, port := range ports {
		if _, excluded := excludeMap[port]; !excluded {
			resultPorts = append(resultPorts, port)
		}
	}

	return resultPorts, exists
}
