// This file was automatically generated by https://github.com/kbinani/win/blob/generator/internal/cmd/gen/gen.go
// go run internal/cmd/gen/gen.go

// +build windows

package win

import (
	"syscall"
	"unsafe"
)

var (
	// Library
	libwinmm uintptr

	// Functions
	closeDriver                 uintptr
	defDriverProc               uintptr
	drvGetModuleHandle          uintptr
	getDriverModuleHandle       uintptr
	openDriver                  uintptr
	playSound                   uintptr
	sendDriverMessage           uintptr
	auxGetDevCaps               uintptr
	auxGetNumDevs               uintptr
	auxGetVolume                uintptr
	auxOutMessage               uintptr
	auxSetVolume                uintptr
	joyGetDevCaps               uintptr
	joyGetNumDevs               uintptr
	joyGetPos                   uintptr
	joyGetPosEx                 uintptr
	joyGetThreshold             uintptr
	joyReleaseCapture           uintptr
	joySetCapture               uintptr
	joySetThreshold             uintptr
	mciGetCreatorTask           uintptr
	mciGetDeviceIDFromElementID uintptr
	mciGetDeviceID              uintptr
	mciGetErrorString           uintptr
	mciGetYieldProc             uintptr
	mciSendCommand              uintptr
	mciSendString               uintptr
	mciSetYieldProc             uintptr
	midiConnect                 uintptr
	midiDisconnect              uintptr
	midiInAddBuffer             uintptr
	midiInClose                 uintptr
	midiInGetDevCaps            uintptr
	midiInGetErrorText          uintptr
	midiInGetID                 uintptr
	midiInGetNumDevs            uintptr
	midiInMessage               uintptr
	midiInOpen                  uintptr
	midiInPrepareHeader         uintptr
	midiInReset                 uintptr
	midiInStart                 uintptr
	midiInStop                  uintptr
	midiInUnprepareHeader       uintptr
	midiOutCacheDrumPatches     uintptr
	midiOutCachePatches         uintptr
	midiOutClose                uintptr
	midiOutGetDevCaps           uintptr
	midiOutGetErrorText         uintptr
	midiOutGetID                uintptr
	midiOutGetNumDevs           uintptr
	midiOutGetVolume            uintptr
	midiOutLongMsg              uintptr
	midiOutMessage              uintptr
	midiOutOpen                 uintptr
	midiOutPrepareHeader        uintptr
	midiOutReset                uintptr
	midiOutSetVolume            uintptr
	midiOutShortMsg             uintptr
	midiOutUnprepareHeader      uintptr
	midiStreamClose             uintptr
	midiStreamOpen              uintptr
	midiStreamOut               uintptr
	midiStreamPause             uintptr
	midiStreamPosition          uintptr
	midiStreamProperty          uintptr
	midiStreamRestart           uintptr
	midiStreamStop              uintptr
	mixerClose                  uintptr
	mixerGetControlDetails      uintptr
	mixerGetDevCaps             uintptr
	mixerGetID                  uintptr
	mixerGetLineControls        uintptr
	mixerGetLineInfo            uintptr
	mixerGetNumDevs             uintptr
	mixerMessage                uintptr
	mixerOpen                   uintptr
	mixerSetControlDetails      uintptr
	mmioAdvance                 uintptr
	mmioAscend                  uintptr
	mmioClose                   uintptr
	mmioCreateChunk             uintptr
	mmioDescend                 uintptr
	mmioFlush                   uintptr
	mmioGetInfo                 uintptr
	mmioInstallIOProc           uintptr
	mmioOpen                    uintptr
	mmioRead                    uintptr
	mmioRename                  uintptr
	mmioSeek                    uintptr
	mmioSendMessage             uintptr
	mmioSetBuffer               uintptr
	mmioSetInfo                 uintptr
	mmioStringToFOURCC          uintptr
	mmioWrite                   uintptr
	sndPlaySound                uintptr
	timeBeginPeriod             uintptr
	timeEndPeriod               uintptr
	timeGetDevCaps              uintptr
	timeGetSystemTime           uintptr
	timeGetTime                 uintptr
	timeKillEvent               uintptr
	timeSetEvent                uintptr
	waveInAddBuffer             uintptr
	waveInClose                 uintptr
	waveInGetDevCaps            uintptr
	waveInGetErrorText          uintptr
	waveInGetID                 uintptr
	waveInGetNumDevs            uintptr
	waveInGetPosition           uintptr
	waveInMessage               uintptr
	waveInOpen                  uintptr
	waveInPrepareHeader         uintptr
	waveInReset                 uintptr
	waveInStart                 uintptr
	waveInStop                  uintptr
	waveInUnprepareHeader       uintptr
	waveOutBreakLoop            uintptr
	waveOutClose                uintptr
	waveOutGetDevCaps           uintptr
	waveOutGetErrorText         uintptr
	waveOutGetID                uintptr
	waveOutGetNumDevs           uintptr
	waveOutGetPitch             uintptr
	waveOutGetPlaybackRate      uintptr
	waveOutGetPosition          uintptr
	waveOutGetVolume            uintptr
	waveOutMessage              uintptr
	waveOutOpen                 uintptr
	waveOutPause                uintptr
	waveOutPrepareHeader        uintptr
	waveOutReset                uintptr
	waveOutRestart              uintptr
	waveOutSetPitch             uintptr
	waveOutSetPlaybackRate      uintptr
	waveOutSetVolume            uintptr
	waveOutUnprepareHeader      uintptr
	waveOutWrite                uintptr
	driverCallback              uintptr
	joyConfigChanged            uintptr
	mciDriverNotify             uintptr
	mciDriverYield              uintptr
	mciExecute                  uintptr
	mciFreeCommandResource      uintptr
	mciGetDriverData            uintptr
	mciLoadCommandResource      uintptr
	mciSetDriverData            uintptr
	mmGetCurrentTask            uintptr
	mmTaskBlock                 uintptr
	mmTaskCreate                uintptr
	mmTaskSignal                uintptr
	mmTaskYield                 uintptr
	mmsystemGetVersion          uintptr
)

func init() {
	// Library
	libwinmm = doLoadLibrary("winmm.dll")

	// Functions
	closeDriver = doGetProcAddress(libwinmm, "CloseDriver")
	defDriverProc = doGetProcAddress(libwinmm, "DefDriverProc")
	drvGetModuleHandle = doGetProcAddress(libwinmm, "DrvGetModuleHandle")
	getDriverModuleHandle = doGetProcAddress(libwinmm, "GetDriverModuleHandle")
	openDriver = doGetProcAddress(libwinmm, "OpenDriver")
	playSound = doGetProcAddress(libwinmm, "PlaySoundW")
	sendDriverMessage = doGetProcAddress(libwinmm, "SendDriverMessage")
	auxGetDevCaps = doGetProcAddress(libwinmm, "auxGetDevCapsW")
	auxGetNumDevs = doGetProcAddress(libwinmm, "auxGetNumDevs")
	auxGetVolume = doGetProcAddress(libwinmm, "auxGetVolume")
	auxOutMessage = doGetProcAddress(libwinmm, "auxOutMessage")
	auxSetVolume = doGetProcAddress(libwinmm, "auxSetVolume")
	joyGetDevCaps = doGetProcAddress(libwinmm, "joyGetDevCapsW")
	joyGetNumDevs = doGetProcAddress(libwinmm, "joyGetNumDevs")
	joyGetPos = doGetProcAddress(libwinmm, "joyGetPos")
	joyGetPosEx = doGetProcAddress(libwinmm, "joyGetPosEx")
	joyGetThreshold = doGetProcAddress(libwinmm, "joyGetThreshold")
	joyReleaseCapture = doGetProcAddress(libwinmm, "joyReleaseCapture")
	joySetCapture = doGetProcAddress(libwinmm, "joySetCapture")
	joySetThreshold = doGetProcAddress(libwinmm, "joySetThreshold")
	mciGetCreatorTask = doGetProcAddress(libwinmm, "mciGetCreatorTask")
	mciGetDeviceIDFromElementID = doGetProcAddress(libwinmm, "mciGetDeviceIDFromElementIDW")
	mciGetDeviceID = doGetProcAddress(libwinmm, "mciGetDeviceIDW")
	mciGetErrorString = doGetProcAddress(libwinmm, "mciGetErrorStringW")
	mciGetYieldProc = doGetProcAddress(libwinmm, "mciGetYieldProc")
	mciSendCommand = doGetProcAddress(libwinmm, "mciSendCommandW")
	mciSendString = doGetProcAddress(libwinmm, "mciSendStringW")
	mciSetYieldProc = doGetProcAddress(libwinmm, "mciSetYieldProc")
	midiConnect = doGetProcAddress(libwinmm, "midiConnect")
	midiDisconnect = doGetProcAddress(libwinmm, "midiDisconnect")
	midiInAddBuffer = doGetProcAddress(libwinmm, "midiInAddBuffer")
	midiInClose = doGetProcAddress(libwinmm, "midiInClose")
	midiInGetDevCaps = doGetProcAddress(libwinmm, "midiInGetDevCapsW")
	midiInGetErrorText = doGetProcAddress(libwinmm, "midiInGetErrorTextW")
	midiInGetID = doGetProcAddress(libwinmm, "midiInGetID")
	midiInGetNumDevs = doGetProcAddress(libwinmm, "midiInGetNumDevs")
	midiInMessage = doGetProcAddress(libwinmm, "midiInMessage")
	midiInOpen = doGetProcAddress(libwinmm, "midiInOpen")
	midiInPrepareHeader = doGetProcAddress(libwinmm, "midiInPrepareHeader")
	midiInReset = doGetProcAddress(libwinmm, "midiInReset")
	midiInStart = doGetProcAddress(libwinmm, "midiInStart")
	midiInStop = doGetProcAddress(libwinmm, "midiInStop")
	midiInUnprepareHeader = doGetProcAddress(libwinmm, "midiInUnprepareHeader")
	midiOutCacheDrumPatches = doGetProcAddress(libwinmm, "midiOutCacheDrumPatches")
	midiOutCachePatches = doGetProcAddress(libwinmm, "midiOutCachePatches")
	midiOutClose = doGetProcAddress(libwinmm, "midiOutClose")
	midiOutGetDevCaps = doGetProcAddress(libwinmm, "midiOutGetDevCapsW")
	midiOutGetErrorText = doGetProcAddress(libwinmm, "midiOutGetErrorTextW")
	midiOutGetID = doGetProcAddress(libwinmm, "midiOutGetID")
	midiOutGetNumDevs = doGetProcAddress(libwinmm, "midiOutGetNumDevs")
	midiOutGetVolume = doGetProcAddress(libwinmm, "midiOutGetVolume")
	midiOutLongMsg = doGetProcAddress(libwinmm, "midiOutLongMsg")
	midiOutMessage = doGetProcAddress(libwinmm, "midiOutMessage")
	midiOutOpen = doGetProcAddress(libwinmm, "midiOutOpen")
	midiOutPrepareHeader = doGetProcAddress(libwinmm, "midiOutPrepareHeader")
	midiOutReset = doGetProcAddress(libwinmm, "midiOutReset")
	midiOutSetVolume = doGetProcAddress(libwinmm, "midiOutSetVolume")
	midiOutShortMsg = doGetProcAddress(libwinmm, "midiOutShortMsg")
	midiOutUnprepareHeader = doGetProcAddress(libwinmm, "midiOutUnprepareHeader")
	midiStreamClose = doGetProcAddress(libwinmm, "midiStreamClose")
	midiStreamOpen = doGetProcAddress(libwinmm, "midiStreamOpen")
	midiStreamOut = doGetProcAddress(libwinmm, "midiStreamOut")
	midiStreamPause = doGetProcAddress(libwinmm, "midiStreamPause")
	midiStreamPosition = doGetProcAddress(libwinmm, "midiStreamPosition")
	midiStreamProperty = doGetProcAddress(libwinmm, "midiStreamProperty")
	midiStreamRestart = doGetProcAddress(libwinmm, "midiStreamRestart")
	midiStreamStop = doGetProcAddress(libwinmm, "midiStreamStop")
	mixerClose = doGetProcAddress(libwinmm, "mixerClose")
	mixerGetControlDetails = doGetProcAddress(libwinmm, "mixerGetControlDetailsW")
	mixerGetDevCaps = doGetProcAddress(libwinmm, "mixerGetDevCapsW")
	mixerGetID = doGetProcAddress(libwinmm, "mixerGetID")
	mixerGetLineControls = doGetProcAddress(libwinmm, "mixerGetLineControlsW")
	mixerGetLineInfo = doGetProcAddress(libwinmm, "mixerGetLineInfoW")
	mixerGetNumDevs = doGetProcAddress(libwinmm, "mixerGetNumDevs")
	mixerMessage = doGetProcAddress(libwinmm, "mixerMessage")
	mixerOpen = doGetProcAddress(libwinmm, "mixerOpen")
	mixerSetControlDetails = doGetProcAddress(libwinmm, "mixerSetControlDetails")
	mmioAdvance = doGetProcAddress(libwinmm, "mmioAdvance")
	mmioAscend = doGetProcAddress(libwinmm, "mmioAscend")
	mmioClose = doGetProcAddress(libwinmm, "mmioClose")
	mmioCreateChunk = doGetProcAddress(libwinmm, "mmioCreateChunk")
	mmioDescend = doGetProcAddress(libwinmm, "mmioDescend")
	mmioFlush = doGetProcAddress(libwinmm, "mmioFlush")
	mmioGetInfo = doGetProcAddress(libwinmm, "mmioGetInfo")
	mmioInstallIOProc = doGetProcAddress(libwinmm, "mmioInstallIOProcW")
	mmioOpen = doGetProcAddress(libwinmm, "mmioOpenW")
	mmioRead = doGetProcAddress(libwinmm, "mmioRead")
	mmioRename = doGetProcAddress(libwinmm, "mmioRenameW")
	mmioSeek = doGetProcAddress(libwinmm, "mmioSeek")
	mmioSendMessage = doGetProcAddress(libwinmm, "mmioSendMessage")
	mmioSetBuffer = doGetProcAddress(libwinmm, "mmioSetBuffer")
	mmioSetInfo = doGetProcAddress(libwinmm, "mmioSetInfo")
	mmioStringToFOURCC = doGetProcAddress(libwinmm, "mmioStringToFOURCCW")
	mmioWrite = doGetProcAddress(libwinmm, "mmioWrite")
	sndPlaySound = doGetProcAddress(libwinmm, "sndPlaySoundW")
	timeBeginPeriod = doGetProcAddress(libwinmm, "timeBeginPeriod")
	timeEndPeriod = doGetProcAddress(libwinmm, "timeEndPeriod")
	timeGetDevCaps = doGetProcAddress(libwinmm, "timeGetDevCaps")
	timeGetSystemTime = doGetProcAddress(libwinmm, "timeGetSystemTime")
	timeGetTime = doGetProcAddress(libwinmm, "timeGetTime")
	timeKillEvent = doGetProcAddress(libwinmm, "timeKillEvent")
	timeSetEvent = doGetProcAddress(libwinmm, "timeSetEvent")
	waveInAddBuffer = doGetProcAddress(libwinmm, "waveInAddBuffer")
	waveInClose = doGetProcAddress(libwinmm, "waveInClose")
	waveInGetDevCaps = doGetProcAddress(libwinmm, "waveInGetDevCapsW")
	waveInGetErrorText = doGetProcAddress(libwinmm, "waveInGetErrorTextW")
	waveInGetID = doGetProcAddress(libwinmm, "waveInGetID")
	waveInGetNumDevs = doGetProcAddress(libwinmm, "waveInGetNumDevs")
	waveInGetPosition = doGetProcAddress(libwinmm, "waveInGetPosition")
	waveInMessage = doGetProcAddress(libwinmm, "waveInMessage")
	waveInOpen = doGetProcAddress(libwinmm, "waveInOpen")
	waveInPrepareHeader = doGetProcAddress(libwinmm, "waveInPrepareHeader")
	waveInReset = doGetProcAddress(libwinmm, "waveInReset")
	waveInStart = doGetProcAddress(libwinmm, "waveInStart")
	waveInStop = doGetProcAddress(libwinmm, "waveInStop")
	waveInUnprepareHeader = doGetProcAddress(libwinmm, "waveInUnprepareHeader")
	waveOutBreakLoop = doGetProcAddress(libwinmm, "waveOutBreakLoop")
	waveOutClose = doGetProcAddress(libwinmm, "waveOutClose")
	waveOutGetDevCaps = doGetProcAddress(libwinmm, "waveOutGetDevCapsW")
	waveOutGetErrorText = doGetProcAddress(libwinmm, "waveOutGetErrorTextW")
	waveOutGetID = doGetProcAddress(libwinmm, "waveOutGetID")
	waveOutGetNumDevs = doGetProcAddress(libwinmm, "waveOutGetNumDevs")
	waveOutGetPitch = doGetProcAddress(libwinmm, "waveOutGetPitch")
	waveOutGetPlaybackRate = doGetProcAddress(libwinmm, "waveOutGetPlaybackRate")
	waveOutGetPosition = doGetProcAddress(libwinmm, "waveOutGetPosition")
	waveOutGetVolume = doGetProcAddress(libwinmm, "waveOutGetVolume")
	waveOutMessage = doGetProcAddress(libwinmm, "waveOutMessage")
	waveOutOpen = doGetProcAddress(libwinmm, "waveOutOpen")
	waveOutPause = doGetProcAddress(libwinmm, "waveOutPause")
	waveOutPrepareHeader = doGetProcAddress(libwinmm, "waveOutPrepareHeader")
	waveOutReset = doGetProcAddress(libwinmm, "waveOutReset")
	waveOutRestart = doGetProcAddress(libwinmm, "waveOutRestart")
	waveOutSetPitch = doGetProcAddress(libwinmm, "waveOutSetPitch")
	waveOutSetPlaybackRate = doGetProcAddress(libwinmm, "waveOutSetPlaybackRate")
	waveOutSetVolume = doGetProcAddress(libwinmm, "waveOutSetVolume")
	waveOutUnprepareHeader = doGetProcAddress(libwinmm, "waveOutUnprepareHeader")
	waveOutWrite = doGetProcAddress(libwinmm, "waveOutWrite")
	driverCallback = doGetProcAddress(libwinmm, "DriverCallback")
	joyConfigChanged = doGetProcAddress(libwinmm, "joyConfigChanged")
	mciDriverNotify = doGetProcAddress(libwinmm, "mciDriverNotify")
	mciDriverYield = doGetProcAddress(libwinmm, "mciDriverYield")
	mciExecute = doGetProcAddress(libwinmm, "mciExecute")
	mciFreeCommandResource = doGetProcAddress(libwinmm, "mciFreeCommandResource")
	mciGetDriverData = doGetProcAddress(libwinmm, "mciGetDriverData")
	mciLoadCommandResource = doGetProcAddress(libwinmm, "mciLoadCommandResource")
	mciSetDriverData = doGetProcAddress(libwinmm, "mciSetDriverData")
	mmGetCurrentTask = doGetProcAddress(libwinmm, "mmGetCurrentTask")
	mmTaskBlock = doGetProcAddress(libwinmm, "mmTaskBlock")
	mmTaskCreate = doGetProcAddress(libwinmm, "mmTaskCreate")
	mmTaskSignal = doGetProcAddress(libwinmm, "mmTaskSignal")
	mmTaskYield = doGetProcAddress(libwinmm, "mmTaskYield")
	mmsystemGetVersion = doGetProcAddress(libwinmm, "mmsystemGetVersion")
}

func CloseDriver(hDriver HDRVR, lParam1 LPARAM, lParam2 LPARAM) LRESULT {
	ret1 := syscall3(closeDriver, 3,
		uintptr(hDriver),
		uintptr(lParam1),
		uintptr(lParam2))
	return LRESULT(ret1)
}

func DefDriverProc(dwDriverIdentifier *uint32, hdrvr HDRVR, uMsg UINT, lParam1 LPARAM, lParam2 LPARAM) LRESULT {
	ret1 := syscall6(defDriverProc, 5,
		uintptr(unsafe.Pointer(dwDriverIdentifier)),
		uintptr(hdrvr),
		uintptr(uMsg),
		uintptr(lParam1),
		uintptr(lParam2),
		0)
	return LRESULT(ret1)
}

func DrvGetModuleHandle(hDriver HDRVR) HMODULE {
	ret1 := syscall3(drvGetModuleHandle, 1,
		uintptr(hDriver),
		0,
		0)
	return HMODULE(ret1)
}

func GetDriverModuleHandle(hDriver HDRVR) HMODULE {
	ret1 := syscall3(getDriverModuleHandle, 1,
		uintptr(hDriver),
		0,
		0)
	return HMODULE(ret1)
}

func OpenDriver(szDriverName string, szSectionName string, lParam2 LPARAM) HDRVR {
	szDriverNameStr := unicode16FromString(szDriverName)
	szSectionNameStr := unicode16FromString(szSectionName)
	ret1 := syscall3(openDriver, 3,
		uintptr(unsafe.Pointer(&szDriverNameStr[0])),
		uintptr(unsafe.Pointer(&szSectionNameStr[0])),
		uintptr(lParam2))
	return HDRVR(ret1)
}

func PlaySound(pszSound string, hmod HMODULE, fdwSound DWORD) bool {
	pszSoundStr := unicode16FromString(pszSound)
	ret1 := syscall3(playSound, 3,
		uintptr(unsafe.Pointer(&pszSoundStr[0])),
		uintptr(hmod),
		uintptr(fdwSound))
	return ret1 != 0
}

func SendDriverMessage(hDriver HDRVR, message UINT, lParam1 LPARAM, lParam2 LPARAM) LRESULT {
	ret1 := syscall6(sendDriverMessage, 4,
		uintptr(hDriver),
		uintptr(message),
		uintptr(lParam1),
		uintptr(lParam2),
		0,
		0)
	return LRESULT(ret1)
}

func AuxGetDevCaps(uDeviceID *uint32, pac *AUXCAPS, cbac UINT) MMRESULT {
	ret1 := syscall3(auxGetDevCaps, 3,
		uintptr(unsafe.Pointer(uDeviceID)),
		uintptr(unsafe.Pointer(pac)),
		uintptr(cbac))
	return MMRESULT(ret1)
}

func AuxGetNumDevs() UINT {
	ret1 := syscall3(auxGetNumDevs, 0,
		0,
		0,
		0)
	return UINT(ret1)
}

func AuxGetVolume(uDeviceID UINT, pdwVolume *uint32) MMRESULT {
	ret1 := syscall3(auxGetVolume, 2,
		uintptr(uDeviceID),
		uintptr(unsafe.Pointer(pdwVolume)),
		0)
	return MMRESULT(ret1)
}

func AuxOutMessage(uDeviceID UINT, uMsg UINT, dw1 *uint32, dw2 *uint32) MMRESULT {
	ret1 := syscall6(auxOutMessage, 4,
		uintptr(uDeviceID),
		uintptr(uMsg),
		uintptr(unsafe.Pointer(dw1)),
		uintptr(unsafe.Pointer(dw2)),
		0,
		0)
	return MMRESULT(ret1)
}

func AuxSetVolume(uDeviceID UINT, dwVolume DWORD) MMRESULT {
	ret1 := syscall3(auxSetVolume, 2,
		uintptr(uDeviceID),
		uintptr(dwVolume),
		0)
	return MMRESULT(ret1)
}

func JoyGetDevCaps(uJoyID *uint32, pjc LPJOYCAPS, cbjc UINT) MMRESULT {
	ret1 := syscall3(joyGetDevCaps, 3,
		uintptr(unsafe.Pointer(uJoyID)),
		uintptr(unsafe.Pointer(pjc)),
		uintptr(cbjc))
	return MMRESULT(ret1)
}

func JoyGetNumDevs() UINT {
	ret1 := syscall3(joyGetNumDevs, 0,
		0,
		0,
		0)
	return UINT(ret1)
}

func JoyGetPos(uJoyID UINT, pji LPJOYINFO) MMRESULT {
	ret1 := syscall3(joyGetPos, 2,
		uintptr(uJoyID),
		uintptr(unsafe.Pointer(pji)),
		0)
	return MMRESULT(ret1)
}

func JoyGetPosEx(uJoyID UINT, pji *JOYINFOEX) MMRESULT {
	ret1 := syscall3(joyGetPosEx, 2,
		uintptr(uJoyID),
		uintptr(unsafe.Pointer(pji)),
		0)
	return MMRESULT(ret1)
}

func JoyGetThreshold(uJoyID UINT, puThreshold *UINT) MMRESULT {
	ret1 := syscall3(joyGetThreshold, 2,
		uintptr(uJoyID),
		uintptr(unsafe.Pointer(puThreshold)),
		0)
	return MMRESULT(ret1)
}

func JoyReleaseCapture(uJoyID UINT) MMRESULT {
	ret1 := syscall3(joyReleaseCapture, 1,
		uintptr(uJoyID),
		0,
		0)
	return MMRESULT(ret1)
}

func JoySetCapture(hwnd HWND, uJoyID UINT, uPeriod UINT, fChanged bool) MMRESULT {
	ret1 := syscall6(joySetCapture, 4,
		uintptr(hwnd),
		uintptr(uJoyID),
		uintptr(uPeriod),
		getUintptrFromBool(fChanged),
		0,
		0)
	return MMRESULT(ret1)
}

func JoySetThreshold(uJoyID UINT, uThreshold UINT) MMRESULT {
	ret1 := syscall3(joySetThreshold, 2,
		uintptr(uJoyID),
		uintptr(uThreshold),
		0)
	return MMRESULT(ret1)
}

func MciGetCreatorTask(mciId MCIDEVICEID) HTASK {
	ret1 := syscall3(mciGetCreatorTask, 1,
		uintptr(mciId),
		0,
		0)
	return HTASK(ret1)
}

func MciGetDeviceIDFromElementID(dwElementID DWORD, lpstrType string) MCIDEVICEID {
	lpstrTypeStr := unicode16FromString(lpstrType)
	ret1 := syscall3(mciGetDeviceIDFromElementID, 2,
		uintptr(dwElementID),
		uintptr(unsafe.Pointer(&lpstrTypeStr[0])),
		0)
	return MCIDEVICEID(ret1)
}

func MciGetDeviceID(pszDevice string) MCIDEVICEID {
	pszDeviceStr := unicode16FromString(pszDevice)
	ret1 := syscall3(mciGetDeviceID, 1,
		uintptr(unsafe.Pointer(&pszDeviceStr[0])),
		0,
		0)
	return MCIDEVICEID(ret1)
}

func MciGetErrorString(mcierr MCIERROR, pszText LPWSTR, cchText UINT) bool {
	ret1 := syscall3(mciGetErrorString, 3,
		uintptr(mcierr),
		uintptr(unsafe.Pointer(pszText)),
		uintptr(cchText))
	return ret1 != 0
}

func MciGetYieldProc(mciId MCIDEVICEID, pdwYieldData *uint32) YIELDPROC {
	ret1 := syscall3(mciGetYieldProc, 2,
		uintptr(mciId),
		uintptr(unsafe.Pointer(pdwYieldData)),
		0)
	return func(mciId MCIDEVICEID, dwYieldData DWORD) UINT {
		ret2 := syscall3(ret1, 2,
			uintptr(mciId),
			uintptr(dwYieldData),
			0)
		return UINT(ret2)
	}
}

func MciSendCommand(mciId MCIDEVICEID, uMsg UINT, dwParam1 *uint32, dwParam2 *uint32) MCIERROR {
	ret1 := syscall6(mciSendCommand, 4,
		uintptr(mciId),
		uintptr(uMsg),
		uintptr(unsafe.Pointer(dwParam1)),
		uintptr(unsafe.Pointer(dwParam2)),
		0,
		0)
	return MCIERROR(ret1)
}

func MciSendString(lpstrCommand string, lpstrReturnString LPWSTR, uReturnLength UINT, hwndCallback HWND) MCIERROR {
	lpstrCommandStr := unicode16FromString(lpstrCommand)
	ret1 := syscall6(mciSendString, 4,
		uintptr(unsafe.Pointer(&lpstrCommandStr[0])),
		uintptr(unsafe.Pointer(lpstrReturnString)),
		uintptr(uReturnLength),
		uintptr(hwndCallback),
		0,
		0)
	return MCIERROR(ret1)
}

func MciSetYieldProc(mciId MCIDEVICEID, fpYieldProc YIELDPROC, dwYieldData DWORD) bool {
	fpYieldProcCallback := syscall.NewCallback(func(mciIdRawArg MCIDEVICEID, dwYieldDataRawArg DWORD) uintptr {
		ret := fpYieldProc(mciIdRawArg, dwYieldDataRawArg)
		return uintptr(ret)
	})
	ret1 := syscall3(mciSetYieldProc, 3,
		uintptr(mciId),
		fpYieldProcCallback,
		uintptr(dwYieldData))
	return ret1 != 0
}

func MidiConnect(hmi HMIDI, hmo HMIDIOUT, pReserved LPVOID) MMRESULT {
	ret1 := syscall3(midiConnect, 3,
		uintptr(hmi),
		uintptr(hmo),
		uintptr(unsafe.Pointer(pReserved)))
	return MMRESULT(ret1)
}

func MidiDisconnect(hmi HMIDI, hmo HMIDIOUT, pReserved LPVOID) MMRESULT {
	ret1 := syscall3(midiDisconnect, 3,
		uintptr(hmi),
		uintptr(hmo),
		uintptr(unsafe.Pointer(pReserved)))
	return MMRESULT(ret1)
}

func MidiInAddBuffer(hmi HMIDIIN, pmh *MIDIHDR, cbmh UINT) MMRESULT {
	ret1 := syscall3(midiInAddBuffer, 3,
		uintptr(hmi),
		uintptr(unsafe.Pointer(pmh)),
		uintptr(cbmh))
	return MMRESULT(ret1)
}

func MidiInClose(hmi HMIDIIN) MMRESULT {
	ret1 := syscall3(midiInClose, 1,
		uintptr(hmi),
		0,
		0)
	return MMRESULT(ret1)
}

func MidiInGetDevCaps(uDeviceID *uint32, pmic *MIDIINCAPS, cbmic UINT) MMRESULT {
	ret1 := syscall3(midiInGetDevCaps, 3,
		uintptr(unsafe.Pointer(uDeviceID)),
		uintptr(unsafe.Pointer(pmic)),
		uintptr(cbmic))
	return MMRESULT(ret1)
}

func MidiInGetErrorText(mmrError MMRESULT, pszText LPWSTR, cchText UINT) MMRESULT {
	ret1 := syscall3(midiInGetErrorText, 3,
		uintptr(mmrError),
		uintptr(unsafe.Pointer(pszText)),
		uintptr(cchText))
	return MMRESULT(ret1)
}

func MidiInGetID(hmi HMIDIIN, puDeviceID *UINT) MMRESULT {
	ret1 := syscall3(midiInGetID, 2,
		uintptr(hmi),
		uintptr(unsafe.Pointer(puDeviceID)),
		0)
	return MMRESULT(ret1)
}

func MidiInGetNumDevs() UINT {
	ret1 := syscall3(midiInGetNumDevs, 0,
		0,
		0,
		0)
	return UINT(ret1)
}

func MidiInMessage(hmi HMIDIIN, uMsg UINT, dw1 *uint32, dw2 *uint32) MMRESULT {
	ret1 := syscall6(midiInMessage, 4,
		uintptr(hmi),
		uintptr(uMsg),
		uintptr(unsafe.Pointer(dw1)),
		uintptr(unsafe.Pointer(dw2)),
		0,
		0)
	return MMRESULT(ret1)
}

func MidiInOpen(phmi *HMIDIIN, uDeviceID UINT, dwCallback *uint32, dwInstance *uint32, fdwOpen DWORD) MMRESULT {
	ret1 := syscall6(midiInOpen, 5,
		uintptr(unsafe.Pointer(phmi)),
		uintptr(uDeviceID),
		uintptr(unsafe.Pointer(dwCallback)),
		uintptr(unsafe.Pointer(dwInstance)),
		uintptr(fdwOpen),
		0)
	return MMRESULT(ret1)
}

func MidiInPrepareHeader(hmi HMIDIIN, pmh *MIDIHDR, cbmh UINT) MMRESULT {
	ret1 := syscall3(midiInPrepareHeader, 3,
		uintptr(hmi),
		uintptr(unsafe.Pointer(pmh)),
		uintptr(cbmh))
	return MMRESULT(ret1)
}

func MidiInReset(hmi HMIDIIN) MMRESULT {
	ret1 := syscall3(midiInReset, 1,
		uintptr(hmi),
		0,
		0)
	return MMRESULT(ret1)
}

func MidiInStart(hmi HMIDIIN) MMRESULT {
	ret1 := syscall3(midiInStart, 1,
		uintptr(hmi),
		0,
		0)
	return MMRESULT(ret1)
}

func MidiInStop(hmi HMIDIIN) MMRESULT {
	ret1 := syscall3(midiInStop, 1,
		uintptr(hmi),
		0,
		0)
	return MMRESULT(ret1)
}

func MidiInUnprepareHeader(hmi HMIDIIN, pmh *MIDIHDR, cbmh UINT) MMRESULT {
	ret1 := syscall3(midiInUnprepareHeader, 3,
		uintptr(hmi),
		uintptr(unsafe.Pointer(pmh)),
		uintptr(cbmh))
	return MMRESULT(ret1)
}

func MidiOutCacheDrumPatches(hmo HMIDIOUT, uPatch UINT, pwkya *uint16, fuCache UINT) MMRESULT {
	ret1 := syscall6(midiOutCacheDrumPatches, 4,
		uintptr(hmo),
		uintptr(uPatch),
		uintptr(unsafe.Pointer(pwkya)),
		uintptr(fuCache),
		0,
		0)
	return MMRESULT(ret1)
}

func MidiOutCachePatches(hmo HMIDIOUT, uBank UINT, pwpa *uint16, fuCache UINT) MMRESULT {
	ret1 := syscall6(midiOutCachePatches, 4,
		uintptr(hmo),
		uintptr(uBank),
		uintptr(unsafe.Pointer(pwpa)),
		uintptr(fuCache),
		0,
		0)
	return MMRESULT(ret1)
}

func MidiOutClose(hmo HMIDIOUT) MMRESULT {
	ret1 := syscall3(midiOutClose, 1,
		uintptr(hmo),
		0,
		0)
	return MMRESULT(ret1)
}

func MidiOutGetDevCaps(uDeviceID *uint32, pmoc LPMIDIOUTCAPS, cbmoc UINT) MMRESULT {
	ret1 := syscall3(midiOutGetDevCaps, 3,
		uintptr(unsafe.Pointer(uDeviceID)),
		uintptr(unsafe.Pointer(pmoc)),
		uintptr(cbmoc))
	return MMRESULT(ret1)
}

func MidiOutGetErrorText(mmrError MMRESULT, pszText LPWSTR, cchText UINT) MMRESULT {
	ret1 := syscall3(midiOutGetErrorText, 3,
		uintptr(mmrError),
		uintptr(unsafe.Pointer(pszText)),
		uintptr(cchText))
	return MMRESULT(ret1)
}

func MidiOutGetID(hmo HMIDIOUT, puDeviceID *UINT) MMRESULT {
	ret1 := syscall3(midiOutGetID, 2,
		uintptr(hmo),
		uintptr(unsafe.Pointer(puDeviceID)),
		0)
	return MMRESULT(ret1)
}

func MidiOutGetNumDevs() UINT {
	ret1 := syscall3(midiOutGetNumDevs, 0,
		0,
		0,
		0)
	return UINT(ret1)
}

func MidiOutGetVolume(hmo HMIDIOUT, pdwVolume *uint32) MMRESULT {
	ret1 := syscall3(midiOutGetVolume, 2,
		uintptr(hmo),
		uintptr(unsafe.Pointer(pdwVolume)),
		0)
	return MMRESULT(ret1)
}

func MidiOutLongMsg(hmo HMIDIOUT, pmh *MIDIHDR, cbmh UINT) MMRESULT {
	ret1 := syscall3(midiOutLongMsg, 3,
		uintptr(hmo),
		uintptr(unsafe.Pointer(pmh)),
		uintptr(cbmh))
	return MMRESULT(ret1)
}

func MidiOutMessage(hmo HMIDIOUT, uMsg UINT, dw1 *uint32, dw2 *uint32) MMRESULT {
	ret1 := syscall6(midiOutMessage, 4,
		uintptr(hmo),
		uintptr(uMsg),
		uintptr(unsafe.Pointer(dw1)),
		uintptr(unsafe.Pointer(dw2)),
		0,
		0)
	return MMRESULT(ret1)
}

func MidiOutOpen(phmo *HMIDIOUT, uDeviceID UINT, dwCallback *uint32, dwInstance *uint32, fdwOpen DWORD) MMRESULT {
	ret1 := syscall6(midiOutOpen, 5,
		uintptr(unsafe.Pointer(phmo)),
		uintptr(uDeviceID),
		uintptr(unsafe.Pointer(dwCallback)),
		uintptr(unsafe.Pointer(dwInstance)),
		uintptr(fdwOpen),
		0)
	return MMRESULT(ret1)
}

func MidiOutPrepareHeader(hmo HMIDIOUT, pmh *MIDIHDR, cbmh UINT) MMRESULT {
	ret1 := syscall3(midiOutPrepareHeader, 3,
		uintptr(hmo),
		uintptr(unsafe.Pointer(pmh)),
		uintptr(cbmh))
	return MMRESULT(ret1)
}

func MidiOutReset(hmo HMIDIOUT) MMRESULT {
	ret1 := syscall3(midiOutReset, 1,
		uintptr(hmo),
		0,
		0)
	return MMRESULT(ret1)
}

func MidiOutSetVolume(hmo HMIDIOUT, dwVolume DWORD) MMRESULT {
	ret1 := syscall3(midiOutSetVolume, 2,
		uintptr(hmo),
		uintptr(dwVolume),
		0)
	return MMRESULT(ret1)
}

func MidiOutShortMsg(hmo HMIDIOUT, dwMsg DWORD) MMRESULT {
	ret1 := syscall3(midiOutShortMsg, 2,
		uintptr(hmo),
		uintptr(dwMsg),
		0)
	return MMRESULT(ret1)
}

func MidiOutUnprepareHeader(hmo HMIDIOUT, pmh *MIDIHDR, cbmh UINT) MMRESULT {
	ret1 := syscall3(midiOutUnprepareHeader, 3,
		uintptr(hmo),
		uintptr(unsafe.Pointer(pmh)),
		uintptr(cbmh))
	return MMRESULT(ret1)
}

func MidiStreamClose(hms HMIDISTRM) MMRESULT {
	ret1 := syscall3(midiStreamClose, 1,
		uintptr(hms),
		0,
		0)
	return MMRESULT(ret1)
}

func MidiStreamOpen(phms *HMIDISTRM, puDeviceID *UINT, cMidi DWORD, dwCallback *uint32, dwInstance *uint32, fdwOpen DWORD) MMRESULT {
	ret1 := syscall6(midiStreamOpen, 6,
		uintptr(unsafe.Pointer(phms)),
		uintptr(unsafe.Pointer(puDeviceID)),
		uintptr(cMidi),
		uintptr(unsafe.Pointer(dwCallback)),
		uintptr(unsafe.Pointer(dwInstance)),
		uintptr(fdwOpen))
	return MMRESULT(ret1)
}

func MidiStreamOut(hms HMIDISTRM, pmh *MIDIHDR, cbmh UINT) MMRESULT {
	ret1 := syscall3(midiStreamOut, 3,
		uintptr(hms),
		uintptr(unsafe.Pointer(pmh)),
		uintptr(cbmh))
	return MMRESULT(ret1)
}

func MidiStreamPause(hms HMIDISTRM) MMRESULT {
	ret1 := syscall3(midiStreamPause, 1,
		uintptr(hms),
		0,
		0)
	return MMRESULT(ret1)
}

func MidiStreamPosition(hms HMIDISTRM, lpmmt *MMTIME, cbmmt UINT) MMRESULT {
	ret1 := syscall3(midiStreamPosition, 3,
		uintptr(hms),
		uintptr(unsafe.Pointer(lpmmt)),
		uintptr(cbmmt))
	return MMRESULT(ret1)
}

func MidiStreamProperty(hms HMIDISTRM, lppropdata *byte, dwProperty DWORD) MMRESULT {
	ret1 := syscall3(midiStreamProperty, 3,
		uintptr(hms),
		uintptr(unsafe.Pointer(lppropdata)),
		uintptr(dwProperty))
	return MMRESULT(ret1)
}

func MidiStreamRestart(hms HMIDISTRM) MMRESULT {
	ret1 := syscall3(midiStreamRestart, 1,
		uintptr(hms),
		0,
		0)
	return MMRESULT(ret1)
}

func MidiStreamStop(hms HMIDISTRM) MMRESULT {
	ret1 := syscall3(midiStreamStop, 1,
		uintptr(hms),
		0,
		0)
	return MMRESULT(ret1)
}

func MixerClose(hmx HMIXER) MMRESULT {
	ret1 := syscall3(mixerClose, 1,
		uintptr(hmx),
		0,
		0)
	return MMRESULT(ret1)
}

func MixerGetControlDetails(hmxobj HMIXEROBJ, pmxcd *MIXERCONTROLDETAILS, fdwDetails DWORD) MMRESULT {
	ret1 := syscall3(mixerGetControlDetails, 3,
		uintptr(hmxobj),
		uintptr(unsafe.Pointer(pmxcd)),
		uintptr(fdwDetails))
	return MMRESULT(ret1)
}

func MixerGetDevCaps(uMxId *uint32, pmxcaps LPMIXERCAPS, cbmxcaps UINT) MMRESULT {
	ret1 := syscall3(mixerGetDevCaps, 3,
		uintptr(unsafe.Pointer(uMxId)),
		uintptr(unsafe.Pointer(pmxcaps)),
		uintptr(cbmxcaps))
	return MMRESULT(ret1)
}

func MixerGetID(hmxobj HMIXEROBJ, puMxId *UINT, fdwId DWORD) MMRESULT {
	ret1 := syscall3(mixerGetID, 3,
		uintptr(hmxobj),
		uintptr(unsafe.Pointer(puMxId)),
		uintptr(fdwId))
	return MMRESULT(ret1)
}

func MixerGetLineControls(hmxobj HMIXEROBJ, pmxlc LPMIXERLINECONTROLS, fdwControls DWORD) MMRESULT {
	ret1 := syscall3(mixerGetLineControls, 3,
		uintptr(hmxobj),
		uintptr(unsafe.Pointer(pmxlc)),
		uintptr(fdwControls))
	return MMRESULT(ret1)
}

func MixerGetLineInfo(hmxobj HMIXEROBJ, pmxl LPMIXERLINE, fdwInfo DWORD) MMRESULT {
	ret1 := syscall3(mixerGetLineInfo, 3,
		uintptr(hmxobj),
		uintptr(unsafe.Pointer(pmxl)),
		uintptr(fdwInfo))
	return MMRESULT(ret1)
}

func MixerGetNumDevs() UINT {
	ret1 := syscall3(mixerGetNumDevs, 0,
		0,
		0,
		0)
	return UINT(ret1)
}

func MixerMessage(hmx HMIXER, uMsg UINT, dwParam1 *uint32, dwParam2 *uint32) DWORD {
	ret1 := syscall6(mixerMessage, 4,
		uintptr(hmx),
		uintptr(uMsg),
		uintptr(unsafe.Pointer(dwParam1)),
		uintptr(unsafe.Pointer(dwParam2)),
		0,
		0)
	return DWORD(ret1)
}

func MixerOpen(phmx *HMIXER, uMxId UINT, dwCallback *uint32, dwInstance *uint32, fdwOpen DWORD) MMRESULT {
	ret1 := syscall6(mixerOpen, 5,
		uintptr(unsafe.Pointer(phmx)),
		uintptr(uMxId),
		uintptr(unsafe.Pointer(dwCallback)),
		uintptr(unsafe.Pointer(dwInstance)),
		uintptr(fdwOpen),
		0)
	return MMRESULT(ret1)
}

func MixerSetControlDetails(hmxobj HMIXEROBJ, pmxcd *MIXERCONTROLDETAILS, fdwDetails DWORD) MMRESULT {
	ret1 := syscall3(mixerSetControlDetails, 3,
		uintptr(hmxobj),
		uintptr(unsafe.Pointer(pmxcd)),
		uintptr(fdwDetails))
	return MMRESULT(ret1)
}

func MmioAdvance(hmmio HMMIO, pmmioinfo LPMMIOINFO, fuAdvance UINT) MMRESULT {
	ret1 := syscall3(mmioAdvance, 3,
		uintptr(hmmio),
		uintptr(unsafe.Pointer(pmmioinfo)),
		uintptr(fuAdvance))
	return MMRESULT(ret1)
}

func MmioAscend(hmmio HMMIO, pmmcki LPMMCKINFO, fuAscend UINT) MMRESULT {
	ret1 := syscall3(mmioAscend, 3,
		uintptr(hmmio),
		uintptr(unsafe.Pointer(pmmcki)),
		uintptr(fuAscend))
	return MMRESULT(ret1)
}

func MmioClose(hmmio HMMIO, fuClose UINT) MMRESULT {
	ret1 := syscall3(mmioClose, 2,
		uintptr(hmmio),
		uintptr(fuClose),
		0)
	return MMRESULT(ret1)
}

func MmioCreateChunk(hmmio HMMIO, pmmcki LPMMCKINFO, fuCreate UINT) MMRESULT {
	ret1 := syscall3(mmioCreateChunk, 3,
		uintptr(hmmio),
		uintptr(unsafe.Pointer(pmmcki)),
		uintptr(fuCreate))
	return MMRESULT(ret1)
}

func MmioDescend(hmmio HMMIO, pmmcki LPMMCKINFO, pmmckiParent /*const*/ *MMCKINFO, fuDescend UINT) MMRESULT {
	ret1 := syscall6(mmioDescend, 4,
		uintptr(hmmio),
		uintptr(unsafe.Pointer(pmmcki)),
		uintptr(unsafe.Pointer(pmmckiParent)),
		uintptr(fuDescend),
		0,
		0)
	return MMRESULT(ret1)
}

func MmioFlush(hmmio HMMIO, fuFlush UINT) MMRESULT {
	ret1 := syscall3(mmioFlush, 2,
		uintptr(hmmio),
		uintptr(fuFlush),
		0)
	return MMRESULT(ret1)
}

func MmioGetInfo(hmmio HMMIO, pmmioinfo LPMMIOINFO, fuInfo UINT) MMRESULT {
	ret1 := syscall3(mmioGetInfo, 3,
		uintptr(hmmio),
		uintptr(unsafe.Pointer(pmmioinfo)),
		uintptr(fuInfo))
	return MMRESULT(ret1)
}

func MmioInstallIOProc(fccIOProc FOURCC, pIOProc *MMIOPROC, dwFlags DWORD) *MMIOPROC {
	ret1 := syscall3(mmioInstallIOProc, 3,
		uintptr(fccIOProc),
		uintptr(unsafe.Pointer(pIOProc)),
		uintptr(dwFlags))
	return (*MMIOPROC)(unsafe.Pointer(ret1))
}

func MmioOpen(pszFileName LPWSTR, pmmioinfo LPMMIOINFO, fdwOpen DWORD) HMMIO {
	ret1 := syscall3(mmioOpen, 3,
		uintptr(unsafe.Pointer(pszFileName)),
		uintptr(unsafe.Pointer(pmmioinfo)),
		uintptr(fdwOpen))
	return HMMIO(ret1)
}

func MmioRead(hmmio HMMIO, pch HPSTR, cch LONG) LONG {
	ret1 := syscall3(mmioRead, 3,
		uintptr(hmmio),
		uintptr(unsafe.Pointer(pch)),
		uintptr(cch))
	return LONG(ret1)
}

func MmioRename(pszFileName string, pszNewFileName string, pmmioinfo /*const*/ *MMIOINFO, fdwRename DWORD) MMRESULT {
	pszFileNameStr := unicode16FromString(pszFileName)
	pszNewFileNameStr := unicode16FromString(pszNewFileName)
	ret1 := syscall6(mmioRename, 4,
		uintptr(unsafe.Pointer(&pszFileNameStr[0])),
		uintptr(unsafe.Pointer(&pszNewFileNameStr[0])),
		uintptr(unsafe.Pointer(pmmioinfo)),
		uintptr(fdwRename),
		0,
		0)
	return MMRESULT(ret1)
}

func MmioSeek(hmmio HMMIO, lOffset LONG, iOrigin int32) LONG {
	ret1 := syscall3(mmioSeek, 3,
		uintptr(hmmio),
		uintptr(lOffset),
		uintptr(iOrigin))
	return LONG(ret1)
}

func MmioSendMessage(hmmio HMMIO, uMsg UINT, lParam1 LPARAM, lParam2 LPARAM) LRESULT {
	ret1 := syscall6(mmioSendMessage, 4,
		uintptr(hmmio),
		uintptr(uMsg),
		uintptr(lParam1),
		uintptr(lParam2),
		0,
		0)
	return LRESULT(ret1)
}

func MmioSetBuffer(hmmio HMMIO, pchBuffer LPSTR, cchBuffer LONG, fuBuffer UINT) MMRESULT {
	ret1 := syscall6(mmioSetBuffer, 4,
		uintptr(hmmio),
		uintptr(unsafe.Pointer(pchBuffer)),
		uintptr(cchBuffer),
		uintptr(fuBuffer),
		0,
		0)
	return MMRESULT(ret1)
}

func MmioSetInfo(hmmio HMMIO, pmmioinfo /*const*/ *MMIOINFO, fuInfo UINT) MMRESULT {
	ret1 := syscall3(mmioSetInfo, 3,
		uintptr(hmmio),
		uintptr(unsafe.Pointer(pmmioinfo)),
		uintptr(fuInfo))
	return MMRESULT(ret1)
}

func MmioStringToFOURCC(sz string, uFlags UINT) FOURCC {
	szStr := unicode16FromString(sz)
	ret1 := syscall3(mmioStringToFOURCC, 2,
		uintptr(unsafe.Pointer(&szStr[0])),
		uintptr(uFlags),
		0)
	return FOURCC(ret1)
}

func MmioWrite(hmmio HMMIO, pch /*const*/ HPSTR, cch LONG) LONG {
	ret1 := syscall3(mmioWrite, 3,
		uintptr(hmmio),
		uintptr(unsafe.Pointer(pch)),
		uintptr(cch))
	return LONG(ret1)
}

func SndPlaySound(pszSound string, fuSound UINT) bool {
	pszSoundStr := unicode16FromString(pszSound)
	ret1 := syscall3(sndPlaySound, 2,
		uintptr(unsafe.Pointer(&pszSoundStr[0])),
		uintptr(fuSound),
		0)
	return ret1 != 0
}

func TimeBeginPeriod(uPeriod UINT) MMRESULT {
	ret1 := syscall3(timeBeginPeriod, 1,
		uintptr(uPeriod),
		0,
		0)
	return MMRESULT(ret1)
}

func TimeEndPeriod(uPeriod UINT) MMRESULT {
	ret1 := syscall3(timeEndPeriod, 1,
		uintptr(uPeriod),
		0,
		0)
	return MMRESULT(ret1)
}

func TimeGetDevCaps(ptc LPTIMECAPS, cbtc UINT) MMRESULT {
	ret1 := syscall3(timeGetDevCaps, 2,
		uintptr(unsafe.Pointer(ptc)),
		uintptr(cbtc),
		0)
	return MMRESULT(ret1)
}

func TimeGetSystemTime(pmmt *MMTIME, cbmmt UINT) MMRESULT {
	ret1 := syscall3(timeGetSystemTime, 2,
		uintptr(unsafe.Pointer(pmmt)),
		uintptr(cbmmt),
		0)
	return MMRESULT(ret1)
}

func TimeGetTime() DWORD {
	ret1 := syscall3(timeGetTime, 0,
		0,
		0,
		0)
	return DWORD(ret1)
}

func TimeKillEvent(uTimerID UINT) MMRESULT {
	ret1 := syscall3(timeKillEvent, 1,
		uintptr(uTimerID),
		0,
		0)
	return MMRESULT(ret1)
}

func TimeSetEvent(uDelay UINT, uResolution UINT, fptc TIMECALLBACK, dwUser *uint32, fuEvent UINT) MMRESULT {
	fptcCallback := syscall.NewCallback(fptc)
	ret1 := syscall6(timeSetEvent, 5,
		uintptr(uDelay),
		uintptr(uResolution),
		fptcCallback,
		uintptr(unsafe.Pointer(dwUser)),
		uintptr(fuEvent),
		0)
	return MMRESULT(ret1)
}

func WaveInAddBuffer(hwi HWAVEIN, pwh LPWAVEHDR, cbwh UINT) MMRESULT {
	ret1 := syscall3(waveInAddBuffer, 3,
		uintptr(hwi),
		uintptr(unsafe.Pointer(pwh)),
		uintptr(cbwh))
	return MMRESULT(ret1)
}

func WaveInClose(hwi HWAVEIN) MMRESULT {
	ret1 := syscall3(waveInClose, 1,
		uintptr(hwi),
		0,
		0)
	return MMRESULT(ret1)
}

func WaveInGetDevCaps(uDeviceID *uint32, pwic LPWAVEINCAPS, cbwic UINT) MMRESULT {
	ret1 := syscall3(waveInGetDevCaps, 3,
		uintptr(unsafe.Pointer(uDeviceID)),
		uintptr(unsafe.Pointer(pwic)),
		uintptr(cbwic))
	return MMRESULT(ret1)
}

func WaveInGetErrorText(mmrError MMRESULT, pszText LPWSTR, cchText UINT) MMRESULT {
	ret1 := syscall3(waveInGetErrorText, 3,
		uintptr(mmrError),
		uintptr(unsafe.Pointer(pszText)),
		uintptr(cchText))
	return MMRESULT(ret1)
}

func WaveInGetID(hwi HWAVEIN, puDeviceID *UINT) MMRESULT {
	ret1 := syscall3(waveInGetID, 2,
		uintptr(hwi),
		uintptr(unsafe.Pointer(puDeviceID)),
		0)
	return MMRESULT(ret1)
}

func WaveInGetNumDevs() UINT {
	ret1 := syscall3(waveInGetNumDevs, 0,
		0,
		0,
		0)
	return UINT(ret1)
}

func WaveInGetPosition(hwi HWAVEIN, pmmt *MMTIME, cbmmt UINT) MMRESULT {
	ret1 := syscall3(waveInGetPosition, 3,
		uintptr(hwi),
		uintptr(unsafe.Pointer(pmmt)),
		uintptr(cbmmt))
	return MMRESULT(ret1)
}

func WaveInMessage(hwi HWAVEIN, uMsg UINT, dw1 *uint32, dw2 *uint32) MMRESULT {
	ret1 := syscall6(waveInMessage, 4,
		uintptr(hwi),
		uintptr(uMsg),
		uintptr(unsafe.Pointer(dw1)),
		uintptr(unsafe.Pointer(dw2)),
		0,
		0)
	return MMRESULT(ret1)
}

func WaveInOpen(phwi *HWAVEIN, uDeviceID UINT, pwfx /*const*/ *WAVEFORMATEX, dwCallback *uint32, dwInstance *uint32, fdwOpen DWORD) MMRESULT {
	ret1 := syscall6(waveInOpen, 6,
		uintptr(unsafe.Pointer(phwi)),
		uintptr(uDeviceID),
		uintptr(unsafe.Pointer(pwfx)),
		uintptr(unsafe.Pointer(dwCallback)),
		uintptr(unsafe.Pointer(dwInstance)),
		uintptr(fdwOpen))
	return MMRESULT(ret1)
}

func WaveInPrepareHeader(hwi HWAVEIN, pwh LPWAVEHDR, cbwh UINT) MMRESULT {
	ret1 := syscall3(waveInPrepareHeader, 3,
		uintptr(hwi),
		uintptr(unsafe.Pointer(pwh)),
		uintptr(cbwh))
	return MMRESULT(ret1)
}

func WaveInReset(hwi HWAVEIN) MMRESULT {
	ret1 := syscall3(waveInReset, 1,
		uintptr(hwi),
		0,
		0)
	return MMRESULT(ret1)
}

func WaveInStart(hwi HWAVEIN) MMRESULT {
	ret1 := syscall3(waveInStart, 1,
		uintptr(hwi),
		0,
		0)
	return MMRESULT(ret1)
}

func WaveInStop(hwi HWAVEIN) MMRESULT {
	ret1 := syscall3(waveInStop, 1,
		uintptr(hwi),
		0,
		0)
	return MMRESULT(ret1)
}

func WaveInUnprepareHeader(hwi HWAVEIN, pwh LPWAVEHDR, cbwh UINT) MMRESULT {
	ret1 := syscall3(waveInUnprepareHeader, 3,
		uintptr(hwi),
		uintptr(unsafe.Pointer(pwh)),
		uintptr(cbwh))
	return MMRESULT(ret1)
}

func WaveOutBreakLoop(hwo HWAVEOUT) MMRESULT {
	ret1 := syscall3(waveOutBreakLoop, 1,
		uintptr(hwo),
		0,
		0)
	return MMRESULT(ret1)
}

func WaveOutClose(hwo HWAVEOUT) MMRESULT {
	ret1 := syscall3(waveOutClose, 1,
		uintptr(hwo),
		0,
		0)
	return MMRESULT(ret1)
}

func WaveOutGetDevCaps(uDeviceID *uint32, pwoc LPWAVEOUTCAPS, cbwoc UINT) MMRESULT {
	ret1 := syscall3(waveOutGetDevCaps, 3,
		uintptr(unsafe.Pointer(uDeviceID)),
		uintptr(unsafe.Pointer(pwoc)),
		uintptr(cbwoc))
	return MMRESULT(ret1)
}

func WaveOutGetErrorText(mmrError MMRESULT, pszText LPWSTR, cchText UINT) MMRESULT {
	ret1 := syscall3(waveOutGetErrorText, 3,
		uintptr(mmrError),
		uintptr(unsafe.Pointer(pszText)),
		uintptr(cchText))
	return MMRESULT(ret1)
}

func WaveOutGetID(hwo HWAVEOUT, puDeviceID *UINT) MMRESULT {
	ret1 := syscall3(waveOutGetID, 2,
		uintptr(hwo),
		uintptr(unsafe.Pointer(puDeviceID)),
		0)
	return MMRESULT(ret1)
}

func WaveOutGetNumDevs() UINT {
	ret1 := syscall3(waveOutGetNumDevs, 0,
		0,
		0,
		0)
	return UINT(ret1)
}

func WaveOutGetPitch(hwo HWAVEOUT, pdwPitch *uint32) MMRESULT {
	ret1 := syscall3(waveOutGetPitch, 2,
		uintptr(hwo),
		uintptr(unsafe.Pointer(pdwPitch)),
		0)
	return MMRESULT(ret1)
}

func WaveOutGetPlaybackRate(hwo HWAVEOUT, pdwRate *uint32) MMRESULT {
	ret1 := syscall3(waveOutGetPlaybackRate, 2,
		uintptr(hwo),
		uintptr(unsafe.Pointer(pdwRate)),
		0)
	return MMRESULT(ret1)
}

func WaveOutGetPosition(hwo HWAVEOUT, pmmt *MMTIME, cbmmt UINT) MMRESULT {
	ret1 := syscall3(waveOutGetPosition, 3,
		uintptr(hwo),
		uintptr(unsafe.Pointer(pmmt)),
		uintptr(cbmmt))
	return MMRESULT(ret1)
}

func WaveOutGetVolume(hwo HWAVEOUT, pdwVolume *uint32) MMRESULT {
	ret1 := syscall3(waveOutGetVolume, 2,
		uintptr(hwo),
		uintptr(unsafe.Pointer(pdwVolume)),
		0)
	return MMRESULT(ret1)
}

func WaveOutMessage(hwo HWAVEOUT, uMsg UINT, dw1 *uint32, dw2 *uint32) MMRESULT {
	ret1 := syscall6(waveOutMessage, 4,
		uintptr(hwo),
		uintptr(uMsg),
		uintptr(unsafe.Pointer(dw1)),
		uintptr(unsafe.Pointer(dw2)),
		0,
		0)
	return MMRESULT(ret1)
}

func WaveOutOpen(phwo *HWAVEOUT, uDeviceID UINT, pwfx /*const*/ *WAVEFORMATEX, dwCallback *uint32, dwInstance *uint32, fdwOpen DWORD) MMRESULT {
	ret1 := syscall6(waveOutOpen, 6,
		uintptr(unsafe.Pointer(phwo)),
		uintptr(uDeviceID),
		uintptr(unsafe.Pointer(pwfx)),
		uintptr(unsafe.Pointer(dwCallback)),
		uintptr(unsafe.Pointer(dwInstance)),
		uintptr(fdwOpen))
	return MMRESULT(ret1)
}

func WaveOutPause(hwo HWAVEOUT) MMRESULT {
	ret1 := syscall3(waveOutPause, 1,
		uintptr(hwo),
		0,
		0)
	return MMRESULT(ret1)
}

func WaveOutPrepareHeader(hwo HWAVEOUT, pwh LPWAVEHDR, cbwh UINT) MMRESULT {
	ret1 := syscall3(waveOutPrepareHeader, 3,
		uintptr(hwo),
		uintptr(unsafe.Pointer(pwh)),
		uintptr(cbwh))
	return MMRESULT(ret1)
}

func WaveOutReset(hwo HWAVEOUT) MMRESULT {
	ret1 := syscall3(waveOutReset, 1,
		uintptr(hwo),
		0,
		0)
	return MMRESULT(ret1)
}

func WaveOutRestart(hwo HWAVEOUT) MMRESULT {
	ret1 := syscall3(waveOutRestart, 1,
		uintptr(hwo),
		0,
		0)
	return MMRESULT(ret1)
}

func WaveOutSetPitch(hwo HWAVEOUT, dwPitch DWORD) MMRESULT {
	ret1 := syscall3(waveOutSetPitch, 2,
		uintptr(hwo),
		uintptr(dwPitch),
		0)
	return MMRESULT(ret1)
}

func WaveOutSetPlaybackRate(hwo HWAVEOUT, dwRate DWORD) MMRESULT {
	ret1 := syscall3(waveOutSetPlaybackRate, 2,
		uintptr(hwo),
		uintptr(dwRate),
		0)
	return MMRESULT(ret1)
}

func WaveOutSetVolume(hwo HWAVEOUT, dwVolume DWORD) MMRESULT {
	ret1 := syscall3(waveOutSetVolume, 2,
		uintptr(hwo),
		uintptr(dwVolume),
		0)
	return MMRESULT(ret1)
}

func WaveOutUnprepareHeader(hwo HWAVEOUT, pwh LPWAVEHDR, cbwh UINT) MMRESULT {
	ret1 := syscall3(waveOutUnprepareHeader, 3,
		uintptr(hwo),
		uintptr(unsafe.Pointer(pwh)),
		uintptr(cbwh))
	return MMRESULT(ret1)
}

func WaveOutWrite(hwo HWAVEOUT, pwh LPWAVEHDR, cbwh UINT) MMRESULT {
	ret1 := syscall3(waveOutWrite, 3,
		uintptr(hwo),
		uintptr(unsafe.Pointer(pwh)),
		uintptr(cbwh))
	return MMRESULT(ret1)
}

func DriverCallback(dwCallBack *uint32, uFlags DWORD, hDev HDRVR, wMsg DWORD, dwUser *uint32, dwParam1 *uint32, dwParam2 *uint32) bool {
	ret1 := syscall9(driverCallback, 7,
		uintptr(unsafe.Pointer(dwCallBack)),
		uintptr(uFlags),
		uintptr(hDev),
		uintptr(wMsg),
		uintptr(unsafe.Pointer(dwUser)),
		uintptr(unsafe.Pointer(dwParam1)),
		uintptr(unsafe.Pointer(dwParam2)),
		0,
		0)
	return ret1 != 0
}

func JoyConfigChanged(flags DWORD) MMRESULT {
	ret1 := syscall3(joyConfigChanged, 1,
		uintptr(flags),
		0,
		0)
	return MMRESULT(ret1)
}

func MciDriverNotify(hWndCallBack HWND, wDevID MCIDEVICEID, wStatus UINT) bool {
	ret1 := syscall3(mciDriverNotify, 3,
		uintptr(hWndCallBack),
		uintptr(wDevID),
		uintptr(wStatus))
	return ret1 != 0
}

func MciDriverYield(uDeviceID MCIDEVICEID) UINT {
	ret1 := syscall3(mciDriverYield, 1,
		uintptr(uDeviceID),
		0,
		0)
	return UINT(ret1)
}

func MciExecute(lpstrCommand /*const*/ LPCSTR) bool {
	ret1 := syscall3(mciExecute, 1,
		uintptr(unsafe.Pointer(lpstrCommand)),
		0,
		0)
	return ret1 != 0
}

func MciFreeCommandResource(uTable UINT) bool {
	ret1 := syscall3(mciFreeCommandResource, 1,
		uintptr(uTable),
		0,
		0)
	return ret1 != 0
}

func MciGetDriverData(uDeviceID MCIDEVICEID) *uint32 {
	ret1 := syscall3(mciGetDriverData, 1,
		uintptr(uDeviceID),
		0,
		0)
	return (*uint32)(unsafe.Pointer(ret1))
}

func MciLoadCommandResource(hInst HINSTANCE, resNameW string, aType UINT) UINT {
	resNameWStr := unicode16FromString(resNameW)
	ret1 := syscall3(mciLoadCommandResource, 3,
		uintptr(hInst),
		uintptr(unsafe.Pointer(&resNameWStr[0])),
		uintptr(aType))
	return UINT(ret1)
}

func MciSetDriverData(uDeviceID MCIDEVICEID, data *uint32) bool {
	ret1 := syscall3(mciSetDriverData, 2,
		uintptr(uDeviceID),
		uintptr(unsafe.Pointer(data)),
		0)
	return ret1 != 0
}

func MmGetCurrentTask() DWORD {
	ret1 := syscall3(mmGetCurrentTask, 0,
		0,
		0,
		0)
	return DWORD(ret1)
}

func MmTaskBlock(tid DWORD) {
	syscall3(mmTaskBlock, 1,
		uintptr(tid),
		0,
		0)
}

func MmTaskCreate(cb TASKCALLBACK, ph *HANDLE, client *uint32) UINT {
	cbCallback := syscall.NewCallback(cb)
	ret1 := syscall3(mmTaskCreate, 3,
		cbCallback,
		uintptr(unsafe.Pointer(ph)),
		uintptr(unsafe.Pointer(client)))
	return UINT(ret1)
}

func MmTaskSignal(tid DWORD) bool {
	ret1 := syscall3(mmTaskSignal, 1,
		uintptr(tid),
		0,
		0)
	return ret1 != 0
}

func MmTaskYield() {
	syscall3(mmTaskYield, 0,
		0,
		0,
		0)
}

func MmsystemGetVersion() UINT {
	ret1 := syscall3(mmsystemGetVersion, 0,
		0,
		0,
		0)
	return UINT(ret1)
}
