package common

import (
	"Lightx/pkg/clients"
	"fmt"
	"log"
	"os"
	"runtime"
	"runtime/debug"
	"strings"
	"time"

	"github.com/urfave/cli/v2"
)

type Options struct {
	Target           string
	Thread           int
	Timeout          int
	DeepScan         bool
	RootPath         bool
	NoPoc            string
	Tags             string
	IncludeTags      string
	ExcludeTags      string
	Template         string
	Proxy            string
	Debug            bool
	DisableDNSLog    bool
	Log4j2           bool
	Html             string
	ConDel           bool
	Log              string
	UsePing          bool   // 是否禁用存活探测，为true时禁用存活探测，对所有IP进行端口扫描
	PortRange        string // 端口扫描范围
	ServiceCheck     bool   // 是否开启服务指纹识别
	WebScan          bool   // 是否执行Web扫描
	RandomPort       bool   // 是否随机化端口扫描顺序
	TCPThreads       int    // TCP端口扫描线程数
	TCPTimeout       int    // TCP端口扫描超时时间
	PortsThreshold   int    // 端口阈值，超过此值认为可能是防火墙
	DisableFastjson  bool   // 是否禁用Fastjson探测
	DisableShiro     bool   // 是否禁用Shiro探测
	Stealth          bool   // 无痕模式，关闭所有主动探测
	CrawlerMode      string // 爬虫模式：auto（自动）、force（强制）、off（关闭）
	CrawlerDebug     bool   // 爬虫调试模式
	DNSLogServer     string // 自定义DNSLog服务器地址
	DNSLogToken      string // 自定义DNSLog授权令牌
	NoPlugin         bool   // 是否禁用插件系统
	UserList         string // 指定用户名列表，以逗号分隔或文件路径
	PasswordList     string // 指定密码列表，以逗号分隔或文件路径
	AppendUserList   string // 追加用户名列表，以逗号分隔或文件路径
	AppendPwdList    string // 追加密码列表，以逗号分隔或文件路径
	ExcludePort      string // 指定不需要扫描的端口范围
	PluginParams     string // 插件自定义参数，例如RTSP的URI路径或SMB域名
	BruteThreads     int    // 爆破线程数
	BruteTimeout     int    // 爆破超时时间（秒）
	BruteMaxAttempts int    // 最大爆破尝试次数
	ModuleName       string // 指定要调用的插件名称
	ShowPluginList   bool   // 是否在主程序中显示插件列表
	DomainResolve    bool   // 是否只进行域名解析，不进行端口扫描
	ResolveAll       bool   // 是否解析所有DNS记录类型（A、AAAA、CNAME、MX、TXT、NS）
	PreferIPv6       bool   // 是否优先使用IPv6地址进行扫描
	SkipCDN          bool   // 是否跳过CDN保护的域名
	ForceRealIP      bool   // 是否强制使用真实IP进行扫描
	DisableWildcard  bool   // 是否禁用泛解析过滤
}

const version = "1.4.0"

func init() {
	go func() {
		for {
			runtime.GC()
			debug.FreeOSMemory()
			time.Sleep(10 * time.Second)
		}
	}()
}

func Banner() {
	fmt.Println(`
    ___       ___       ___       ___       ___       ___   
   /\__\     /\  \     /\  \     /\__\     /\  \     /\__\  
  /:/  /    _\:\  \   /::\  \   /:/__/_    \:\  \   |::L__L 
 /:/__/    /\/::\__\ /:/\:\__\ /::\/\__\   /::\__\ /::::\__\
 \:\  \    \::/\/__/ \:\:\/__/ \/\::/  /  /:/\/__/ \;::;/__/
  \:\__\    \:\__\    \::/  /    /:/  /  /:/__/     |::|__| 
   \/__/     \/__/     \/__/     \/__/   \/__/       \/__/  
	`)
	fmt.Println("		         Lightx version:", version, "  by onewin\n")
}

func NewOptions() *Options {
	var cf Options
	app := &cli.App{
		Name:      "Lightx",
		Usage:     "一款轻量化的Web指纹识别与漏洞扫描工具 / A lightweight web fingerprint identification and vulnerability scanning tool",
		Version:   version,
		UsageText: "Lightx -u http://127.0.0.1",
		Flags: []cli.Flag{
			&cli.StringFlag{
				Name:    "t",
				Aliases: []string{"target"},
				Usage:   "目标URL或IP地址或IP段或域名，支持逗号分隔和IP范围 / Target URL, IP address, IP range or domain, supports comma-separated and IP ranges (e.g., ***********,*********** or ***********-3)",
			},
			&cli.BoolFlag{
				Name:    "d",
				Aliases: []string{"deep"},
				Value:   true,
				Usage:   "启用深度扫描以检查更多指纹，例如nacos、xxl-job / Enable deepScan to check more fingerprints, e.g nacos, xxl-job",
			},
			&cli.BoolFlag{
				Name:    "rp",
				Aliases: []string{"root-path"},
				Value:   true,
				Usage:   "深度指纹扫描是否采用根路径扫描 / Does the deep fingerprint adopt root path scanning",
			},
			&cli.StringFlag{
				Name:    "vul",
				Aliases: []string{"vulnerability"},
				Value:   "more", // 默认值为 more，表示多线程扫描
				Usage:   "禁用漏洞扫描或选择扫描模式 / Disable vulnerability scanning or select scan mode (more: 多线程, one: 单线程，false: 禁用)",
			},
			&cli.StringFlag{
				Name:    "tp",
				Aliases: []string{"template"},
				Value:   "",
				Usage:   "模板文件或目录 / Template file or directory",
			},
			&cli.IntFlag{
				Name:    "th",
				Aliases: []string{"thread"},
				Value:   100,
				Usage:   "指纹扫描线程数 / Fingerscan thread count",
			},
			&cli.StringFlag{
				Name:    "tag",
				Aliases: []string{"tags"},
				Value:   "",
				Usage:   "POC标签，例如：cve,sqli / POC tag, e.g: cve,sqli",
			},
			&cli.StringFlag{
				Name:    "itags",
				Aliases: []string{"include-tags"},
				Value:   "",
				Usage:   "包含的POC标签，例如：cve,sqli / Include POC tags, e.g: cve,sqli",
			},
			&cli.StringFlag{
				Name:    "etags",
				Aliases: []string{"exclude-tags"},
				Value:   "Nginx,Apache-Tomcat",
				Usage:   "排除的POC标签，例如：dos,fuzz / Exclude POC tags, e.g: dos,fuzz",
			},
			&cli.IntFlag{
				Name:    "to",
				Aliases: []string{"timeout"},
				Value:   7,
				Usage:   "Web请求超时时间（秒） / Web request timeout in seconds",
			},
			&cli.BoolFlag{
				Name:    "dbg",
				Aliases: []string{"debug"},
				Value:   false,
				Usage:   "显示请求和响应数据包 / Show request and response data packet",
			},
			&cli.BoolFlag{
				Name:    "ndns",
				Aliases: []string{"nodnslog"},
				Value:   false,
				Usage:   "禁用Interactsh引擎（多线程模式下无法禁用） / Disable Interactsh engine",
			},
			&cli.StringFlag{
				Name:    "dns",
				Aliases: []string{"dnslog-server"},
				Value:   "",
				Usage:   "自定义DNSLog服务器地址 / Custom DNSLog server address",
			},
			&cli.StringFlag{
				Name:    "dnstoken",
				Aliases: []string{"dnslog-token"},
				Value:   "",
				Usage:   "自定义DNSLog授权令牌 / Custom DNSLog authorization token",
			},
			&cli.BoolFlag{
				Name:    "cd",
				Aliases: []string{"condel"},
				Value:   true,
				Usage:   "删除配置文件 / Delete configuration file",
			},
			&cli.StringFlag{
				Name:    "x",
				Aliases: []string{"proxy"},
				Usage:   "设置代理，例如：http://127.0.0.1:8080 | sock5://127.0.0.1 / Set proxy, e.g: http://127.0.0.1:8080 | sock://127.0.0.1",
			},
			&cli.StringFlag{
				Name:    "r",
				Aliases: []string{"report"},
				Value:   time.Now().Format("2006-01-02 15-04-05") + ".html",
				Usage:   "输出HTML文件路径 / Output HTML file path",
			},
			&cli.StringFlag{
				Name:    "o",
				Aliases: []string{"output"},
				Value:   "log.txt",
				Usage:   "输出日志记录 / Output log",
			},
			&cli.BoolFlag{
				Name:    "np",
				Aliases: []string{"noping"},
				Value:   false,
				Usage:   "禁用存活探测，对所有IP进行端口扫描 / Disable alive detection, scan all IPs",
			},
			&cli.StringFlag{
				Name:    "p",
				Aliases: []string{"port"},
				Value:   "default",
				Usage:   "指定端口扫描范围，例如：80,443,8080-8090，或使用预定义列表：db(数据库)、web(网站)、mail(邮件)、remote(远程管理)、file(文件服务)、mq(消息队列)、vuln(漏洞)、unauth(未授权)、top100(常用端口)、plugin(所有插件端口) / Specify port scan range, e.g: 80,443,8080-8090, or use predefined lists: db, web, mail, remote, file, mq, vuln, unauth, top100, plugin",
			},
			&cli.StringFlag{
				Name:    "pn",
				Aliases: []string{"exclude-port"},
				Value:   "9100,9103,9101,9102",
				Usage:   "指定不需要扫描的端口范围，例如：8080,8443,9000-9100 / Specify ports to exclude from scanning, e.g: 8080,8443,9000-9100",
			},
			&cli.BoolFlag{
				Name:    "pf",
				Aliases: []string{"portfinger"},
				Value:   true,
				Usage:   "启用服务指纹识别 / Enable service fingerprint identification",
			},
			&cli.BoolFlag{
				Name:    "nw",
				Aliases: []string{"noweb"},
				Value:   false,
				Usage:   "禁用Web扫描，仅执行IP/端口扫描 / Disable web scanning, only perform IP/port scanning",
			},
			&cli.BoolFlag{
				Name:    "rport",
				Aliases: []string{"randomport"},
				Value:   false,
				Usage:   "随机化端口扫描顺序 / Randomize port scan order",
			},
			&cli.IntFlag{
				Name:    "tt",
				Aliases: []string{"tcp-threads"},
				Value:   1000,
				Usage:   "TCP端口扫描线程数 / TCP port scan thread count",
			},
			&cli.IntFlag{
				Name:    "tm",
				Aliases: []string{"tcp-timeout"},
				Value:   5,
				Usage:   "TCP端口扫描超时时间（秒） / TCP port scan timeout in seconds",
			},
			&cli.IntFlag{
				Name:    "pt",
				Aliases: []string{"ports-threshold"},
				Value:   1000,
				Usage:   "端口阈值，超过此值认为可能是防火墙 / Port threshold, if exceeded, it may be a firewall",
			},
			&cli.BoolFlag{
				Name:    "df",
				Aliases: []string{"disable-fastjson"},
				Value:   false,
				Usage:   "禁用Fastjson探测 / Disable Fastjson detection",
			},
			&cli.BoolFlag{
				Name:    "ds",
				Aliases: []string{"disable-shiro"},
				Value:   false,
				Usage:   "禁用Shiro探测 / Disable Shiro detection",
			},
			&cli.BoolFlag{
				Name:    "l4j",
				Aliases: []string{"log4j"},
				Value:   true,
				Usage:   "启用Log4j2、SpringBoot-Actuator未授权检查漏洞检查 / Enable Log4j2、SpringBoot-Actuator vulnerability check",
			},
			&cli.BoolFlag{
				Name:    "st",
				Aliases: []string{"stealth"},
				Value:   false,
				Usage:   "无痕模式，关闭所有主动探测(Fastjson、Shiro、深度指纹扫描、Log4j)以防止WAF封禁 / Stealth mode, disable all active detection to prevent WAF blocking",
			},
			&cli.StringFlag{
				Name:    "cr",
				Aliases: []string{"crawler"},
				Value:   "auto",
				Usage:   "爬虫模式：auto（自动检测需要JS的网站）、force（强制所有网站使用爬虫）、off（完全禁用爬虫） / Crawler mode: auto, force, off",
			},
			&cli.BoolFlag{
				Name:    "crd",
				Aliases: []string{"crawler-debug"},
				Value:   false,
				Usage:   "启用爬虫调试模式，输出详细日志（使用[crawler]前缀） / Enable crawler debug mode with detailed logs",
			},
			&cli.BoolFlag{
				Name:    "nobr",
				Aliases: []string{"noplugin"},
				Value:   false,
				Usage:   "禁用插件系统 / Disable plugin system",
			},
			&cli.StringFlag{
				Name:  "user",
				Usage: "指定用户名列表，可以是逗号分隔的字符串或文件路径（每行一个用户名）/ Specify usernames, comma-separated string or file path (one username per line)",
			},
			&cli.StringFlag{
				Name:  "pwd",
				Usage: "指定密码列表，可以是逗号分隔的字符串或文件路径（每行一个密码）/ Specify passwords, comma-separated string or file path (one password per line)",
			},
			&cli.StringFlag{
				Name:  "usera",
				Usage: "追加用户名列表，可以是逗号分隔的字符串或文件路径（每行一个用户名）/ Append usernames, comma-separated string or file path (one username per line)",
			},
			&cli.StringFlag{
				Name:  "pwda",
				Usage: "追加密码列表，可以是逗号分隔的字符串或文件路径（每行一个密码）/ Append passwords, comma-separated string or file path (one password per line)",
			},
			&cli.StringFlag{
				Name:    "pp",
				Aliases: []string{"plugin-params"},
				Usage:   "插件自定义参数，例如RTSP的URI路径：-pp \"/live.sdp,/cam/realmonitor\" 或SMB域名：-pp \"domain=WORKGROUP\" / Plugin custom parameters, e.g. RTSP URI paths or SMB domain",
			},
			&cli.IntFlag{
				Name:    "bt",
				Aliases: []string{"brute-threads"},
				Value:   10,
				Usage:   "爆破线程数 / Brute force thread count",
			},
			&cli.IntFlag{
				Name:    "bto",
				Aliases: []string{"brute-timeout"},
				Value:   5,
				Usage:   "爆破超时时间（秒） / Brute force timeout in seconds",
			},
			&cli.IntFlag{
				Name:    "bma",
				Aliases: []string{"brute-max-attempts"},
				Value:   500,
				Usage:   "最大爆破尝试次数 / Maximum brute force attempts",
			},
			&cli.StringFlag{
				Name:    "m",
				Aliases: []string{"module"},
				Value:   "",
				Usage:   "指定要调用的插件名称，例如：GaussDB、MySQL等（不区分大小写）/ Specify the plugin to call, e.g: GaussDB, MySQL, etc. (case insensitive)",
			},
			&cli.BoolFlag{
				Name:    "dr",
				Aliases: []string{"domain-resolve"},
				Value:   false,
				Usage:   "只进行域名解析，不进行端口扫描 / Only perform domain resolution without port scanning",
			},
			&cli.BoolFlag{
				Name:    "ra",
				Aliases: []string{"resolve-all"},
				Value:   false,
				Usage:   "使用解析IP模式：先解析域名为IP再扫描（默认使用直接域名扫描）/ Use IP resolution mode: resolve domain to IPs first (default: direct domain scanning)",
			},
			&cli.BoolFlag{
				Name:    "ipv6",
				Aliases: []string{"prefer-ipv6"},
				Value:   false,
				Usage:   "优先使用IPv6地址进行扫描 / Prefer IPv6 addresses for scanning",
			},
			&cli.BoolFlag{
				Name:    "sc",
				Aliases: []string{"skip-cdn"},
				Value:   false,
				Usage:   "跳过CDN保护的域名 / Skip CDN-protected domains",
			},
			&cli.BoolFlag{
				Name:    "fri",
				Aliases: []string{"force-real-ip"},
				Value:   false,
				Usage:   "强制使用真实IP进行扫描（尝试绕过CDN）/ Force scanning with real IPs (bypass CDN)",
			},
			&cli.BoolFlag{
				Name:    "nwc",
				Aliases: []string{"no-wildcard"},
				Value:   false,
				Usage:   "禁用泛解析过滤（默认启用泛解析检测以跳过重复目标）/ Disable wildcard filtering (default: enable wildcard detection to skip duplicate targets)",
			},
		},
		Action: func(c *cli.Context) error {
			// 检查是否有参数传入
			if c.NumFlags() == 0 {
				cli.ShowAppHelp(c) // 显示帮助信息
				os.Exit(0)         // 退出程序
			}

			// 记录命令行中显式设置的参数
			deepScanSet := c.IsSet("d") || c.IsSet("deep")
			log4j2Set := c.IsSet("l4j") || c.IsSet("log4j")
			disableFastjsonSet := c.IsSet("df") || c.IsSet("disable-fastjson")
			disableShiroSet := c.IsSet("ds") || c.IsSet("disable-shiro")
			crawlerModeSet := c.IsSet("cr") || c.IsSet("crawler")

			// 获取参数值
			cf.Target = c.String("t")
			cf.DeepScan = c.Bool("d")
			cf.Thread = c.Int("th")
			cf.RootPath = c.Bool("rp")
			cf.Tags = c.String("tag")
			cf.IncludeTags = c.String("itags")
			cf.ExcludeTags = c.String("etags")
			cf.NoPoc = c.String("vul")
			cf.Timeout = c.Int("to")
			cf.Proxy = c.String("x")
			cf.Debug = c.Bool("dbg")
			cf.Log4j2 = c.Bool("l4j")
			cf.ConDel = c.Bool("cd")
			cf.DisableDNSLog = c.Bool("ndns")
			cf.Template = c.String("tp")
			cf.Html = c.String("r")
			cf.Log = c.String("o")
			cf.UsePing = c.Bool("np")
			cf.PortRange = c.String("p")
			cf.ExcludePort = c.String("pn")
			cf.ServiceCheck = c.Bool("pf")
			cf.WebScan = !c.Bool("nw")      // 反转noweb参数，默认执行Web扫描
			cf.RandomPort = c.Bool("rport") // 是否随机化端口扫描顺序
			cf.TCPThreads = c.Int("tt")     // TCP端口扫描线程数
			cf.TCPTimeout = c.Int("tm")     // TCP端口扫描超时时间
			cf.PortsThreshold = c.Int("pt") // 端口阈值
			cf.DisableFastjson = c.Bool("df")
			cf.DisableShiro = c.Bool("ds")
			cf.Stealth = c.Bool("st")
			cf.CrawlerMode = c.String("cr")
			cf.CrawlerDebug = c.Bool("crd")
			cf.DNSLogServer = c.String("dns")
			cf.DNSLogToken = c.String("dnstoken")
			cf.NoPlugin = c.Bool("nobr")             // 是否禁用插件系统
			cf.ModuleName = c.String("m")            // 指定要调用的插件名称
			cf.DomainResolve = c.Bool("dr")          // 是否只进行域名解析
			cf.ResolveAll = c.Bool("ra")             // 是否解析所有DNS记录类型
			cf.PreferIPv6 = c.Bool("ipv6")           // 是否优先使用IPv6地址
			cf.SkipCDN = c.Bool("skip-cdn")          // 是否跳过CDN保护的域名
			cf.ForceRealIP = c.Bool("force-real-ip") // 是否强制使用真实IP

			// 获取爆破相关参数
			cf.UserList = c.String("user")
			cf.PasswordList = c.String("pwd")
			cf.AppendUserList = c.String("usera")
			cf.AppendPwdList = c.String("pwda")
			cf.BruteThreads = c.Int("bt")
			cf.BruteTimeout = c.Int("bto")
			cf.BruteMaxAttempts = c.Int("bma")

			// 获取插件自定义参数
			cf.PluginParams = c.String("pp")

			// 检查-m参数是否指定，如果指定了，必须同时指定-p参数
			if cf.ModuleName != "" {
				// 特殊处理：当 -m 为 h/help/ls 时，设置一个标志，稍后在主程序中显示插件列表
				if cf.ModuleName == "h" || cf.ModuleName == "help" || cf.ModuleName == "ls" {
					cf.ShowPluginList = true
					return nil
				}

				if cf.PortRange == "" {
					fmt.Println("[错误] 使用-m参数指定插件时，必须同时使用-p参数指定端口")
					os.Exit(1)
				}
			}

			// 如果启用了无痕模式，则禁用所有主动探测，但允许命令行参数覆盖
			if cf.Stealth {
				// 只有当命令行中没有显式设置相关参数时，才应用无痕模式的默认设置
				if !disableFastjsonSet {
					cf.DisableFastjson = true
				}
				if !disableShiroSet {
					cf.DisableShiro = true
				}
				if !deepScanSet {
					cf.DeepScan = false
				}
				if !log4j2Set {
					cf.Log4j2 = false
				}
				// 不再默认禁用爬虫功能
				if !crawlerModeSet {
					cf.CrawlerMode = "off"
				}

				// 输出无痕模式状态信息
				fmt.Println("[提示] 无痕模式已启用，禁用主动探测以防止WAF封禁，可通过命令行参数覆盖默认设置")
			}

			// 设置全局超时时间
			clients.SetDefaultTimeout(cf.Timeout)

			// 更新GlobalConfig
			GlobalConfig = &cf

			// 处理爆破用户名和密码参数
			if cf.UserList != "" || cf.PasswordList != "" || cf.AppendUserList != "" || cf.AppendPwdList != "" ||
				cf.BruteThreads > 0 || cf.BruteTimeout > 0 || cf.BruteMaxAttempts > 0 {
				// 预初始化配置
				initBruteConfig()
			}

			return nil
		},
	}
	if err := app.Run(os.Args); err != nil {
		log.Fatal(err)
	}
	return &cf
}

// 初始化爆破配置
func initBruteConfig() {
	// 确保全局爆破配置已初始化
	if GlobalBruteConfig == nil {
		GlobalBruteConfig = &BruteConfig{
			Usernames:        make([]string, 0), // 保留但不使用
			Passwords:        make([]string, 0), // 保留但不使用
			UserDict:         make(map[string][]string),
			DefaultPasswords: make([]string, 0),
			Threads:          10,
			Timeout:          5,
			MaxAttempts:      1000,
		}
	}

	// 如果命令行指定了爆破线程数，更新全局配置
	if GlobalConfig.BruteThreads > 0 {
		GlobalBruteConfig.Threads = GlobalConfig.BruteThreads
	}

	// 如果命令行指定了爆破超时时间，更新全局配置
	if GlobalConfig.BruteTimeout > 0 {
		GlobalBruteConfig.Timeout = GlobalConfig.BruteTimeout
	}

	// 如果命令行指定了最大爆破尝试次数，更新全局配置
	if GlobalConfig.BruteMaxAttempts > 0 {
		GlobalBruteConfig.MaxAttempts = GlobalConfig.BruteMaxAttempts
	}

	// 处理用户名参数
	if GlobalConfig.UserList != "" {
		// 加载用户指定的用户名列表
		usernames := loadUserOrPassword(GlobalConfig.UserList, false)

		if len(usernames) > 0 {
			// -user参数：替换所有服务的UserDict中的用户名列表
			// 获取UserDict中的所有服务，直接遍历替换
			for service := range GlobalBruteConfig.UserDict {
				// 完全替换而不是追加
				GlobalBruteConfig.UserDict[service] = usernames
			}

			// 如果UserDict为空，则使用常见服务创建条目
			// if len(GlobalBruteConfig.UserDict) == 0 {
			// 	commonServices := []string{
			// 		"MySQL", "MSSQL", "PostgreSQL", "Oracle", "MongoDB", "Redis",
			// 		"SSH", "FTP", "Telnet", "SMB", "RDP", "Memcached",
			// 		"Elastic", "RabbitMQ", "Kafka", "ActiveMQ", "LDAP",
			// 		"SMTP", "IMAP", "POP3", "Zabbix", "Rsync",
			// 		"Cassandra", "Neo4j", "RTSP", "DM", "MQTT", "SOCKS5", "Kingbase",
			// 	}
			// 	for _, service := range commonServices {
			// 		GlobalBruteConfig.UserDict[service] = usernames
			// 	}
			// }
		}
	}

	// 处理密码参数
	if GlobalConfig.PasswordList != "" {
		// 加载用户指定的密码列表
		passwords := loadUserOrPassword(GlobalConfig.PasswordList, false)

		if len(passwords) > 0 {
			// -pwd参数：替换DefaultPasswords列表
			GlobalBruteConfig.DefaultPasswords = passwords // 完全替换而不是追加
		}
	}

	// 处理追加用户名参数
	if GlobalConfig.AppendUserList != "" {
		// 加载用户指定的追加用户名列表
		appendUsers := loadUserOrPassword(GlobalConfig.AppendUserList, false)

		if len(appendUsers) > 0 {
			// -usera参数：追加到所有服务的UserDict中
			for service := range GlobalBruteConfig.UserDict {
				GlobalBruteConfig.UserDict[service] = append(GlobalBruteConfig.UserDict[service], appendUsers...)
			}

			// 如果UserDict为空，则为常见服务创建条目
			if len(GlobalBruteConfig.UserDict) == 0 {
				commonServices := []string{"MySQL", "MSSQL", "PostgreSQL", "Oracle", "MongoDB", "Redis", "SSH", "FTP", "Telnet", "SMB"}
				for _, service := range commonServices {
					GlobalBruteConfig.UserDict[service] = appendUsers
				}
			}
		}
	}

	// 处理追加密码参数
	if GlobalConfig.AppendPwdList != "" {
		// 加载用户指定的追加密码列表
		appendPwds := loadUserOrPassword(GlobalConfig.AppendPwdList, false)

		if len(appendPwds) > 0 {
			// -pwda参数：追加到DefaultPasswords列表
			GlobalBruteConfig.DefaultPasswords = append(GlobalBruteConfig.DefaultPasswords, appendPwds...)
		}
	}
}

// 加载用户名或密码列表
func loadUserOrPassword(input string, isPassword bool) []string {
	// 判断是否为文件路径
	if _, err := os.Stat(input); err == nil {
		// 读取文件内容
		data, err := os.ReadFile(input)
		if err != nil {
			fmt.Printf("[错误] 读取文件失败: %s, %v\n", input, err)
			return nil
		}

		// 按行分割
		lines := strings.Split(string(data), "\n")
		var result []string
		for _, line := range lines {
			line = strings.TrimSpace(line)
			if line != "" {
				result = append(result, line)
			}
		}
		return result
	}

	// 否则按逗号分隔
	items := strings.Split(input, ",")
	var result []string
	for _, item := range items {
		item = strings.TrimSpace(item)
		if item != "" {
			result = append(result, item)
		}
	}
	return result
}
