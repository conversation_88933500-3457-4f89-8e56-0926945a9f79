package core

import (
	"Lightx/internal/common"
	"Lightx/pkg/clients"
	"Lightx/pkg/netutil"
	"Lightx/pkg/portscan"
	"Lightx/pkg/utils"
	"Lightx/pkg/webscan"
	"Lightx/plugins/core"
	_ "Lightx/plugins/services" // 导入所有服务插件
	"fmt"
	"os"
	"os/signal"
	"strconv"
	"strings"
	"syscall"
	"time"

	"github.com/projectdiscovery/gologger"
	"github.com/projectdiscovery/gologger/levels"
)

// Engine 是Lightx的核心引擎
type Engine struct {
	Options *common.Options
	TempDir string
}

// NewEngine 创建一个新的引擎实例
func NewEngine(options *common.Options) *Engine {
	return &Engine{
		Options: options,
	}
}

// Initialize 初始化引擎
func (e *Engine) Initialize() error {
	// 实现优先级加载：优先从本地加载，如果本地不存在config则使用embed
	// if _, err := os.Stat("config"); os.IsNotExist(err) {
	// 	// 如果 config 目录不存在，使用embed模式，不释放文件
	// 	e.TempDir = "config" // 设置默认值，使用embed
	// 	// gologger.Info().Msg("本地config目录不存在，使用embed模式加载配置")
	// } else {
	// 	// 如果 config 目录存在，优先使用本地文件
	// 	e.TempDir = "config"
	// 	// gologger.Info().Msg("检测到本地config目录，优先使用本地配置文件")
	// }

	// 设置信号处理
	e.setupSignalHandler()

	// // 检查是否有可用的目标
	// if e.Options.Target == "" {
	// 	return fmt.Errorf("未指定目标，请使用 -t 参数指定目标")
	// }

	// 如果启用了Debug模式，设置gologger的日志级别为Debug
	if e.Options.Debug {
		gologger.DefaultLogger.SetMaxLevel(levels.LevelDebug)
		gologger.Debug().Msg("调试模式已启用，将显示详细日志")
	}

	// 初始化插件工作池
	core.InitDefaultWorkerPool()

	return nil
}

// Run 运行引擎
func (e *Engine) Run() {
	// 重置漏洞计数和插件任务统计
	webscan.ResetVulnerabilityCount()
	core.ResetTaskStats()

	// 根据目标类型进行处理
	if _, err := os.Stat(e.Options.Target); err == nil {
		// 目标是文件，处理文件内容
		e.processFile()
	} else {
		// 处理单个目标
		e.processSingleTarget()
	}

	// 等待所有插件任务完成
	if !e.Options.NoPlugin && core.DefaultWorkerPool != nil {
		// 获取任务队列长度
		queueLen := len(core.DefaultWorkerPool.GetTaskQueue())
		if queueLen > 0 {
			gologger.Info().Msgf("等待 %d 个插件任务完成...", queueLen)

			// 等待任务队列清空或超时
			maxWait := 60 * time.Second // 增加最大等待时间
			startWait := time.Now()
			for len(core.DefaultWorkerPool.GetTaskQueue()) > 0 {
				if time.Since(startWait) > maxWait {
					gologger.Warning().Msg("等待插件任务超时，部分任务可能未完成")
					break
				}
				time.Sleep(500 * time.Millisecond)
			}

			// 给已经从队列取出的任务一些额外时间完成
			time.Sleep(2 * time.Second)
		}
	}
}

// Cleanup 清理资源
func (e *Engine) Cleanup() {
	// 停止工作池
	core.StopDefaultWorkerPool()

	// 释放全局nuclei引擎
	webscan.ReleaseGlobalThreadSafeNucleiEngine()

	// 根据 options.configdel 参数决定是否删除临时目录
	if e.Options.ConDel {
		webscan.CleanupTempDir(e.TempDir)
	}

	// 检查是否需要删除报告文件
	reportPath := e.Options.Html
	if reportPath != "" {
		// 首先检查报告文件是否存在
		_, err := os.Stat(reportPath)
		if err != nil {
			// 如果报告文件不存在，直接返回，不执行后续逻辑
			if os.IsNotExist(err) {
				return
			}
			gologger.Error().Msgf("检查报告文件状态失败: %v", err)
			return
		}

		// 获取漏洞扫描结果数量
		vulCount := webscan.GetVulnerabilityCount()
		// 获取插件扫描成功结果数量（只计算真正成功的结果）
		pluginSuccessCount := core.GetSuccessTaskCount()

		// 如果没有漏洞和插件扫描成功结果，则删除报告文件
		if vulCount == 0 && pluginSuccessCount == 0 {
			gologger.Info().Msgf("未发现漏洞和有效插件扫描结果，删除报告文件: %s", reportPath)
			if err := os.Remove(reportPath); err != nil {
				gologger.Error().Msgf("删除报告文件失败: %v", err)
			}
		} else {
			gologger.Info().Msgf("扫描完成，发现 %d 个漏洞和 %d 个插件扫描成功结果，报告已保存至: %s", vulCount, pluginSuccessCount, reportPath)
		}
	}
}

// 处理文件目标
func (e *Engine) processFile() {
	// 读取文件内容
	content, err := os.ReadFile(e.Options.Target)
	if err != nil {
		gologger.Error().Msgf("读取文件失败: %s", err)
		return
	}

	// 按行分割内容
	lines := strings.Split(string(content), "\n")
	var webTargets []string
	var ipTargets []string
	var ipPortTargets []string

	// 处理每一行
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 检查是否为URL
		if utils.IsURL(line) {
			webTargets = append(webTargets, line)
		} else if IsIPWithPort(line) {
			// 处理IP:端口格式
			ipPortTargets = append(ipPortTargets, line)
		} else if netutil.IsIPv4(line) {
			// 处理纯IP
			ipTargets = append(ipTargets, line)
		} else if netutil.IsIPv4Range(line) {
			// 处理IP段
			ipTargets = append(ipTargets, line)
		} else if netutil.IsDomain(line) {
			// 处理域名
			ProcessDomain(&common.Options{
				Target:    line,
				WebScan:   e.Options.WebScan,
				Timeout:   e.Options.Timeout,
				Proxy:     e.Options.Proxy,
				Thread:    e.Options.Thread,
				PortRange: e.Options.PortRange,
				UsePing:   e.Options.UsePing,
			})
		}
	}

	// 存储所有端口扫描结果，用于后续插件处理
	var allPortResults []portscan.PortResult

	// 处理IP:端口目标
	for _, target := range ipPortTargets {
		parts := strings.Split(target, ":")
		ip := parts[0]
		port, _ := strconv.Atoi(parts[1])

		// 创建PortResult对象，用于端口服务识别
		pr := portscan.Connect(ip, port, e.Options.Timeout, clients.Proxy{})
		pr.IP = ip
		pr.Port = port

		if pr.Status {
			// 输出端口服务信息
			gologger.Info().Msgf("[Port] %s:%s:%d", pr.Server, pr.IP, pr.Port)

			// 添加到结果列表
			allPortResults = append(allPortResults, pr)

			// 如果是Web服务，添加到Web目标
			if pr.Server == "http" || pr.Server == "https" {
				target := fmt.Sprintf("%s://%s:%d", pr.Server, pr.IP, pr.Port)
				webTargets = append(webTargets, target)
			} else if pr.Port == 80 || pr.Port == 8080 || pr.Port == 8000 || pr.Port == 8888 {
				target := fmt.Sprintf("http://%s:%d", pr.IP, pr.Port)
				webTargets = append(webTargets, target)
			} else if pr.Port == 443 || pr.Port == 8443 {
				target := fmt.Sprintf("https://%s:%d", pr.IP, pr.Port)
				webTargets = append(webTargets, target)
			}
		}
	}

	// 处理IP目标
	if len(ipTargets) > 0 {
		// 执行IP存活探测
		gologger.Info().Msgf("开始对 %d 个IP进行存活探测", len(ipTargets))
		aliveIPs := e.checkAliveIPs(ipTargets)

		if len(aliveIPs) > 0 {
			// 执行端口扫描
			results := e.scanPorts(aliveIPs)

			// 添加到结果列表
			allPortResults = append(allPortResults, results...)

			// 收集Web服务目标
			for _, result := range results {
				if result.Server == "http" || result.Server == "https" {
					target := fmt.Sprintf("%s://%s:%d", result.Server, result.IP, result.Port)
					webTargets = append(webTargets, target)
				} else if (result.Port == 80 || result.Port == 8080 || result.Port == 8000 || result.Port == 8888) && result.Server != "http" && result.Server != "https" {
					target := fmt.Sprintf("http://%s:%d", result.IP, result.Port)
					webTargets = append(webTargets, target)
				} else if (result.Port == 443 || result.Port == 8443) && result.Server != "http" && result.Server != "https" {
					target := fmt.Sprintf("https://%s:%d", result.IP, result.Port)
					webTargets = append(webTargets, target)
				}
			}
		}
	}

	// 如果有Web目标，执行Web扫描
	if len(webTargets) > 0 && e.Options.WebScan {
		gologger.Info().Msgf("从文件中读取到 %d 个Web目标", len(webTargets))
		RunWebScan(webTargets, e.Options)
	}

	// Web扫描完成后，执行插件任务（如果未禁用插件系统）
	if len(allPortResults) > 0 && !e.Options.NoPlugin {
		// gologger.Info().Msgf("正在启动插件系统处理扫描结果...")
		core.DispatchPluginTasks(allPortResults)
	} else if e.Options.NoPlugin {
		gologger.Info().Msgf("插件系统已禁用，跳过插件任务执行")
	}
}

// 检查IP存活
func (e *Engine) checkAliveIPs(ipTargets []string) []string {
	var aliveIPs []string

	if e.Options.UsePing {
		// 如果UsePing为true，跳过存活探测，直接使用所有IP
		gologger.Info().Msgf("已禁用存活探测，将对所有 %d 个IP进行端口扫描", len(ipTargets))
		aliveIPs = ipTargets
	} else {
		// 执行IP存活探测
		gologger.Info().Msgf("开始对 %d 个IP进行存活探测", len(ipTargets))
		aliveIPs = e.scanAliveIPs(ipTargets)
		gologger.Info().Msgf("IP存活探测完成，发现 %d 个存活IP", len(aliveIPs))
	}

	return aliveIPs
}

// 扫描存活IP
func (e *Engine) scanAliveIPs(ipTargets []string) []string {
	return portscan.CheckLive(ipTargets, e.Options.UsePing, e.Options.Log)
}

// 扫描端口
func (e *Engine) scanPorts(aliveIPs []string) []portscan.PortResult {
	// 获取报告路径
	reportPath := e.Options.Html

	return portscan.ScanAndOutputResult(
		aliveIPs,
		e.Options.PortRange,
		e.Options.ExcludePort,
		e.Options.TCPThreads,
		e.Options.TCPTimeout,
		e.Options.Proxy,
		e.Options.Log,
		e.Options.RandomPort,
		e.Options.PortsThreshold,
		reportPath,
		e.Options.ServiceCheck,
	)
}

// 处理单个目标
func (e *Engine) processSingleTarget() {
	// 首先尝试解析多目标格式（逗号分隔、IP范围等）
	targets, err := netutil.ParseMultipleTargets(e.Options.Target)
	if err != nil {
		gologger.Error().Msgf("解析目标失败: %v", err)
		return
	}

	// 如果解析出多个目标，递归处理每个目标
	if len(targets) > 1 {
		gologger.Info().Msgf("检测到多个目标，共 %d 个", len(targets))
		for i, target := range targets {
			gologger.Info().Msgf("处理目标 [%d/%d]: %s", i+1, len(targets), target)

			// 创建新的选项副本
			newOptions := *e.Options
			newOptions.Target = target

			// 递归处理单个目标
			newEngine := &Engine{Options: &newOptions}
			newEngine.processSingleTargetInternal()
		}
		return
	}

	// 单个目标，使用原有逻辑
	e.processSingleTargetInternal()
}

// processSingleTargetInternal 处理单个目标的内部逻辑
func (e *Engine) processSingleTargetInternal() {
	if IsIPWithPort(e.Options.Target) {
		// IP:端口 格式，从端口扫描开始
		ProcessIPWithPort(e.Options)
	} else if netutil.IsIP(e.Options.Target) || netutil.IsIP(netutil.CleanIPv6(e.Options.Target)) {
		// 纯IP地址（IPv4或IPv6，包括带接口标识符的IPv6），执行完整流程：IP存活 -> 端口扫描 -> Web扫描
		ProcessIP(e.Options)
	} else if netutil.IsIPRange(e.Options.Target) {
		// IP段（IPv4或IPv6），执行IP段扫描流程
		ProcessIPRange(e.Options)
	} else if netutil.IsDomain(e.Options.Target) {
		// 域名处理
		if e.Options.DomainResolve {
			// 只进行域名解析，不进行端口扫描
			ProcessDomainResolveOnly(e.Options)
		} else {
			// 执行完整的域名解析和端口扫描
			ProcessDomain(e.Options)
		}
	} else if utils.IsURL(e.Options.Target) {
		// URL，直接进行Web扫描
		ProcessURL(e.Options)
	} else {
		gologger.Error().Msgf("不支持的目标格式: %s", e.Options.Target)
	}
}

// 设置信号处理
func (e *Engine) setupSignalHandler() {
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)
	go func() {
		<-c
		fmt.Println("\r- 检测到Ctrl+C，正在退出...")
		portscan.ExitFunc = true

		// 停止工作池
		core.StopDefaultWorkerPool()

		// 清理资源
		e.Cleanup()

		os.Exit(0)
	}()
}
