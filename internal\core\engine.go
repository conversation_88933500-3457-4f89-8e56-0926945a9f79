package core

import (
	"Lightx/internal/common"
	"Lightx/pkg/clients"
	"Lightx/pkg/netutil"
	"Lightx/pkg/portscan"
	"Lightx/pkg/utils"
	"Lightx/pkg/webscan"
	"Lightx/plugins/core"
	_ "Lightx/plugins/services" // 导入所有服务插件
	"fmt"
	"os"
	"os/signal"
	"regexp"
	"strconv"
	"strings"
	"syscall"
	"time"

	"github.com/projectdiscovery/gologger"
	"github.com/projectdiscovery/gologger/levels"
)

// Engine 是Lightx的核心引擎
type Engine struct {
	Options *common.Options
	TempDir string
}

// NewEngine 创建一个新的引擎实例
func NewEngine(options *common.Options) *Engine {
	return &Engine{
		Options: options,
	}
}

// Initialize 初始化引擎
func (e *Engine) Initialize() error {
	// 实现优先级加载：优先从本地加载，如果本地不存在config则使用embed
	// if _, err := os.Stat("config"); os.IsNotExist(err) {
	// 	// 如果 config 目录不存在，使用embed模式，不释放文件
	// 	e.TempDir = "config" // 设置默认值，使用embed
	// 	// gologger.Info().Msg("本地config目录不存在，使用embed模式加载配置")
	// } else {
	// 	// 如果 config 目录存在，优先使用本地文件
	// 	e.TempDir = "config"
	// 	// gologger.Info().Msg("检测到本地config目录，优先使用本地配置文件")
	// }

	// 设置信号处理
	e.setupSignalHandler()

	// // 检查是否有可用的目标
	// if e.Options.Target == "" {
	// 	return fmt.Errorf("未指定目标，请使用 -t 参数指定目标")
	// }

	// 如果启用了Debug模式，设置gologger的日志级别为Debug
	if e.Options.Debug {
		gologger.DefaultLogger.SetMaxLevel(levels.LevelDebug)
		gologger.Debug().Msg("调试模式已启用，将显示详细日志")
	}

	// 初始化插件工作池
	core.InitDefaultWorkerPool()

	return nil
}

// Run 运行引擎
func (e *Engine) Run() {
	// 重置漏洞计数和插件任务统计
	webscan.ResetVulnerabilityCount()
	core.ResetTaskStats()

	// 根据目标类型进行处理
	if _, err := os.Stat(e.Options.Target); err == nil {
		// 目标是文件，处理文件内容
		e.processFile()
	} else {
		// 处理单个目标
		e.processSingleTarget()
	}

	// 等待所有插件任务完成
	if !e.Options.NoPlugin && core.DefaultWorkerPool != nil {
		// 获取任务队列长度
		queueLen := len(core.DefaultWorkerPool.GetTaskQueue())
		if queueLen > 0 {
			gologger.Info().Msgf("等待 %d 个插件任务完成...", queueLen)

			// 等待任务队列清空或超时
			maxWait := 60 * time.Second // 增加最大等待时间
			startWait := time.Now()
			for len(core.DefaultWorkerPool.GetTaskQueue()) > 0 {
				if time.Since(startWait) > maxWait {
					gologger.Warning().Msg("等待插件任务超时，部分任务可能未完成")
					break
				}
				time.Sleep(500 * time.Millisecond)
			}

			// 给已经从队列取出的任务一些额外时间完成
			time.Sleep(2 * time.Second)
		}
	}
}

// Cleanup 清理资源
func (e *Engine) Cleanup() {
	// 停止工作池
	core.StopDefaultWorkerPool()

	// 释放全局nuclei引擎
	webscan.ReleaseGlobalThreadSafeNucleiEngine()

	// 根据 options.configdel 参数决定是否删除临时目录
	if e.Options.ConDel {
		webscan.CleanupTempDir(e.TempDir)
	}

	// 检查是否需要删除报告文件
	reportPath := e.Options.Html
	if reportPath != "" {
		// 首先检查报告文件是否存在
		_, err := os.Stat(reportPath)
		if err != nil {
			// 如果报告文件不存在，直接返回，不执行后续逻辑
			if os.IsNotExist(err) {
				return
			}
			gologger.Error().Msgf("检查报告文件状态失败: %v", err)
			return
		}

		// 获取漏洞扫描结果数量
		vulCount := webscan.GetVulnerabilityCount()
		// 获取插件扫描成功结果数量（只计算真正成功的结果）
		pluginSuccessCount := core.GetSuccessTaskCount()

		// 如果没有漏洞和插件扫描成功结果，则删除报告文件
		if vulCount == 0 && pluginSuccessCount == 0 {
			gologger.Info().Msgf("未发现漏洞和有效插件扫描结果，删除报告文件: %s", reportPath)
			if err := os.Remove(reportPath); err != nil {
				gologger.Error().Msgf("删除报告文件失败: %v", err)
			}
		} else {
			gologger.Info().Msgf("扫描完成，发现 %d 个漏洞和 %d 个插件扫描成功结果，报告已保存至: %s", vulCount, pluginSuccessCount, reportPath)
		}
	}
}

// 处理文件目标
func (e *Engine) processFile() {
	// 读取文件内容
	content, err := os.ReadFile(e.Options.Target)
	if err != nil {
		gologger.Error().Msgf("读取文件失败: %s", err)
		return
	}

	// 按行分割内容
	lines := strings.Split(string(content), "\n")
	var webTargets []string
	var ipTargets []string
	var ipPortTargets []string

	// 处理每一行
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 检查是否为URL
		if utils.IsURL(line) {
			webTargets = append(webTargets, line)
		} else if IsIPWithPort(line) {
			// 处理IP:端口格式
			ipPortTargets = append(ipPortTargets, line)
		} else if netutil.IsIPv4(line) {
			// 处理纯IP
			ipTargets = append(ipTargets, line)
		} else if netutil.IsIPv4Range(line) {
			// 处理IP段
			ipTargets = append(ipTargets, line)
		} else if netutil.IsDomain(line) {
			// 处理域名
			ProcessDomain(&common.Options{
				Target:    line,
				WebScan:   e.Options.WebScan,
				Timeout:   e.Options.Timeout,
				Proxy:     e.Options.Proxy,
				Thread:    e.Options.Thread,
				PortRange: e.Options.PortRange,
				UsePing:   e.Options.UsePing,
			})
		}
	}

	// 存储所有端口扫描结果，用于后续插件处理
	var allPortResults []portscan.PortResult

	// 处理IP:端口目标
	for _, target := range ipPortTargets {
		parts := strings.Split(target, ":")
		ip := parts[0]
		port, _ := strconv.Atoi(parts[1])

		// 创建PortResult对象，用于端口服务识别
		pr := portscan.Connect(ip, port, e.Options.Timeout, clients.Proxy{})
		pr.IP = ip
		pr.Port = port

		if pr.Status {
			// 输出端口服务信息
			gologger.Info().Msgf("[Port] %s:%s:%d", pr.Server, pr.IP, pr.Port)

			// 添加到结果列表
			allPortResults = append(allPortResults, pr)

			// 如果是Web服务，添加到Web目标
			if pr.Server == "http" || pr.Server == "https" {
				target := fmt.Sprintf("%s://%s:%d", pr.Server, pr.IP, pr.Port)
				webTargets = append(webTargets, target)
			} else if pr.Port == 80 || pr.Port == 8080 || pr.Port == 8000 || pr.Port == 8888 {
				target := fmt.Sprintf("http://%s:%d", pr.IP, pr.Port)
				webTargets = append(webTargets, target)
			} else if pr.Port == 443 || pr.Port == 8443 {
				target := fmt.Sprintf("https://%s:%d", pr.IP, pr.Port)
				webTargets = append(webTargets, target)
			}
		}
	}

	// 处理IP目标
	if len(ipTargets) > 0 {
		// 执行IP存活探测
		gologger.Info().Msgf("开始对 %d 个IP进行存活探测", len(ipTargets))
		aliveIPs := e.checkAliveIPs(ipTargets)

		if len(aliveIPs) > 0 {
			// 执行端口扫描
			results := e.scanPorts(aliveIPs)

			// 添加到结果列表
			allPortResults = append(allPortResults, results...)

			// 收集Web服务目标
			for _, result := range results {
				if result.Server == "http" || result.Server == "https" {
					target := fmt.Sprintf("%s://%s:%d", result.Server, result.IP, result.Port)
					webTargets = append(webTargets, target)
				} else if (result.Port == 80 || result.Port == 8080 || result.Port == 8000 || result.Port == 8888) && result.Server != "http" && result.Server != "https" {
					target := fmt.Sprintf("http://%s:%d", result.IP, result.Port)
					webTargets = append(webTargets, target)
				} else if (result.Port == 443 || result.Port == 8443) && result.Server != "http" && result.Server != "https" {
					target := fmt.Sprintf("https://%s:%d", result.IP, result.Port)
					webTargets = append(webTargets, target)
				}
			}
		}
	}

	// 如果有Web目标，执行Web扫描
	if len(webTargets) > 0 && e.Options.WebScan {
		gologger.Info().Msgf("从文件中读取到 %d 个Web目标", len(webTargets))
		RunWebScan(webTargets, e.Options)
	}

	// Web扫描完成后，执行插件任务（如果未禁用插件系统）
	if len(allPortResults) > 0 && !e.Options.NoPlugin {
		// gologger.Info().Msgf("正在启动插件系统处理扫描结果...")
		core.DispatchPluginTasks(allPortResults)
	} else if e.Options.NoPlugin {
		gologger.Info().Msgf("插件系统已禁用，跳过插件任务执行")
	}
}

// 检查IP存活
func (e *Engine) checkAliveIPs(ipTargets []string) []string {
	var aliveIPs []string

	if e.Options.UsePing {
		// 如果UsePing为true，跳过存活探测，直接使用所有IP
		gologger.Info().Msgf("已禁用存活探测，将对所有 %d 个IP进行端口扫描", len(ipTargets))
		aliveIPs = ipTargets
	} else {
		// 执行IP存活探测
		gologger.Info().Msgf("开始对 %d 个IP进行存活探测", len(ipTargets))
		aliveIPs = e.scanAliveIPs(ipTargets)
		gologger.Info().Msgf("IP存活探测完成，发现 %d 个存活IP", len(aliveIPs))
	}

	return aliveIPs
}

// 扫描存活IP
func (e *Engine) scanAliveIPs(ipTargets []string) []string {
	return portscan.CheckLive(ipTargets, e.Options.UsePing, e.Options.Log)
}

// 扫描端口
func (e *Engine) scanPorts(aliveIPs []string) []portscan.PortResult {
	// 获取报告路径
	reportPath := e.Options.Html

	return portscan.ScanAndOutputResult(
		aliveIPs,
		e.Options.PortRange,
		e.Options.ExcludePort,
		e.Options.TCPThreads,
		e.Options.TCPTimeout,
		e.Options.Proxy,
		e.Options.Log,
		e.Options.RandomPort,
		e.Options.PortsThreshold,
		reportPath,
		e.Options.ServiceCheck,
	)
}

// 处理单个目标
func (e *Engine) processSingleTarget() {
	// 解析目标，支持逗号分隔和范围格式
	targets := parseTargets(e.Options.Target)

	if len(targets) == 1 {
		// 单个目标，使用原有逻辑
		e.processSingleTargetItem(targets[0])
	} else {
		// 多个目标，进行批量处理
		e.processMultipleTargets(targets)
	}
}

// 解析目标字符串，支持逗号分隔和范围格式
func parseTargets(target string) []string {
	var targets []string

	// 检查是否包含逗号分隔
	if strings.Contains(target, ",") {
		// 逗号分隔的目标
		parts := strings.Split(target, ",")
		for _, part := range parts {
			part = strings.TrimSpace(part)
			if part != "" {
				// 递归解析每个部分，支持范围格式
				subTargets := parseTargets(part)
				targets = append(targets, subTargets...)
			}
		}
		return targets
	}

	// 检查是否为IP范围格式 (***********-3)
	if strings.Contains(target, "-") && !strings.Contains(target, "/") {
		// 可能是IP范围格式
		if expandedTargets := expandIPRange(target); len(expandedTargets) > 0 {
			return expandedTargets
		}
	}

	// 单个目标
	return []string{target}
}

// 展开IP范围格式 (***********-3 -> ***********, ***********, ***********)
func expandIPRange(target string) []string {
	var targets []string

	// 检查IPv4范围格式: ***********-3
	if matched, _ := regexp.MatchString(`^\d+\.\d+\.\d+\.\d+-\d+$`, target); matched {
		parts := strings.Split(target, "-")
		if len(parts) == 2 {
			baseIP := parts[0]
			endNum, err := strconv.Atoi(parts[1])
			if err != nil {
				return []string{target} // 解析失败，返回原始目标
			}

			// 提取基础IP的最后一段
			ipParts := strings.Split(baseIP, ".")
			if len(ipParts) == 4 {
				startNum, err := strconv.Atoi(ipParts[3])
				if err != nil {
					return []string{target}
				}

				basePrefix := strings.Join(ipParts[:3], ".")

				// 生成范围内的所有IP
				for i := startNum; i <= endNum; i++ {
					if i >= 1 && i <= 254 { // 有效的主机IP范围
						targets = append(targets, fmt.Sprintf("%s.%d", basePrefix, i))
					}
				}
				return targets
			}
		}
	}

	// 检查IPv6范围格式: 2001:db8::1-3 或 2001:4860:4860::8888-8889
	if strings.Contains(target, ":") && strings.Contains(target, "-") {
		parts := strings.Split(target, "-")
		if len(parts) == 2 {
			baseIP := parts[0]
			endStr := parts[1]

			// 处理 IPv6 地址范围
			if strings.Contains(baseIP, "::") {
				// 分割基础IP和最后一段
				ipParts := strings.Split(baseIP, "::")
				if len(ipParts) == 2 {
					basePrefix := ipParts[0]
					lastPart := ipParts[1]

					// 如果最后一段为空，默认从1开始
					if lastPart == "" {
						lastPart = "1"
					}

					// 解析起始数字（支持十六进制）
					var startNum int64
					var err error
					if strings.Contains(lastPart, "x") {
						// 十六进制格式
						startNum, err = strconv.ParseInt(strings.TrimPrefix(lastPart, "0x"), 16, 64)
					} else {
						// 十进制或十六进制
						startNum, err = strconv.ParseInt(lastPart, 16, 64)
						if err != nil {
							// 尝试十进制
							startNum, err = strconv.ParseInt(lastPart, 10, 64)
						}
					}

					if err != nil {
						return []string{target}
					}

					// 解析结束数字（支持十六进制）
					var endNum int64
					if strings.Contains(endStr, "x") {
						endNum, err = strconv.ParseInt(strings.TrimPrefix(endStr, "0x"), 16, 64)
					} else {
						endNum, err = strconv.ParseInt(endStr, 16, 64)
						if err != nil {
							// 尝试十进制
							endNum, err = strconv.ParseInt(endStr, 10, 64)
						}
					}

					if err != nil {
						return []string{target}
					}

					// 生成范围内的所有IPv6地址
					for i := startNum; i <= endNum; i++ {
						if basePrefix != "" {
							targets = append(targets, fmt.Sprintf("%s::%x", basePrefix, i))
						} else {
							targets = append(targets, fmt.Sprintf("::%x", i))
						}
					}
					return targets
				}
			}
		}
	}

	return []string{target} // 不是范围格式，返回原始目标
}

// 处理单个目标项
func (e *Engine) processSingleTargetItem(target string) {
	if IsIPWithPort(target) {
		// IP:端口 格式，从端口扫描开始
		options := *e.Options
		options.Target = target
		ProcessIPWithPort(&options)
	} else if netutil.IsIP(target) || netutil.IsIP(netutil.CleanIPv6(target)) {
		// 纯IP地址（IPv4或IPv6，包括带接口标识符的IPv6），执行完整流程：IP存活 -> 端口扫描 -> Web扫描
		options := *e.Options
		options.Target = target
		ProcessIP(&options)
	} else if netutil.IsIPRange(target) {
		// IP段（IPv4或IPv6），执行IP段扫描流程
		options := *e.Options
		options.Target = target
		ProcessIPRange(&options)
	} else if netutil.IsDomain(target) {
		// 域名处理
		options := *e.Options
		options.Target = target
		if e.Options.DomainResolve {
			// 只进行域名解析，不进行端口扫描
			ProcessDomainResolveOnly(&options)
		} else {
			// 执行完整的域名解析和端口扫描
			ProcessDomain(&options)
		}
	} else if utils.IsURL(target) {
		// URL，直接进行Web扫描
		options := *e.Options
		options.Target = target
		ProcessURL(&options)
	} else {
		gologger.Error().Msgf("不支持的目标格式: %s", target)
	}
}

// 处理多个目标
func (e *Engine) processMultipleTargets(targets []string) {
	gologger.Info().Msgf("检测到多个目标 (%d个)，开始批量处理", len(targets))

	// 分类目标
	var ipTargets []string
	var ipPortTargets []string
	var domainTargets []string
	var urlTargets []string
	var ipRangeTargets []string

	for _, target := range targets {
		if IsIPWithPort(target) {
			ipPortTargets = append(ipPortTargets, target)
		} else if netutil.IsIP(target) || netutil.IsIP(netutil.CleanIPv6(target)) {
			ipTargets = append(ipTargets, target)
		} else if netutil.IsIPRange(target) {
			ipRangeTargets = append(ipRangeTargets, target)
		} else if netutil.IsDomain(target) {
			domainTargets = append(domainTargets, target)
		} else if utils.IsURL(target) {
			urlTargets = append(urlTargets, target)
		} else {
			gologger.Warning().Msgf("跳过不支持的目标格式: %s", target)
		}
	}

	// 批量处理IP目标
	if len(ipTargets) > 0 {
		gologger.Info().Msgf("处理 %d 个IP目标", len(ipTargets))
		e.processMultipleIPs(ipTargets)
	}

	// 处理其他类型的目标
	for _, target := range ipPortTargets {
		options := *e.Options
		options.Target = target
		ProcessIPWithPort(&options)
	}

	for _, target := range ipRangeTargets {
		options := *e.Options
		options.Target = target
		ProcessIPRange(&options)
	}

	for _, target := range domainTargets {
		options := *e.Options
		options.Target = target
		if e.Options.DomainResolve {
			ProcessDomainResolveOnly(&options)
		} else {
			ProcessDomain(&options)
		}
	}

	for _, target := range urlTargets {
		options := *e.Options
		options.Target = target
		ProcessURL(&options)
	}
}

// 批量处理多个IP目标
func (e *Engine) processMultipleIPs(ips []string) {
	gologger.Info().Msgf("开始批量扫描 %d 个IP目标", len(ips))

	// 如果启用了存活探测，先进行存活检测
	var aliveIPs []string
	if e.Options.UsePing {
		gologger.Info().Msg("开始存活探测...")
		aliveIPs = portscan.CheckLive(ips, false, e.Options.Log)
		gologger.Info().Msgf("存活探测完成，发现 %d 个存活主机", len(aliveIPs))
	} else {
		aliveIPs = ips
		gologger.Info().Msg("跳过存活探测")
	}

	if len(aliveIPs) == 0 {
		gologger.Warning().Msg("没有发现存活主机")
		return
	}

	// 执行端口扫描
	gologger.Info().Msg("开始端口扫描...")
	results := portscan.ScanAndOutputResult(aliveIPs, e.Options.PortRange, e.Options.ExcludePort, e.Options.TCPThreads, e.Options.TCPTimeout, e.Options.Proxy, e.Options.Log, e.Options.RandomPort, e.Options.PortsThreshold, e.Options.Html, e.Options.ServiceCheck)

	if len(results) == 0 {
		gologger.Warning().Msg("未发现开放端口")
		return
	}

	// 如果不进行Web扫描，直接处理插件任务
	if !e.Options.WebScan {
		gologger.Info().Msg("Web扫描已禁用，跳过Web扫描阶段")

		// 执行插件系统处理端口扫描结果
		if !e.Options.NoPlugin {
			core.DispatchPluginTasks(results)
		} else {
			gologger.Info().Msg("插件系统已禁用，跳过插件任务执行")
		}
		return
	}

	// 收集Web服务目标
	var webTargets []string
	for _, result := range results {
		if result.Server == "http" || result.Server == "https" || result.Link != "" {
			webTargets = append(webTargets, result.Link)
		}
	}

	// 执行Web扫描
	if len(webTargets) > 0 {
		gologger.Info().Msgf("已解析 %d 个Web目标，准备进行Web扫描", len(webTargets))
		RunWebScan(webTargets, e.Options)
	}

	// Web扫描完成后，执行插件系统处理端口扫描结果
	if !e.Options.NoPlugin {
		core.DispatchPluginTasks(results)
	} else {
		gologger.Info().Msg("插件系统已禁用，跳过插件任务执行")
	}
}

// 设置信号处理
func (e *Engine) setupSignalHandler() {
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)
	go func() {
		<-c
		fmt.Println("\r- 检测到Ctrl+C，正在退出...")
		portscan.ExitFunc = true

		// 停止工作池
		core.StopDefaultWorkerPool()

		// 清理资源
		e.Cleanup()

		os.Exit(0)
	}()
}
